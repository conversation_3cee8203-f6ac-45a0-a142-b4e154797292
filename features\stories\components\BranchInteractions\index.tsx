import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { MessageCircle, ThumbsUp, ChevronDown, ChevronUp } from 'lucide-react-native';
import { useBranchInteractions } from '../../hooks/useBranchInteractions';
import BranchVotes from '../BranchVotes';
import BranchComments from '../BranchComments';

interface BranchInteractionsProps {
  segmentId?: string;
}

export default function BranchInteractions({
  segmentId,
}: BranchInteractionsProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  // 状态
  const [activeTab, setActiveTab] = useState<'votes' | 'comments'>('votes');
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 使用分支交互Hook
  const {
    comments,
    voteStats,
    userVote,
    isLoadingComments,
    isLoadingVotes,
    isAddingComment,
    isVoting,
    addComment,
    deleteComment,
    vote,
    refreshComments,
    refreshVotes,
  } = useBranchInteractions({ segmentId });

  // 切换展开/折叠
  const toggleExpanded = () => {
    setIsExpanded(prev => !prev);
  };

  // 渲染标签页
  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'votes' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('votes')}
        >
          <ThumbsUp
            size={16}
            color={
              activeTab === 'votes'
                ? theme.colors.primary
                : theme.colors.text
            }
          />
          <Text
            style={[
              styles.tabText,
              activeTab === 'votes' && styles.activeTabText,
            ]}
          >
            {t('storyDetail.votes', 'Votes')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'comments' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('comments')}
        >
          <MessageCircle
            size={16}
            color={
              activeTab === 'comments'
                ? theme.colors.primary
                : theme.colors.text
            }
          />
          <Text
            style={[
              styles.tabText,
              activeTab === 'comments' && styles.activeTabText,
            ]}
          >
            {t('storyDetail.comments', 'Comments')} ({comments.length})
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (activeTab === 'votes') {
      return (
        <BranchVotes
          voteStats={voteStats}
          userVote={userVote}
          isLoading={isLoadingVotes}
          isVoting={isVoting}
          onVote={vote}
        />
      );
    } else {
      return (
        <BranchComments
          comments={comments}
          isLoading={isLoadingComments}
          isAddingComment={isAddingComment}
          onAddComment={addComment}
          onDeleteComment={deleteComment}
          onRefresh={refreshComments}
        />
      );
    }
  };

  if (!segmentId) {
    return null;
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={toggleExpanded}
        activeOpacity={0.7}
      >
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>
            {t('storyDetail.branchInteractions', 'Branch Interactions')}
          </Text>
        </View>
        
        {isExpanded ? (
          <ChevronUp size={24} color={theme.colors.text} />
        ) : (
          <ChevronDown size={24} color={theme.colors.text} />
        )}
      </TouchableOpacity>
      
      {isExpanded && (
        <View style={styles.content}>
          {renderTabs()}
          {renderContent()}
        </View>
      )}
    </View>
  );
}
