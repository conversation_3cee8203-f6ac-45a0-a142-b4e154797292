import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSettingsStore, ThemeMode } from '@/lib/store/settingsStore';
import { OptionsGroup, Option } from '@/components/shared/OptionsGroup'; // Import shared component

export function ThemeOptionsGroup() {
  const { t } = useTranslation();
  const themeMode = useSettingsStore((state) => state.themeMode);
  const setThemeMode = useSettingsStore((state) => state.setThemeMode);

  // Define theme options conforming to the Option interface
  const themeOptions: Option<ThemeMode>[] = [
    { value: 'light', label: t('light') },
    { value: 'dark', label: t('dark') },
    { value: 'system', label: t('system') },
  ];

  return (
    <OptionsGroup<ThemeMode>
      options={themeOptions}
      selectedValue={themeMode}
      onSelect={setThemeMode}
    />
  );
} 