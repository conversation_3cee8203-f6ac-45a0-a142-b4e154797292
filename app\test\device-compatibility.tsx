import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/StoryTabs';
import { StoryList } from '@/features/stories/components/StoryList';
import { useStoriesScreenTest } from '@/features/stories/hooks/useStoriesScreenTest';

/**
 * Test screen for device compatibility testing
 * This screen simulates different device sizes and orientations
 */
export default function DeviceCompatibilityTest() {
  const theme = useAppTheme();
  const { t } = useTranslation();
  const router = useRouter();
  
  // Get actual device dimensions
  const actualWidth = Dimensions.get('window').width;
  const actualHeight = Dimensions.get('window').height;
  
  // Device simulation options
  const deviceSizes = [
    { name: 'Actual Device', width: actualWidth, height: actualHeight },
    { name: 'iPhone SE', width: 375, height: 667 },
    { name: 'iPhone X/11/12/13', width: 390, height: 844 },
    { name: 'iPhone Pro Max', width: 428, height: 926 },
    { name: 'Small Android', width: 360, height: 640 },
    { name: 'Medium Android', width: 393, height: 851 },
    { name: 'Large Android', width: 412, height: 915 },
    { name: 'iPad', width: 768, height: 1024 },
    { name: 'Android Tablet', width: 800, height: 1280 },
  ];
  
  // Orientation options
  const orientations = [
    { name: 'Portrait', isPortrait: true },
    { name: 'Landscape', isPortrait: false },
  ];
  
  // State for selected device and orientation
  const [selectedDevice, setSelectedDevice] = useState(deviceSizes[0]);
  const [selectedOrientation, setSelectedOrientation] = useState(orientations[0]);
  
  // Calculate container dimensions based on selected device and orientation
  const containerWidth = selectedOrientation.isPortrait ? selectedDevice.width : selectedDevice.height;
  const containerHeight = selectedOrientation.isPortrait ? selectedDevice.height : selectedDevice.width;
  
  // Scale factor to fit the device simulation in the screen
  const scaleFactor = Math.min(
    (actualWidth - 40) / containerWidth,
    (actualHeight - 200) / containerHeight,
    1 // Don't scale up, only down
  );
  
  // Use the stories screen test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
  } = useStoriesScreenTest();
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    optionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.md,
    },
    optionGroup: {
      flex: 1,
    },
    optionTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    optionButtonsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.xs,
    },
    optionButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: 4,
      borderWidth: 1,
      marginBottom: theme.spacing.xs,
    },
    optionButtonText: {
      fontSize: 12,
    },
    deviceContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    deviceSimulation: {
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderRadius: 16,
      overflow: 'hidden',
      backgroundColor: theme.colors.background,
    },
    deviceInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    deviceInfoText: {
      fontSize: 12,
      color: theme.colors.secondaryText,
    },
    backButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
  });
  
  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Device Compatibility Test</Text>
          
          <View style={styles.optionsContainer}>
            {/* Device selection */}
            <View style={styles.optionGroup}>
              <Text style={styles.optionTitle}>Device</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.optionButtonsContainer}>
                  {deviceSizes.map((device) => (
                    <TouchableOpacity
                      key={device.name}
                      style={[
                        styles.optionButton,
                        {
                          borderColor: selectedDevice.name === device.name ? theme.colors.primary : theme.colors.border,
                          backgroundColor: selectedDevice.name === device.name ? `${theme.colors.primary}20` : 'transparent',
                        },
                      ]}
                      onPress={() => setSelectedDevice(device)}
                    >
                      <Text
                        style={[
                          styles.optionButtonText,
                          {
                            color: selectedDevice.name === device.name ? theme.colors.primary : theme.colors.text,
                          },
                        ]}
                      >
                        {device.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
            
            {/* Orientation selection */}
            <View style={styles.optionGroup}>
              <Text style={styles.optionTitle}>Orientation</Text>
              <View style={styles.optionButtonsContainer}>
                {orientations.map((orientation) => (
                  <TouchableOpacity
                    key={orientation.name}
                    style={[
                      styles.optionButton,
                      {
                        borderColor: selectedOrientation.name === orientation.name ? theme.colors.primary : theme.colors.border,
                        backgroundColor: selectedOrientation.name === orientation.name ? `${theme.colors.primary}20` : 'transparent',
                      },
                    ]}
                    onPress={() => setSelectedOrientation(orientation)}
                  >
                    <Text
                      style={[
                        styles.optionButtonText,
                        {
                          color: selectedOrientation.name === orientation.name ? theme.colors.primary : theme.colors.text,
                        },
                      ]}
                    >
                      {orientation.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Back to App</Text>
          </TouchableOpacity>
        </View>
        
        {/* Device simulation */}
        <View style={styles.deviceContainer}>
          <View
            style={[
              styles.deviceSimulation,
              {
                width: containerWidth * scaleFactor,
                height: containerHeight * scaleFactor,
                transform: [{ scale: scaleFactor }],
                transformOrigin: 'top left',
              },
            ]}
          >
            <View style={styles.deviceInfo}>
              <Text style={styles.deviceInfoText}>
                {selectedDevice.name} - {selectedOrientation.name}
              </Text>
              <Text style={styles.deviceInfoText}>
                {containerWidth}x{containerHeight}
              </Text>
            </View>
            
            <View style={{ flex: 1, width: containerWidth, height: containerHeight - 30 }}>
              <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
              
              <View style={{ flex: 1 }}>
                <StoryList
                  stories={stories}
                  onStoryPress={handleStoryPress}
                  isLoading={isLoading}
                  isRefreshing={isRefreshing}
                  onRefresh={refreshStories}
                  onLoadMore={loadMoreStories}
                  hasMoreStories={hasMoreStories}
                  error={error}
                  onRetry={retryFetch}
                  retryCount={retryCount}
                />
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
