import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  cardContainer: {
    marginRight: theme.spacing.md,
    width: 150,
  },
  image: {
    width: 150,
    height: 100,
    borderRadius: theme.roundness,
    marginBottom: theme.spacing.sm,
    backgroundColor: theme.colors.border, // Placeholder background
  },
  title: {
    color: theme.colors.text,
    fontFamily: theme.fonts.medium,
    fontSize: 14,
    lineHeight: 18,
  },
}); 