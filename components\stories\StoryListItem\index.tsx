import React from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Story } from '@/types/story';
import { createStyles } from './styles';
import { StoryListItemHeader } from './StoryListItemHeader';
import { StoryListItemStats } from './StoryListItemStats';
import { StoryListItemTags } from './StoryListItemTags';

interface StoryListItemProps {
  story: Story;
  onOptionsPress?: () => void;
}

export default function StoryListItem({
  story,
  onOptionsPress,
}: StoryListItemProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);
  const router = useRouter();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (e) {
      return 'Invalid Date';
    }
  };

  return (
    <TouchableOpacity
      onPress={() => router.push(`/stories/${story.id}`)}
      style={[
        styles.container,
        {
          backgroundColor: appTheme.colors.card,
          borderColor: appTheme.colors.border,
        },
        appTheme.shadows.sm,
      ]}
    >
      <Image source={{ uri: story.coverImage }} style={styles.coverImage} />

      <View style={styles.content}>
        <StoryListItemHeader
          title={story.title}
          onOptionsPress={onOptionsPress}
        />

        <StoryListItemStats
          views={story.views}
          likes={story.likes}
          updatedAt={story.updatedAt}
          formatDate={formatDate}
        />

        <StoryListItemTags
          themes={story.theme}
          isCompleted={story.isCompleted}
        />
      </View>
    </TouchableOpacity>
  );
}
