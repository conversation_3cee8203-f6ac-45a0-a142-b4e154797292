import React from 'react';
import { Card } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Story } from '@/types/story';
import { StoryListItemHeader } from './StoryListItemHeader';
import { StoryListItemStats } from './StoryListItemStats';
import { StoryListItemTags } from './StoryListItemTags';

interface StoryListItemProps {
  story: Story;
  onOptionsPress?: () => void;
}

export default function StoryListItem({
  story,
  onOptionsPress,
}: StoryListItemProps) {
  const theme = usePaperTheme();
  const router = useRouter();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (e) {
      return 'Invalid Date';
    }
  };

  return (
    <Card
      mode="elevated"
      onPress={() => router.push(`/stories/${story.id}`)}
      style={{
        margin: theme.spacing?.sm || 8,
      }}
    >
      <Card.Cover source={{ uri: story.coverImage }} style={{ height: 120 }} />

      <Card.Content style={{ paddingTop: theme.spacing?.sm || 8 }}>
        <StoryListItemHeader
          title={story.title}
          onOptionsPress={onOptionsPress}
        />

        <StoryListItemStats
          views={story.views}
          likes={story.likes}
          updatedAt={story.updatedAt}
          formatDate={formatDate}
        />

        <StoryListItemTags
          themes={story.theme}
          isCompleted={story.isCompleted}
        />
      </Card.Content>
    </Card>
  );
}
