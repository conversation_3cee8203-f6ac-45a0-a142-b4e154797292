import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    nodeContainer: {
      marginLeft: theme.spacing.sm, // Indent children slightly
      borderLeftWidth: 1,
      borderLeftColor: theme.colors.border,
      paddingLeft: theme.spacing.sm,
      marginTop: theme.spacing.sm, // Spacing between sibling nodes at the same level
    },
    partContainer: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    partContent: {
      fontSize: 16,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      lineHeight: 24,
      marginBottom: theme.spacing.sm,
    },
    partAuthor: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondary,
      textAlign: 'right',
      marginBottom: theme.spacing.md,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end', // Align buttons to the right
      marginTop: theme.spacing.xs,
      borderTopWidth: StyleSheet.hairlineWidth,
      borderTopColor: theme.colors.border,
      paddingTop: theme.spacing.sm,
    },
    actionButton: {
      marginLeft: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
    },
    actionText: {
      color: theme.colors.primary,
      fontFamily: theme.fonts.medium,
    },
    childrenContainer: {
      marginTop: theme.spacing.xs, // Space between parent and children block
    },
    continuationInputContainer: {
      marginTop: theme.spacing.md,
      paddingTop: theme.spacing.md,
      borderTopWidth: StyleSheet.hairlineWidth,
      borderTopColor: theme.colors.border,
    },
    continuationInput: {
      backgroundColor: theme.colors.inputBackground || theme.colors.background, // Slightly different background
      color: theme.colors.text,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.roundness,
      fontSize: 15,
      borderWidth: 1,
      borderColor: theme.colors.border,
      minHeight: 80,
      textAlignVertical: 'top',
      marginBottom: theme.spacing.sm,
    },
    submitButton: {
      alignSelf: 'flex-end',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.roundness,
    },
  });
