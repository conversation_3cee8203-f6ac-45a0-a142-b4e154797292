import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './RegisterScreen.styles';
import { signUpWithEmail } from '@/api/auth';
import { useAuthStore } from '@/lib/store/authStore';
import { Link, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

export default function RegisterScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSignup = async () => {
    if (!username || !email || !password) {
      Alert.alert(t('registrationErrorTitle'), t('fillAllFields'));
      return;
    }

    setLoading(true);
    const { data, error } = await signUpWithEmail(email, password, username);
    setLoading(false);

    if (error) {
      Alert.alert(t('registrationErrorTitle'), error.message);
    } else {
      Alert.alert(
        t('registrationSuccessTitle'),
        t('registrationVerificationSentMessage')
      );
      router.replace('/(auth)/login');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('registerTitle')}</Text>

      <TextInput
        style={styles.input}
        placeholder={t('usernamePlaceholder')}
        placeholderTextColor={theme.colors.placeholder}
        autoCapitalize="none"
        value={username}
        onChangeText={setUsername}
      />

      <TextInput
        style={styles.input}
        placeholder={t('emailPlaceholder')}
        placeholderTextColor={theme.colors.placeholder}
        keyboardType="email-address"
        autoCapitalize="none"
        value={email}
        onChangeText={setEmail}
      />

      <TextInput
        style={styles.input}
        placeholder={t('passwordPlaceholder')}
        placeholderTextColor={theme.colors.placeholder}
        secureTextEntry
        value={password}
        onChangeText={setPassword}
      />

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleSignup}
        disabled={loading}
      >
        <Text style={styles.buttonText}>
          {loading ? t('registering') : t('registerButton')}
        </Text>
      </TouchableOpacity>

      <View style={styles.signinContainer}>
        <Text style={styles.signinText}>{t('alreadyHaveAccountText')}</Text>
        <Link href="/(auth)/login" style={styles.signinLink}>
          {t('signinLinkText')}
        </Link>
      </View>
    </View>
  );
}
 