import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginHorizontal: theme.spacing.sm,
      marginVertical: theme.spacing.md,
      overflow: 'hidden',
      elevation: 3,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      maxHeight: 400, // 限制最大高度，避免占据太多屏幕空间
    },
    controlBar: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    filterContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    filterButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      borderRadius: 16, // 更圆润的按钮
      marginRight: theme.spacing.sm,
    },
    activeFilterButton: {
      backgroundColor: theme.colors.primary,
      borderWidth: 0,
    },
    filterButtonText: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
    },
    activeFilterButtonText: {
      color: theme.colors.onPrimary, // 白色文字在紫色背景上
      fontFamily: theme.fonts.bold,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusText: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      marginRight: theme.spacing.md,
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    carouselContent: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.md,
      backgroundColor: theme.colors.background,
    },
    loadingContainer: {
      padding: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.background,
      minHeight: 200,
    },
    emptyContainer: {
      padding: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.background,
      minHeight: 200,
    },
    emptyText: {
      fontSize: 16,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      marginTop: theme.spacing.sm,
      textAlign: 'center',
    },
    footerContainer: {
      padding: theme.spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.background,
    },
    viewAllButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 20, // 更圆润的按钮
      backgroundColor: theme.colors.primary,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    viewAllButtonText: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.onPrimary,
    },
  });
