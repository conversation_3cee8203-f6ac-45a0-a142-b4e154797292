import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createAISuggestionBlockStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    aiSectionContainer: {
      marginTop: theme.spacing.lg,
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.background, // Match screen background or use a subtle card color
      borderRadius: theme.radius.md,
    },
    aiButton: {
      // Using the general button style as a base
      backgroundColor: theme.colors.accent2, // Distinct color for AI actions
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
      marginBottom: theme.spacing.md, // Space before suggestions list
    },
    buttonText: {
      color: theme.colors.onPrimary, // Assuming accent2 is dark enough for onPrimary to work, else define a specific color
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
    },
    aiLoadingIndicator: {
      marginVertical: theme.spacing.md, // Give it some space when loading
      alignSelf: 'center',
    },
    aiSuggestionsListContainer: {
      marginTop: theme.spacing.sm,
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.surface, // Suggestions on a surface card
      borderRadius: theme.radius.md,
    },
    suggestionsTitle: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      marginLeft: theme.spacing.xs, // Align with cards
    },
    noSuggestionsText: {
      textAlign: 'center',
      color: theme.colors.secondaryText,
      marginVertical: theme.spacing.md,
      fontFamily: theme.fonts.regular,
      fontStyle: 'italic',
    },
    // AiSuggestionCard will have its own styles, this container doesn't need to style it directly
  });
};
