import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createDynamicStyles } from '../screens/StoryDetailScreen.styles';

export function StoryDetailEmpty() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);
  
  return (
    <View style={styles.centered}>
      <Text style={styles.errorText}>
        {t('storyDetail.errors.notFound', 'Story not found.')}
      </Text>
    </View>
  );
}
