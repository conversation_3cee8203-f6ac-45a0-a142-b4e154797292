import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Animated,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { X, GitBranch, ChevronRight } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import BranchPreviewCard from './BranchPreviewCard';
import { StorySegment } from '@/api/stories/types';

export interface BranchCarouselProps {
  segmentId: string;
  branches: StorySegment[];
  totalBranches: number;
  isLoading: boolean;
  currentPage: number;
  hasMorePages: boolean;
  onBranchSelect: (branchId: string) => void;
  onClose: () => void;
  onLoadMore: () => void;
  onFilterChange: (filter: 'popular' | 'sameAuthor' | 'recommended') => void;
}

export default function BranchCarousel({
  segmentId,
  branches,
  totalBranches,
  isLoading,
  currentPage,
  hasMorePages,
  onBranchSelect,
  onClose,
  onLoadMore,
  onFilterChange,
}: BranchCarouselProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // State for tracking the current visible branch index
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeFilter, setActiveFilter] = useState<
    'popular' | 'sameAuthor' | 'recommended'
  >('popular');

  // Ref for the FlatList
  const flatListRef = useRef<FlatList>(null);

  // Animation for the carousel appearance
  const slideAnim = useRef(new Animated.Value(0)).current;

  // Screen dimensions
  const { width } = Dimensions.get('window');

  // Start animation when component mounts
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  // Handle filter change
  const handleFilterChange = (
    filter: 'popular' | 'sameAuthor' | 'recommended'
  ) => {
    setActiveFilter(filter);
    setCurrentIndex(0);
    onFilterChange(filter);
  };

  // Handle scroll end to update current index
  const handleScrollEnd = (e: any) => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / (width * 0.8)); // Assuming each card takes 80% of screen width
    setCurrentIndex(index);
  };

  // Handle branch selection
  const handleBranchSelect = (branchId: string) => {
    // Close carousel with animation
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      onBranchSelect(branchId);
    });
  };

  // Handle close
  const handleClose = () => {
    // Close carousel with animation
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(onClose);
  };

  // Handle end reached to load more branches
  const handleEndReached = () => {
    if (!isLoading && hasMorePages) {
      onLoadMore();
    }
  };

  // Render the filter button
  const renderFilterButton = (
    label: string,
    filter: 'popular' | 'sameAuthor' | 'recommended'
  ) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        activeFilter === filter && styles.activeFilterButton,
      ]}
      onPress={() => handleFilterChange(filter)}
    >
      <Text
        style={[
          styles.filterButtonText,
          activeFilter === filter && styles.activeFilterButtonText,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  // Render the branch preview card
  const renderBranchItem = ({
    item,
    index,
  }: {
    item: StorySegment;
    index: number;
  }) => (
    <BranchPreviewCard
      branch={item}
      onPress={() => handleBranchSelect(item.id)}
      isActive={index === currentIndex}
    />
  );

  // Render footer with "View All" link if needed
  const renderFooter = () => {
    if (isLoading) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
        </View>
      );
    }

    if (totalBranches > branches.length && hasMorePages) {
      return (
        <View style={styles.footerContainer}>
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={() => {
              // 加载更多分支
              onLoadMore();
            }}
          >
            <Text style={styles.viewAllButtonText}>
              {t('storyDetail.viewAllBranches', '查看全部 {{count}} 个分支', {
                count: totalBranches,
              })}
            </Text>
            <ChevronRight
              size={16}
              color={theme.colors.onPrimary}
              style={{ marginLeft: 4 }}
            />
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: slideAnim,
          transform: [
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [50, 0],
              }),
            },
          ],
        },
      ]}
    >
      {/* Top Control Bar */}
      <View style={styles.controlBar}>
        {/* Left - Filter Tabs */}
        <View style={styles.filterContainer}>
          {renderFilterButton(t('storyDetail.popular', '最火'), 'popular')}
          {renderFilterButton(
            t('storyDetail.sameAuthor', '同一作者'),
            'sameAuthor'
          )}
          {renderFilterButton(
            t('storyDetail.recommended', '推荐'),
            'recommended'
          )}
        </View>

        {/* Right - Status and Close */}
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>
            {branches.length > 0
              ? `${currentIndex + 1} / ${totalBranches}`
              : `0 / 0`}
          </Text>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <X size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Branch Preview Cards */}
      {isLoading && branches.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : branches.length > 0 ? (
        <FlatList
          ref={flatListRef}
          data={branches}
          renderItem={renderBranchItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.carouselContent}
          snapToInterval={width * 0.8} // Snap to card width (80% of screen)
          snapToAlignment="center"
          decelerationRate="fast"
          onMomentumScrollEnd={handleScrollEnd}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          initialNumToRender={3}
          maxToRenderPerBatch={3}
          windowSize={5}
          removeClippedSubviews={true}
          ListFooterComponent={renderFooter}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <GitBranch size={24} color={theme.colors.secondaryText} />
          <Text style={styles.emptyText}>
            {t('storyDetail.noBranches', '暂无分支，点击 + 按钮创建第一个分支')}
          </Text>
        </View>
      )}
    </Animated.View>
  );
}
