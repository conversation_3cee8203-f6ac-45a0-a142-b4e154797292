import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.md,
      padding: theme.spacing.md,
      elevation: 1,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    originStoryContainer: {
      borderColor: theme.colors.warning,
      borderWidth: 2,
    },
    creatorInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
      height: 24, // Fixed height as per PRD
    },
    creatorInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    avatar: {
      width: 20,
      height: 20,
      borderRadius: 10,
      marginRight: theme.spacing.xs,
    },
    username: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginRight: theme.spacing.xs,
    },
    handle: {
      fontSize: 12,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginRight: theme.spacing.xs,
    },
    timeAgo: {
      fontSize: 12,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
    },
    moreButton: {
      padding: theme.spacing.xs,
    },
    content: {
      fontSize: 16,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      lineHeight: 24,
      marginBottom: theme.spacing.md,
    },
    interactionRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: theme.spacing.sm,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    interactionIcons: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    iconButton: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    iconText: {
      fontSize: 12,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginLeft: theme.spacing.xs,
    },
    branchIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: 12, // Pill shape
      minWidth: 40, // Ensure minimum width for better touch target
      marginLeft: 'auto', // Push to the right
    },
    originBranchIndicator: {
      backgroundColor: theme.colors.warning,
    },
    branchCount: {
      fontSize: 12,
      fontFamily: theme.fonts.medium,
      color: theme.colors.background,
      marginLeft: theme.spacing.xs,
    },
    originBranchCount: {
      color: theme.colors.background,
    },
    branchPlaceholder: {
      width: 40, // Approximate width of branch indicator
    },
  });
