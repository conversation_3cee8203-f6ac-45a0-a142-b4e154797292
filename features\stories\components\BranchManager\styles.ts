import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden',
      marginVertical: theme.spacing.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: theme.fontSizes.lg,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginLeft: theme.spacing.sm,
    },
    headerControls: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    createBranchButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.radius.md,
      marginRight: theme.spacing.md,
    },
    createBranchButtonText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.onPrimary,
      marginLeft: theme.spacing.xs,
    },
    content: {
      padding: theme.spacing.md,
    },
    visualizationStyleSelector: {
      marginBottom: theme.spacing.md,
    },
    visualizationStyleLabel: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    visualizationStyleButtons: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    visualizationStyleButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.radius.sm,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginRight: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    visualizationStyleButtonActive: {
      backgroundColor: theme.colors.primaryLight,
      borderColor: theme.colors.primary,
    },
    visualizationStyleButtonText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    visualizationStyleButtonTextActive: {
      color: theme.colors.primary,
      fontFamily: theme.fonts.bold,
    },
    loadingContainer: {
      padding: theme.spacing.lg,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
    errorContainer: {
      padding: theme.spacing.lg,
      justifyContent: 'center',
      alignItems: 'center',
    },
    errorText: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.radius.md,
    },
    retryButtonText: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.onPrimary,
    },
  });
};
