-- Create a function to get top authors based on story likes
CREATE OR R<PERSON>LACE FUNCTION get_top_authors(
  from_date timestamp with time zone,
  limit_param integer,
  offset_param integer
) RETURNS TABLE (
  author_id uuid,
  total_likes bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.author_id,
    COUNT(sl.story_id) AS total_likes
  FROM 
    stories s
  LEFT JOIN 
    story_likes sl ON s.id = sl.story_id
  WHERE 
    s.created_at >= from_date
    AND s.status = 'published'
    AND s.visibility = 'public'
  GROUP BY 
    s.author_id
  ORDER BY 
    total_likes DESC
  LIMIT 
    limit_param
  OFFSET 
    offset_param;
END;
$$ LANGUAGE plpgsql;