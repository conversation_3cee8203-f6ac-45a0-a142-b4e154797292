import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface SectionHeaderProps {
  title: string;
  showSeeMore?: boolean;
  onSeeMorePress?: () => void;
}

export function SectionHeader({ 
  title, 
  showSeeMore = false,
  onSeeMorePress
}: SectionHeaderProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.sectionHeader}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        {title}
      </Text>
      {showSeeMore && onSeeMorePress && (
        <TouchableOpacity onPress={onSeeMorePress}>
          <Text style={[styles.seeMoreText, { color: theme.colors.primary }]}>
            {t('social.search.seeMore', '查看更多')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
