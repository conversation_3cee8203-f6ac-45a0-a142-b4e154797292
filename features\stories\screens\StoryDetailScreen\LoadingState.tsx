import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createDynamicStyles } from '../StoryDetailScreen.styles';

export function LoadingState() {
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);

  return (
    <View style={styles.centered}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
  );
}
