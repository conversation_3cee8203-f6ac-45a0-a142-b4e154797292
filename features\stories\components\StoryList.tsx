import React, { useCallback, useRef } from 'react';
import { FlatList, View, RefreshControl, Animated, Easing } from 'react-native';
import { ActivityIndicator, Text, Button } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import StoryListItem, { Story } from '@/components/stories/StoryListItem';
import { useTranslation } from 'react-i18next';

interface StoryListProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  isLoading?: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMoreStories?: boolean;
  error?: string | null;
  onRetry?: () => void;
  retryCount?: number;
}

export function StoryList({
  stories,
  onStoryPress,
  isLoading = false,
  isRefreshing = false,
  onRefresh,
  onLoadMore,
  hasMoreStories = false,
  error = null,
  onRetry,
  retryCount = 0,
}: StoryListProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  // Animation for list items
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Animation for retry button
  const spinValue = useRef(new Animated.Value(0)).current;
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Start animations when component mounts or stories change
  React.useEffect(() => {
    // Fade in animation for list items
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
      easing: Easing.out(Easing.cubic),
    }).start();

    // Reset spin animation
    spinValue.setValue(0);
  }, [stories, fadeAnim, spinValue]);

  // Animation for retry button
  const startSpinAnimation = useCallback(() => {
    spinValue.setValue(0);
    Animated.timing(spinValue, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
      easing: Easing.linear,
    }).start();
  }, [spinValue]);

  // Handle retry button press
  const handleRetry = useCallback(() => {
    if (onRetry) {
      startSpinAnimation();
      onRetry();
    }
  }, [onRetry, startSpinAnimation]);

  // Render footer with loading indicator or "no more stories" message
  const renderFooter = () => {
    if (isLoading && stories.length > 0) {
      return (
        <View
          style={{
            padding: theme.spacing?.md || 16,
            alignItems: 'center',
          }}
        >
          <ActivityIndicator size="small" />
        </View>
      );
    }

    if (!hasMoreStories && stories.length > 0) {
      return (
        <View
          style={{
            padding: theme.spacing?.md || 16,
            alignItems: 'center',
          }}
        >
          <Text
            variant="bodyMedium"
            style={{ color: theme.colors.onSurfaceVariant }}
          >
            {t('storyList.noMoreStories', 'No more stories')}
          </Text>
        </View>
      );
    }

    return null;
  };

  // Render item with animation
  const renderItem = useCallback(
    ({ item, index }) => {
      // Calculate staggered delay based on index
      const delay = index * 100;

      return (
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [50, 0],
                }),
              },
            ],
          }}
        >
          <StoryListItem key={item.id} story={item} onPress={onStoryPress} />
        </Animated.View>
      );
    },
    [fadeAnim, onStoryPress]
  );

  // Render error message if there's an error
  if (error) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: theme.spacing?.lg || 24,
        }}
      >
        <Text
          variant="bodyLarge"
          style={{
            color: theme.colors.error,
            textAlign: 'center',
            marginBottom: theme.spacing?.lg || 24,
          }}
        >
          {error}
        </Text>

        {onRetry && (
          <Button
            mode="outlined"
            icon="refresh"
            onPress={handleRetry}
            loading={isLoading}
            style={{ marginTop: theme.spacing?.md || 16 }}
          >
            {t('common.retry', 'Retry')}
            {retryCount > 0 ? ` (${retryCount}/3)` : ''}
          </Button>
        )}
      </View>
    );
  }

  return (
    <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
      <FlatList
        data={stories}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: theme.spacing?.sm || 8 }}
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          ) : undefined
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews={true}
        // Add loading skeleton when loading initial data
        ListEmptyComponent={
          isLoading && !error ? (
            <View style={{ padding: theme.spacing?.md || 16 }}>
              {[...Array(5)].map((_, index) => (
                <Animated.View
                  key={index}
                  style={[
                    {
                      height: 120,
                      backgroundColor: theme.colors.surfaceVariant,
                      borderRadius: theme.roundness || 12,
                      marginBottom: theme.spacing?.sm || 8,
                    },
                    {
                      opacity: fadeAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, 0.8],
                      }),
                    },
                  ]}
                />
              ))}
            </View>
          ) : null
        }
      />
    </Animated.View>
  );
}
