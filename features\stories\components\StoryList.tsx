import React, { useCallback, useRef } from 'react';
import {
  FlatList,
  ActivityIndicator,
  Text,
  View,
  RefreshControl,
  TouchableOpacity,
  Animated,
  Easing,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './StoryList.styles';
import StoryListItem, { Story } from '@/components/stories/StoryListItem';
import { useTranslation } from 'react-i18next';
import { RefreshCw } from 'lucide-react-native';

interface StoryListProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  isLoading?: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMoreStories?: boolean;
  error?: string | null;
  onRetry?: () => void;
  retryCount?: number;
}

export function StoryList({
  stories,
  onStoryPress,
  isLoading = false,
  isRefreshing = false,
  onRefresh,
  onLoadMore,
  hasMoreStories = false,
  error = null,
  onRetry,
  retryCount = 0,
}: StoryListProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  // Animation for list items
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Animation for retry button
  const spinValue = useRef(new Animated.Value(0)).current;
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Start animations when component mounts or stories change
  React.useEffect(() => {
    // Fade in animation for list items
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
      easing: Easing.out(Easing.cubic),
    }).start();

    // Reset spin animation
    spinValue.setValue(0);
  }, [stories, fadeAnim, spinValue]);

  // Animation for retry button
  const startSpinAnimation = useCallback(() => {
    spinValue.setValue(0);
    Animated.timing(spinValue, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
      easing: Easing.linear,
    }).start();
  }, [spinValue]);

  // Handle retry button press
  const handleRetry = useCallback(() => {
    if (onRetry) {
      startSpinAnimation();
      onRetry();
    }
  }, [onRetry, startSpinAnimation]);

  // Render footer with loading indicator or "no more stories" message
  const renderFooter = () => {
    if (isLoading && stories.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
        </View>
      );
    }

    if (!hasMoreStories && stories.length > 0) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            {t('storyList.noMoreStories', 'No more stories')}
          </Text>
        </View>
      );
    }

    return null;
  };

  // Render item with animation
  const renderItem = useCallback(
    ({ item, index }) => {
      // Calculate staggered delay based on index
      const delay = index * 100;

      return (
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [50, 0],
                }),
              },
            ],
          }}
        >
          <StoryListItem key={item.id} story={item} onPress={onStoryPress} />
        </Animated.View>
      );
    },
    [fadeAnim, onStoryPress]
  );

  // Render error message if there's an error
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>

        {onRetry && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={handleRetry}
            disabled={isLoading}
          >
            <Animated.View style={{ transform: [{ rotate: spin }] }}>
              <RefreshCw size={20} color={theme.colors.onPrimary} />
            </Animated.View>
            <Text style={styles.retryButtonText}>
              {t('common.retry', 'Retry')}
              {retryCount > 0 ? ` (${retryCount}/3)` : ''}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
      <FlatList
        data={stories}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.contentContainer}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          ) : undefined
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews={true}
        // Add loading skeleton when loading initial data
        ListEmptyComponent={
          isLoading && !error ? (
            <View style={styles.loadingContainer}>
              {[...Array(5)].map((_, index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.skeletonItem,
                    {
                      opacity: fadeAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, 0.8],
                      }),
                    },
                  ]}
                />
              ))}
            </View>
          ) : null
        }
      />
    </Animated.View>
  );
}
