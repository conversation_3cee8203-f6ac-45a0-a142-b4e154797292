import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './StoryRankingItem.styles';
import { Story } from '@/api/stories';
import { useTranslation } from 'react-i18next';
import { formatDistanceToNow } from 'date-fns';
import { Heart } from 'lucide-react-native';

interface StoryRankingItemProps {
  story: Story;
  rank: number;
  onPress: (storyId: string) => void;
}

export function StoryRankingItem({
  story,
  rank,
  onPress,
}: StoryRankingItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const formattedDate = story.updated_at
    ? formatDistanceToNow(new Date(story.updated_at), { addSuffix: true })
    : '';

  const handlePress = () => {
    onPress(story.id);
  };

  // Get rank color
  const getRankColor = () => {
    switch (rank) {
      case 1:
        return theme.colors.gold;
      case 2:
        return theme.colors.silver;
      case 3:
        return theme.colors.bronze;
      default:
        return theme.colors.secondaryText;
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.rankContainer}>
        <Text style={[styles.rankText, { color: getRankColor() }]}>
          {rank}
        </Text>
      </View>

      <View style={styles.imageContainer}>
        {story.cover_image_url ? (
          <Image
            source={{ uri: story.cover_image_url }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.image, styles.placeholderImage]} />
        )}
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={1}>
          {story.title}
        </Text>

        <View style={styles.authorContainer}>
          <Text style={styles.authorName} numberOfLines={1}>
            {story.profiles?.username || t('rankings.unknownAuthor', '未知作者')}
          </Text>
          <Text style={styles.date}>{formattedDate}</Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Heart size={14} color={theme.colors.primary} />
            <Text style={styles.statText}>
              {story.likes_count || 0}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}