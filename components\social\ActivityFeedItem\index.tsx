import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { MessageSquare, Heart, GitBranch } from 'lucide-react-native';
import { User } from '@/types/user';

interface ActivityItem {
  id: string;
  type: 'comment' | 'like' | 'branch';
  user: User;
  storyTitle: string;
  content: string;
  time: string;
}

interface ActivityFeedItemProps {
  item: ActivityItem;
  onPress?: () => void;
}

export default function ActivityFeedItem({ item, onPress }: ActivityFeedItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  const renderIcon = () => {
    const color = getIconColor();
    const size = 16;
    
    switch (item.type) {
      case 'comment':
        return <MessageSquare size={size} color={color} />;
      case 'like':
        return <Heart size={size} color={color} fill={color} />;
      case 'branch':
        return <GitBranch size={size} color={color} />;
      default:
        return null;
    }
  };
  
  const getIconColor = () => {
    switch (item.type) {
      case 'comment':
        return theme.colors.primary;
      case 'like':
        return '#FF4D4D';
      case 'branch':
        return '#30D158';
      default:
        return theme.colors.text;
    }
  };

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      disabled={!onPress}
    >
      <Image 
        source={{ uri: item.user.avatar }}
        style={styles.avatar} 
      />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.username}>
            {item.user.displayName}
          </Text>
          <Text style={styles.time}>
            {item.time}
          </Text>
        </View>
        
        <View style={styles.activityRow}>
          {renderIcon()}
          <Text style={styles.activityText} numberOfLines={1}>
            {item.content}
          </Text>
        </View>
        
        <Text style={styles.storyTitle} numberOfLines={1}>
          《{item.storyTitle}》
        </Text>
      </View>
    </TouchableOpacity>
  );
}
