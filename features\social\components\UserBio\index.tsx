import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface UserBioProps {
  bio: string | null | undefined;
}

export default function UserBio({ bio }: UserBioProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.bioContainer}>
      <Text style={styles.bioText}>
        {bio ||
          t('social.userProfile.noBio', '这个用户很懒，还没有填写个人简介')}
      </Text>
    </View>
  );
}
