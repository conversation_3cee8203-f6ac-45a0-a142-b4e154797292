import { StoryTheme } from '../../types/story';

// Mock story themes
export const mockThemes: StoryTheme[] = [
  {
    id: '1',
    name: '科幻',
    description: '探索未知世界和先进科技',
    icon: 'rocket',
    color: '#4A89F7',
  },
  {
    id: '2',
    name: '奇幻',
    description: '魔法、神话与异世界冒险',
    icon: 'wand',
    color: '#DA62FF',
  },
  {
    id: '3',
    name: '悬疑',
    description: '谜团与惊险的调查旅程',
    icon: 'search',
    color: '#FF9F0A',
  },
  {
    id: '4',
    name: '青春',
    description: '成长、友情与青春的羁绊',
    icon: 'heart',
    color: '#FF453A',
  },
  {
    id: '5',
    name: '历史',
    description: '穿越时空的历史故事',
    icon: 'landmark',
    color: '#8E7CFF',
  },
  {
    id: '6',
    name: '都市',
    description: '现代都市的人生百态',
    icon: 'building',
    color: '#30D158',
  },
];
