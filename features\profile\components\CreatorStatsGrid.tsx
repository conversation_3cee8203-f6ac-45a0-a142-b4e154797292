import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './CreatorStatsGrid.styles';
import { useTranslation } from 'react-i18next';
import { BookOpen, Award, Heart, Star } from 'lucide-react-native'; // Import necessary icons

interface CreatorStats {
  totalStories: number;
  contributionStreak: number;
  totalLikes: number;
  avgCompletionRate: number;
}

interface CreatorStatsGridProps {
  stats: CreatorStats;
}

interface StatCardProps {
  icon: React.ReactNode;
  value: string | number;
  label: string;
}

// Internal component for each stat card
const StatCard = ({ icon, value, label }: StatCardProps) => {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  return (
    <View style={styles.statCard}>
      <View style={styles.iconContainer}>{icon}</View>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );
};

export function CreatorStatsGrid({ stats }: CreatorStatsGridProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t('creatorStats')}</Text>
      <View style={styles.grid}>
        <StatCard 
          icon={<BookOpen size={20} color={theme.colors.primary} />}
          value={stats.totalStories}
          label={t('totalStories')}
        />
        <StatCard 
          icon={<Award size={20} color="#FFB100" />} // Example color
          value={stats.contributionStreak}
          label={t('contributionStreak')}
        />
        <StatCard 
          icon={<Heart size={20} color="#FF4D4D" />} // Example color
          value={stats.totalLikes}
          label={t('totalLikes')}
        />
        <StatCard 
          icon={<Star size={20} color="#5E5CE6" />} // Example color
          value={`${stats.avgCompletionRate}%`}
          label={t('avgCompletionRate')}
        />
      </View>
    </View>
  );
} 