import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { Bell } from 'lucide-react-native';

interface EmptyNotificationsStateProps {
  filterType?: string | null;
}

export default function EmptyNotificationsState({
  filterType,
}: EmptyNotificationsStateProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  // 根据筛选类型显示不同的消息
  const getMessage = () => {
    if (filterType) {
      return t(
        'notifications.emptyFilteredState',
        '没有{{type}}类型的通知',
        { type: getFilterTypeName() }
      );
    }
    return t('notifications.emptyState', '暂无通知');
  };

  // 获取筛选类型的名称
  const getFilterTypeName = () => {
    switch (filterType) {
      case 'like':
        return t('notifications.types.like', '点赞');
      case 'comment':
        return t('notifications.types.comment', '评论');
      case 'follow':
        return t('notifications.types.follow', '关注');
      case 'mention':
        return t('notifications.types.mention', '提及');
      case 'new_story':
        return t('notifications.types.newStory', '新故事');
      case 'new_segment':
        return t('notifications.types.newSegment', '新段落');
      case 'system':
        return t('notifications.types.system', '系统');
      default:
        return '';
    }
  };

  return (
    <View style={styles.container}>
      <Bell size={48} color={theme.colors.secondaryText} />
      <Text style={styles.message}>{getMessage()}</Text>
      <Text style={styles.subMessage}>
        {t(
          'notifications.emptyStateSubMessage',
          '当有人点赞、评论或关注你时，你会收到通知'
        )}
      </Text>
    </View>
  );
}