import { User } from '../../types/user';

// Mock users
export const mockUsers: User[] = [
  {
    id: 'user1',
    username: 'stardust',
    displayName: '星辰大海',
    email: '<EMAIL>',
    avatar:
      'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg',
    bio: '科幻故事创作者，喜欢探索宇宙的奥秘和人类的未来。',
    memberSince: '2022-05-15T00:00:00Z',
    isPremium: true,
    premiumUntil: '2024-05-15T00:00:00Z',
    followers: 1243,
    following: 357,
    storiesCount: 15,
    branchesCount: 87,
    likesReceived: 8976,
    stats: {
      totalStories: 15,
      totalBranches: 87,
      totalLikes: 8976,
      totalViews: 45302,
      avgCompletionRate: 73,
      popularThemes: ['科幻', '哲学', '未来'],
      contributionStreak: 12,
      lastActive: '2023-09-16T08:45:00Z',
    },
  },
  {
    id: 'user2',
    username: 'dreamwalker',
    displayName: '幻境漫步',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg',
    bio: '奇幻故事爱好者，在现实与梦境的边界寻找灵感。',
    memberSince: '2021-10-08T00:00:00Z',
    isPremium: true,
    premiumUntil: '2023-10-08T00:00:00Z',
    followers: 2156,
    following: 194,
    storiesCount: 23,
    branchesCount: 56,
    likesReceived: 12453,
    stats: {
      totalStories: 23,
      totalBranches: 56,
      totalLikes: 12453,
      totalViews: 67890,
      avgCompletionRate: 89,
      popularThemes: ['奇幻', '冒险', '神话'],
      contributionStreak: 28,
      lastActive: '2023-09-17T12:20:00Z',
    },
  },
  {
    id: 'user3',
    username: 'lightchaser',
    displayName: '追光者',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
    bio: '喜欢写悬疑推理故事，追寻真相的过程比真相本身更有魅力。',
    memberSince: '2022-02-28T00:00:00Z',
    isPremium: false,
    premiumUntil: null,
    followers: 843,
    following: 216,
    storiesCount: 9,
    branchesCount: 43,
    likesReceived: 5621,
    stats: {
      totalStories: 9,
      totalBranches: 43,
      totalLikes: 5621,
      totalViews: 28754,
      avgCompletionRate: 67,
      popularThemes: ['悬疑', '推理', '都市'],
      contributionStreak: 5,
      lastActive: '2023-09-15T18:10:00Z',
    },
  },
  {
    id: 'user4',
    username: 'inkdreamer',
    displayName: '墨梦者',
    email: '<EMAIL>',
    avatar:
      'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg',
    bio: '现代诗人和短篇故事创作者，用文字描绘内心世界的色彩。',
    memberSince: '2022-01-15T00:00:00Z',
    isPremium: true,
    premiumUntil: '2024-01-15T00:00:00Z',
    followers: 1875,
    following: 342,
    storiesCount: 28,
    branchesCount: 65,
    likesReceived: 9843,
    stats: {
      totalStories: 28,
      totalBranches: 65,
      totalLikes: 9843,
      totalViews: 35672,
      avgCompletionRate: 82,
      popularThemes: ['文学', '青春', '抒情'],
      contributionStreak: 18,
      lastActive: '2023-09-17T09:25:00Z',
    },
  },
  {
    id: 'user5',
    username: 'timeweaver',
    displayName: '时间编织者',
    email: '<EMAIL>',
    avatar:
      'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg',
    bio: '历史题材故事爱好者，擅长将古代事件与现代元素融合创作。',
    memberSince: '2021-11-20T00:00:00Z',
    isPremium: false,
    premiumUntil: null,
    followers: 1243,
    following: 165,
    storiesCount: 12,
    branchesCount: 38,
    likesReceived: 6754,
    stats: {
      totalStories: 12,
      totalBranches: 38,
      totalLikes: 6754,
      totalViews: 29876,
      avgCompletionRate: 75,
      popularThemes: ['历史', '古风', '架空'],
      contributionStreak: 9,
      lastActive: '2023-09-16T14:30:00Z',
    },
  },
  {
    id: 'user6',
    username: 'urbanchronicler',
    displayName: '都市编年史',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg',
    bio: '专注都市生活故事，从平凡日常中发掘不平凡的故事。',
    memberSince: '2022-04-10T00:00:00Z',
    isPremium: true,
    premiumUntil: '2024-04-10T00:00:00Z',
    followers: 2546,
    following: 287,
    storiesCount: 32,
    branchesCount: 76,
    likesReceived: 14532,
    stats: {
      totalStories: 32,
      totalBranches: 76,
      totalLikes: 14532,
      totalViews: 62145,
      avgCompletionRate: 91,
      popularThemes: ['都市', '职场', '爱情'],
      contributionStreak: 24,
      lastActive: '2023-09-17T16:40:00Z',
    },
  },
  {
    id: 'user7',
    username: 'braveheartnovelist',
    displayName: '勇气小说家',
    email: '<EMAIL>',
    avatar:
      'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',
    bio: '热爱冒险故事，相信每个人心中都有一个英雄。',
    memberSince: '2022-03-05T00:00:00Z',
    isPremium: false,
    premiumUntil: null,
    followers: 985,
    following: 204,
    storiesCount: 11,
    branchesCount: 42,
    likesReceived: 5214,
    stats: {
      totalStories: 11,
      totalBranches: 42,
      totalLikes: 5214,
      totalViews: 24567,
      avgCompletionRate: 68,
      popularThemes: ['冒险', '成长', '勇气'],
      contributionStreak: 7,
      lastActive: '2023-09-15T21:15:00Z',
    },
  },
  {
    id: 'user8',
    username: 'cyberpunker',
    displayName: '赛博朋克',
    email: '<EMAIL>',
    avatar:
      'https://images.pexels.com/photos/1438072/pexels-photo-1438072.jpeg',
    bio: '科技与人性交融的未来世界建造者，黑客文化的践行者。',
    memberSince: '2022-02-14T00:00:00Z',
    isPremium: true,
    premiumUntil: '2024-02-14T00:00:00Z',
    followers: 3142,
    following: 178,
    storiesCount: 19,
    branchesCount: 53,
    likesReceived: 17698,
    stats: {
      totalStories: 19,
      totalBranches: 53,
      totalLikes: 17698,
      totalViews: 72345,
      avgCompletionRate: 86,
      popularThemes: ['赛博朋克', '反乌托邦', '人工智能'],
      contributionStreak: 31,
      lastActive: '2023-09-17T22:05:00Z',
    },
  },
];
