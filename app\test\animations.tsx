import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Animated, Easing } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/StoryTabs';
import { StoryList } from '@/features/stories/components/StoryList';
import { useStoriesScreenTest } from '@/features/stories/hooks/useStoriesScreenTest';
import { RefreshCw, ChevronDown, ChevronUp } from 'lucide-react-native';

/**
 * Animations test screen
 * This screen tests the animations in the app
 */
export default function AnimationsTest() {
  const theme = useAppTheme();
  const { t } = useTranslation();
  const router = useRouter();
  
  // Use the test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
  } = useStoriesScreenTest();
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  // Animation types
  const animationTypes = [
    {
      id: 'fade',
      title: 'Fade In/Out',
      description: 'Fade animation for smooth transitions',
      run: () => {
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start();
      },
    },
    {
      id: 'slide',
      title: 'Slide In/Out',
      description: 'Slide animation for entrance and exit',
      run: () => {
        Animated.sequence([
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
            easing: Easing.out(Easing.cubic),
          }),
          Animated.timing(slideAnim, {
            toValue: -100,
            duration: 1000,
            useNativeDriver: true,
            easing: Easing.in(Easing.cubic),
          }),
        ]).start();
      },
    },
    {
      id: 'spin',
      title: 'Spin',
      description: 'Rotation animation for loading indicators',
      run: () => {
        spinValue.setValue(0);
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.linear,
        }).start();
      },
    },
    {
      id: 'scale',
      title: 'Scale',
      description: 'Scale animation for emphasis',
      run: () => {
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.out(Easing.cubic),
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.in(Easing.cubic),
          }),
        ]).start();
      },
    },
  ];
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  // Interpolate spin value to rotation
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    animationsContainer: {
      marginBottom: theme.spacing.md,
    },
    animationTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    animationCard: {
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 8,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
      flexDirection: 'row',
      alignItems: 'center',
    },
    animationCardContent: {
      flex: 1,
    },
    animationCardTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    animationCardDescription: {
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    animationButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 8,
    },
    animationButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
    backButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
    contentContainer: {
      flex: 1,
    },
    animationPreviewContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.md,
      backgroundColor: `${theme.colors.primary}10`,
      borderRadius: 8,
      marginBottom: theme.spacing.md,
      height: 100,
    },
    animationPreviewText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
  });
  
  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Animations Test</Text>
          
          <View style={styles.animationPreviewContainer}>
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [
                  { translateX: slideAnim },
                  { rotate: spin },
                  { scale: scaleAnim },
                ],
              }}
            >
              <Text style={styles.animationPreviewText}>Animation Preview</Text>
            </Animated.View>
          </View>
          
          <View style={styles.animationsContainer}>
            <Text style={styles.animationTitle}>Animation Types</Text>
            
            {animationTypes.map((animation) => (
              <View key={animation.id} style={styles.animationCard}>
                <View style={styles.animationCardContent}>
                  <Text style={styles.animationCardTitle}>{animation.title}</Text>
                  <Text style={styles.animationCardDescription}>{animation.description}</Text>
                </View>
                
                <TouchableOpacity
                  style={styles.animationButton}
                  onPress={animation.run}
                >
                  <Text style={styles.animationButtonText}>Run</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
          
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Back to Tests</Text>
          </TouchableOpacity>
        </View>
        
        <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
        
        <View style={{ height: 400 }}>
          <StoryList
            stories={stories}
            onStoryPress={handleStoryPress}
            isLoading={isLoading}
            isRefreshing={isRefreshing}
            onRefresh={refreshStories}
            onLoadMore={loadMoreStories}
            hasMoreStories={hasMoreStories}
            error={error}
            onRetry={retryFetch}
            retryCount={retryCount}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
