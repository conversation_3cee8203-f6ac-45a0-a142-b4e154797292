import { Stack } from 'expo-router';
import React from 'react';
import { useAppTheme } from '@/hooks/useAppTheme'; // For header styling based on theme
import { useTranslation } from 'react-i18next'; // For internationalization

export default function ProfileGroupLayout() {
  const theme = useAppTheme();
  const { t } = useTranslation();

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.background,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          color: theme.colors.text,
        },
      }}
    >
      <Stack.Screen
        name="edit"
        options={{
          title: t('profile.editProfileTitle', 'Edit Profile'),
        }}
      />
      {/* Add other screens within the (profile) group here if needed */}
      {/* e.g., <Stack.Screen name="view-achievements" options={{ title: t('profile.achievementsTitle', 'Achievements') }} /> */}
    </Stack>
  );
}
