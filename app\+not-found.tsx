import React from 'react';
import { Link, Stack } from 'expo-router';
import { StyleSheet, Text, View } from 'react-native';

/**
 * NotFoundScreen 组件
 * 
 * 这是一个 404 页面组件，当用户访问不存在的路由时显示。
 * 组件使用了 React Navigation 的 Stack.Screen 来设置页面标题，
 * 并提供了一个返回首页的链接。
 * 
 * @returns JSX 元素
 */
export default function NotFoundScreen() {
  return (
    <>
      {/* 设置页面标题为 "Oops!" */}
      <Stack.Screen options={{ title: 'Oops!' }} />
      
      {/* 主容器视图 */}
      <View style={styles.container}>
        {/* 显示错误信息 */}
        <Text style={styles.text}>This screen doesn't exist.</Text>
        
        {/* 返回首页的链接 */}
        <Link href="/" style={styles.link}>
          <Text>Go to home screen!</Text>
        </Link>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  text: {
    fontSize: 20,
    fontWeight: 600,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
});
