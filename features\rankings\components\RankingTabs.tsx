import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './RankingTabs.styles';
import { useTranslation } from 'react-i18next';
import { RankingPeriod } from '@/api/rankings';

interface RankingTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  activePeriod: RankingPeriod;
  onPeriodChange: (period: RankingPeriod) => void;
}

export function RankingTabs({
  activeTab,
  onTabChange,
  activePeriod,
  onPeriodChange,
}: RankingTabsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const tabs = [
    { id: 'stories', label: t('rankings.tabs.stories', '热门故事') },
    { id: 'authors', label: t('rankings.tabs.authors', '热门作者') },
  ];

  const periods = [
    { id: 'day', label: t('rankings.periods.day', '日榜') },
    { id: 'week', label: t('rankings.periods.week', '周榜') },
    { id: 'month', label: t('rankings.periods.month', '月榜') },
    { id: 'all', label: t('rankings.periods.all', '总榜') },
  ];

  return (
    <View style={styles.container}>
      {/* Main Tabs */}
      <View style={styles.tabsContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tab,
              activeTab === tab.id && styles.activeTab,
            ]}
            onPress={() => onTabChange(tab.id)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab.id && styles.activeTabText,
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Period Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.periodTabsContainer}
      >
        {periods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodTab,
              activePeriod === period.id && styles.activePeriodTab,
            ]}
            onPress={() => onPeriodChange(period.id as RankingPeriod)}
          >
            <Text
              style={[
                styles.periodTabText,
                activePeriod === period.id && styles.activePeriodTabText,
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}