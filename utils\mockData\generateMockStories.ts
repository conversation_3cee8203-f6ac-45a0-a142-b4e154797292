import { Story } from '@/api/stories/types';

/**
 * Generates a specified number of mock stories for testing
 * @param count Number of stories to generate
 * @returns Array of mock stories
 */
export function generateMockStories(count: number = 10): Story[] {
  const stories: Story[] = [];
  
  // Story themes
  const themes = [
    ['科幻', '冒险'],
    ['奇幻', '冒险'],
    ['悬疑', '都市'],
    ['爱情', '现代'],
    ['历史', '战争'],
    ['恐怖', '超自然'],
    ['青春', '校园'],
    ['武侠', '古风'],
    ['推理', '犯罪'],
    ['职场', '都市'],
  ];
  
  // Story titles
  const titlePrefixes = [
    '星际', '迷雾', '记忆', '时光', '幻境', '黑暗', '光明', '永恒', '遗忘', '命运',
    '梦境', '远方', '秘密', '传说', '神话', '未来', '过去', '现在', '虚幻', '真实',
  ];
  
  const titleSuffixes = [
    '迷航', '森林', '收藏家', '旅人', '守护者', '猎人', '使者', '之门', '之路', '之战',
    '之歌', '之舞', '之梦', '之光', '之影', '之谜', '之约', '之恋', '之城', '之国',
  ];
  
  // Author names
  const authorNames = [
    '星辰大海', '幻境漫步', '追光者', '时光旅人', '梦境守护者',
    '黑暗猎人', '光明使者', '永恒之门', '遗忘之路', '命运之战',
  ];
  
  // Cover images
  const coverImages = [
    'https://images.pexels.com/photos/1169754/pexels-photo-1169754.jpeg',
    'https://images.pexels.com/photos/925743/pexels-photo-925743.jpeg',
    'https://images.pexels.com/photos/3693788/pexels-photo-3693788.jpeg',
    'https://images.pexels.com/photos/1252890/pexels-photo-1252890.jpeg',
    'https://images.pexels.com/photos/1366957/pexels-photo-1366957.jpeg',
    'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg',
    'https://images.pexels.com/photos/1366942/pexels-photo-1366942.jpeg',
    'https://images.pexels.com/photos/1366913/pexels-photo-1366913.jpeg',
    'https://images.pexels.com/photos/1366909/pexels-photo-1366909.jpeg',
    'https://images.pexels.com/photos/1366907/pexels-photo-1366907.jpeg',
  ];
  
  // Story content templates
  const contentTemplates = [
    '在遥远的未来，一艘星际飞船发现了一个神秘的量子裂隙，这个发现将改变人类对宇宙的认知。',
    '小镇边缘的迷雾森林一直是禁地，传说进入其中的人再也没有回来过。但当村里的孩子们接连失踪后，年轻的猎人决定铤而走险...',
    '警官接到一起离奇的案件：城市里出现了数十名失忆患者，他们都丢失了同一天的记忆。唯一的线索是每个人手腕上都有一个小小的针孔...',
    '时光旅行已经成为现实，但严格受到政府控制。一位时间管理局的特工发现了一个可怕的阴谋，有人试图改变历史的走向...',
    '在一个被魔法支配的世界里，一位普通的农家少年发现自己拥有着前所未有的强大力量，而这份力量背后隐藏着一个古老的秘密...',
    '城市的夜晚，一系列离奇的谋杀案接连发生，受害者的死状诡异，唯一的共同点是他们的眼睛都被挖走了...',
    '两个来自不同世界的灵魂意外相遇，他们之间不可能的爱情将面临命运的考验...',
    '古代战场上，一位身负重伤的将军被神秘力量救下，醒来后发现自己身处千年之后的现代社会...',
    '一本古老的日记被发现，记录着一段被历史遗忘的真相，而寻找这个真相的过程充满了危险...',
    '在一个科技高度发达的未来，人类的情感被认为是一种疾病，主角发现自己开始有了感情，这让他成为了社会的异类...',
  ];
  
  // Generate stories
  for (let i = 0; i < count; i++) {
    const id = `test-story-${i + 1}`;
    const titlePrefixIndex = i % titlePrefixes.length;
    const titleSuffixIndex = (i + 3) % titleSuffixes.length;
    const title = `${titlePrefixes[titlePrefixIndex]}${titleSuffixes[titleSuffixIndex]}`;
    
    const themeIndex = i % themes.length;
    const authorIndex = i % authorNames.length;
    const coverIndex = i % coverImages.length;
    const contentIndex = i % contentTemplates.length;
    
    // Create dates with some variation
    const createdDate = new Date();
    createdDate.setDate(createdDate.getDate() - (i % 30)); // Up to 30 days ago
    
    const updatedDate = new Date(createdDate);
    updatedDate.setDate(updatedDate.getDate() + (i % 10)); // Up to 10 days after creation
    
    // Create story
    const story: Story = {
      id,
      title,
      author_id: `test-author-${authorIndex + 1}`,
      status: i % 5 === 0 ? 'draft' : 'published',
      visibility: 'public',
      tags: themes[themeIndex],
      cover_image_url: coverImages[coverIndex],
      created_at: createdDate.toISOString(),
      updated_at: updatedDate.toISOString(),
      // Embedded/joined data
      profiles: {
        id: `test-author-${authorIndex + 1}`,
        username: authorNames[authorIndex],
        avatar_url: null,
      },
      like_count: Math.floor(Math.random() * 500),
      is_liked_by_user: i % 3 === 0, // Every third story is liked
      first_segment_content: contentTemplates[contentIndex],
    };
    
    stories.push(story);
  }
  
  return stories;
}
