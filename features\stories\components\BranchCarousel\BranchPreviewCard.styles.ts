import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: theme.spacing.md,
      marginHorizontal: theme.spacing.xs,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 3,
      minHeight: 180, // 确保卡片有最小高度
    },
    activeContainer: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
      backgroundColor: theme.colors.surface,
    },
    contentPreview: {
      fontSize: 15,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      lineHeight: 22,
      marginBottom: theme.spacing.md,
      minHeight: 88, // 确保内容预览区域有最小高度
    },
    authorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    authorAvatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
      marginRight: theme.spacing.sm,
      backgroundColor: theme.colors.border, // 占位背景色
    },
    authorName: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: theme.spacing.sm,
      paddingTop: theme.spacing.sm,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border + '50', // 半透明边框
    },
    statsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    statText: {
      fontSize: 12,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginLeft: theme.spacing.xs,
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: 12, // 更圆润的徽章
    },
    statusText: {
      fontSize: 12,
      fontFamily: theme.fonts.bold,
    },
  });
