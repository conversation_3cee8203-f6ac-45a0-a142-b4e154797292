import React from 'react';
import { View, Text, Image } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { Message } from '@/api/messages/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';

interface MessageItemProps {
  message: Message;
  isCurrentUser: boolean;
}

export default function MessageItem({
  message,
  isCurrentUser,
}: MessageItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t, i18n } = useTranslation();

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <View
      style={[
        styles.container,
        isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer,
      ]}
    >
      {!isCurrentUser && message.sender && (
        <View style={styles.avatarContainer}>
          {message.sender.avatar_url ? (
            <Image
              source={{ uri: message.sender.avatar_url }}
              style={styles.avatar}
            />
          ) : (
            <View style={[styles.avatar, styles.placeholderAvatar]} />
          )}
        </View>
      )}

      <View
        style={[
          styles.messageContainer,
          isCurrentUser ? styles.currentUserMessageContainer : styles.otherUserMessageContainer,
        ]}
      >
        <Text style={styles.messageText}>{message.content}</Text>
        <Text style={styles.timeText}>{formatTime(message.created_at)}</Text>
      </View>

      {isCurrentUser && (
        <View style={styles.avatarContainer}>
          {message.sender && message.sender.avatar_url ? (
            <Image
              source={{ uri: message.sender.avatar_url }}
              style={styles.avatar}
            />
          ) : (
            <View style={[styles.avatar, styles.placeholderAvatar]} />
          )}
        </View>
      )}
    </View>
  );
}