import { createClient } from '@supabase/supabase-js';
import { mockUsers, mockStories, mockThemes } from '../utils/mockData';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function migrateData() {
  try {
    console.log('Starting data migration...');

    // Migrate themes
    console.log('Migrating themes...');
    const { data: themesData, error: themesError } = await supabase
      .from('story_themes')
      .insert(mockThemes.map(theme => ({
        name: theme.name,
        description: theme.description,
        icon: theme.icon,
        color: theme.color
      })))
      .select();

    if (themesError) {
      throw new Error(`Error migrating themes: ${themesError.message}`);
    }
    console.log('Themes migrated successfully:', themesData);

    // Migrate users
    console.log('Migrating users...');
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .insert(mockUsers.map(user => ({
        id: user.id,
        username: user.username,
        display_name: user.displayName,
        email: user.email,
        avatar_url: user.avatar,
        bio: user.bio,
        member_since: user.memberSince,
        is_premium: user.isPremium,
        premium_until: user.premiumUntil,
        followers_count: user.followers,
        following_count: user.following,
        stories_count: user.storiesCount,
        branches_count: user.branchesCount,
        likes_received: user.likesReceived
      })))
      .select();

    if (usersError) {
      throw new Error(`Error migrating users: ${usersError.message}`);
    }
    console.log('Users migrated successfully:', usersData);

    // Migrate stories and branches
    console.log('Migrating stories and branches...');
    for (const story of mockStories) {
      // Insert story
      const { data: storyData, error: storyError } = await supabase
        .from('stories')
        .insert({
          id: story.id,
          title: story.title,
          cover_image: story.coverImage,
          summary: story.summary,
          author_id: story.authorId,
          is_completed: story.isCompleted,
          is_premium: story.isPremium,
          likes_count: story.likes,
          views_count: story.views,
          is_ai_assisted: story.isAiAssisted,
          created_at: story.createdAt,
          updated_at: story.updatedAt
        })
        .select()
        .single();

      if (storyError) {
        throw new Error(`Error migrating story ${story.id}: ${storyError.message}`);
      }

      // Insert story themes
      const { error: themesJunctionError } = await supabase
        .from('stories_themes')
        .insert(story.theme.map(themeName => ({
          story_id: story.id,
          theme_id: mockThemes.find(t => t.name === themeName)?.id
        })));

      if (themesJunctionError) {
        throw new Error(`Error migrating story themes for ${story.id}: ${themesJunctionError.message}`);
      }

      // Insert branches
      for (const [branchId, branch] of Object.entries(story.branches)) {
        const { error: branchError } = await supabase
          .from('story_branches')
          .insert({
            id: branchId,
            story_id: story.id,
            content: branch.content,
            author_id: branch.authorId,
            is_ai_generated: branch.isAiGenerated,
            likes_count: branch.likes,
            parent_branch_id: branch.parentBranchId,
            created_at: branch.createdAt
          });

        if (branchError) {
          throw new Error(`Error migrating branch ${branchId}: ${branchError.message}`);
        }
      }

      // Update story with main branch ID
      const { error: updateMainBranchError } = await supabase
        .from('stories')
        .update({ main_branch_id: story.mainBranchId })
        .eq('id', story.id);

      if (updateMainBranchError) {
        throw new Error(`Error updating main branch for story ${story.id}: ${updateMainBranchError.message}`);
      }
    }

    console.log('Data migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

migrateData();