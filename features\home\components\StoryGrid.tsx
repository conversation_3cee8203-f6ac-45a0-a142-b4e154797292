import React from 'react';
import { FlatList } from 'react-native';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import StoryCard, { Story } from '@/components/stories/StoryCard';

interface StoryGridProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
}

export function StoryGrid({ stories, onStoryPress }: StoryGridProps) {
  const theme = usePaperTheme();

  const renderStoryCard = ({ item }: { item: Story }) => (
    <StoryCard story={item} onPress={() => onStoryPress?.(item.id)} />
  );

  return (
    <FlatList
      data={stories}
      renderItem={renderStoryCard}
      keyExtractor={(item) => item.id}
      numColumns={2}
      contentContainerStyle={{
        padding: theme.spacing?.xs || 4,
      }}
      columnWrapperStyle={{
        justifyContent: 'space-between',
      }}
      showsVerticalScrollIndicator={false}
      scrollEnabled={false} // Disable scroll since it's inside a ScrollView
    />
  );
}
