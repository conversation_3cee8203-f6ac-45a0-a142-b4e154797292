import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  ScrollView,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { X, GitBranch, Sparkles } from 'lucide-react-native';
import { useStoryOptimization } from '@/features/stories/hooks/useStoryOptimization';
import StoryOptimizationBlock from '@/features/stories/components/StoryOptimizationBlock';

interface CreateBranchFormProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (
    content: string,
    branchTitle?: string,
    isAiGenerated?: boolean
  ) => Promise<void>;
  parentSegmentId: string;
  isSubmitting: boolean;
  onRequestAiSuggestion?: () => Promise<string | null>;
}

export default function CreateBranchForm({
  visible,
  onClose,
  onSubmit,
  parentSegmentId,
  isSubmitting,
  onRequestAiSuggestion,
}: CreateBranchFormProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  const [branchTitle, setBranchTitle] = useState('');
  const [branchContent, setBranchContent] = useState('');
  const [isGeneratingAiSuggestion, setIsGeneratingAiSuggestion] =
    useState(false);
  const [isAIGenerated, setIsAIGenerated] = useState(false);

  // 添加 AI 优化功能
  const { isOptimizing, handleOptimizeContent } = useStoryOptimization({
    onOptimizedContent: (optimizedContent) => {
      setBranchContent(optimizedContent);
      setIsAIGenerated(true);
    },
  });

  // 处理提交
  const handleSubmit = async () => {
    if (!branchContent.trim()) return;
    await onSubmit(
      branchContent.trim(),
      branchTitle.trim() || undefined,
      isAIGenerated
    );
    resetForm();
  };

  // 处理 AI 优化
  const handleOptimize = (contentToOptimize: string, type: any) => {
    handleOptimizeContent(contentToOptimize);
  };

  // 处理AI建议
  const handleAiSuggestion = async () => {
    if (!onRequestAiSuggestion) return;

    setIsGeneratingAiSuggestion(true);
    try {
      const suggestion = await onRequestAiSuggestion();
      if (suggestion) {
        setBranchContent(suggestion);
      }
    } finally {
      setIsGeneratingAiSuggestion(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setBranchTitle('');
    setBranchContent('');
    setIsAIGenerated(false);
  };

  // 关闭模态框
  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {t('storyDetail.createBranch', 'Create New Branch')}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleClose}
              disabled={isSubmitting}
            >
              <X size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formContainer}>
            <Text style={styles.inputLabel}>
              {t('storyDetail.branchTitle', 'Branch Title (Optional)')}
            </Text>
            <TextInput
              style={styles.titleInput}
              placeholder={t(
                'storyDetail.branchTitlePlaceholder',
                'Enter a title for this branch'
              )}
              placeholderTextColor={theme.colors.placeholder}
              value={branchTitle}
              onChangeText={setBranchTitle}
              maxLength={50}
              editable={!isSubmitting}
            />

            <Text style={styles.inputLabel}>
              {t('storyDetail.branchContent', 'Branch Content')}
            </Text>
            <TextInput
              style={styles.contentInput}
              placeholder={t(
                'storyDetail.branchContentPlaceholder',
                'Continue the story...'
              )}
              placeholderTextColor={theme.colors.placeholder}
              multiline
              textAlignVertical="top"
              value={branchContent}
              onChangeText={setBranchContent}
              editable={!isSubmitting && !isGeneratingAiSuggestion}
            />

            {onRequestAiSuggestion && (
              <TouchableOpacity
                style={styles.aiSuggestionButton}
                onPress={handleAiSuggestion}
                disabled={isSubmitting || isGeneratingAiSuggestion}
              >
                {isGeneratingAiSuggestion ? (
                  <ActivityIndicator
                    size="small"
                    color={theme.colors.onPrimary}
                  />
                ) : (
                  <>
                    <Sparkles size={16} color={theme.colors.onPrimary} />
                    <Text style={styles.aiSuggestionButtonText}>
                      {t('storyDetail.getAiSuggestion', 'Get AI Suggestion')}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            )}

            {/* AI 优化按钮 */}
            <StoryOptimizationBlock
              onOptimizeContent={handleOptimize}
              isOptimizing={isOptimizing}
              disabled={isSubmitting}
            />
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.cancelButton,
                isSubmitting && styles.disabledButton,
              ]}
              onPress={handleClose}
              disabled={isSubmitting}
            >
              <Text style={styles.cancelButtonText}>
                {t('cancel', 'Cancel')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.submitButton,
                (!branchContent.trim() || isSubmitting) &&
                  styles.disabledButton,
              ]}
              onPress={handleSubmit}
              disabled={!branchContent.trim() || isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator
                  size="small"
                  color={theme.colors.onPrimary}
                />
              ) : (
                <>
                  <GitBranch size={16} color={theme.colors.onPrimary} />
                  <Text style={styles.submitButtonText}>
                    {t('storyDetail.createBranchButton', 'Create Branch')}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
