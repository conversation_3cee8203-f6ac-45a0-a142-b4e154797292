import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { UserPlus, Check, MessageCircle } from 'lucide-react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface UserActionButtonsProps {
  isFollowing: boolean;
  isFollowingInProgress: boolean;
  onFollowToggle: () => void;
  onSendMessage: () => void;
}

export default function UserActionButtons({
  isFollowing,
  isFollowingInProgress,
  onFollowToggle,
  onSendMessage,
}: UserActionButtonsProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.actionButtonsContainer}>
      <TouchableOpacity
        style={[
          styles.actionButton,
          styles.followButton,
          isFollowing && styles.followingButton,
        ]}
        onPress={onFollowToggle}
        disabled={isFollowingInProgress}
      >
        {isFollowingInProgress ? (
          <ActivityIndicator
            size="small"
            color={
              isFollowing ? theme.colors.primary : theme.colors.background
            }
          />
        ) : (
          <>
            {isFollowing ? (
              <Check size={16} color={theme.colors.primary} />
            ) : (
              <UserPlus size={16} color={theme.colors.background} />
            )}
            <Text
              style={[
                styles.actionButtonText,
                isFollowing && styles.followingButtonText,
              ]}
            >
              {isFollowing
                ? t('social.userProfile.followingStatus', '已关注')
                : t('social.userProfile.follow', '关注')}
            </Text>
          </>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.actionButton, styles.messageButton]}
        onPress={onSendMessage}
      >
        <MessageCircle size={16} color={theme.colors.primary} />
        <Text style={[styles.actionButtonText, styles.messageButtonText]}>
          {t('social.userProfile.message', '发消息')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}
