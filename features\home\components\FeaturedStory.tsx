import React from 'react';
import FeaturedStoryCard from '@/components/stories/FeaturedStoryCard';
import { Story } from '@/api/stories';

interface FeaturedStoryProps {
  story: Story;
  onPress?: (storyId: string) => void;
}

export function FeaturedStory({ story, onPress }: FeaturedStoryProps) {
  // Handle case where story might be null or undefined
  if (!story) {
    return null;
  }

  return (
    <FeaturedStoryCard story={story} onPress={() => onPress?.(story.id)} />
  );
}
