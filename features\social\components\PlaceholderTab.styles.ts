import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Shared styles for placeholder tabs (Messages, Notifications)
export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
    backgroundColor: theme.colors.background, // Ensure background matches
  },
  iconContainer: {
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontFamily: theme.fonts.bold,
    fontSize: 20,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontFamily: theme.fonts.regular,
    fontSize: 16,
    color: theme.colors.secondaryText,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  actionButton: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness * 2,
    backgroundColor: theme.colors.primary,
  },
  actionButtonText: {
    fontFamily: theme.fonts.bold,
    fontSize: 16,
    color: theme.dark ? theme.colors.background : '#FFF',
  },
}); 