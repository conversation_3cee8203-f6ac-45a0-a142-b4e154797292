import React from 'react';
import { View, Text, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface ContinuationInputProps {
  content: string;
  onContentChange: (text: string) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export default function ContinuationInput({
  content,
  onContentChange,
  onSubmit,
  isSubmitting,
}: ContinuationInputProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.continuationInputContainer}>
      <TextInput
        style={styles.continuationInput}
        placeholder={t(
          'story.writeContinuationPlaceholder',
          'Write your continuation here...'
        )}
        value={content}
        onChangeText={onContentChange}
        multiline
      />
      <TouchableOpacity
        style={[styles.actionButton, styles.submitButton]}
        onPress={onSubmit}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <ActivityIndicator size="small" color={theme.colors.primary} />
        ) : (
          <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
            {t('story.submitContinuation', 'Submit')}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
}
