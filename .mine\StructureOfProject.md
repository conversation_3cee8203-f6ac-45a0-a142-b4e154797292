# 项目结构概述

本文档提供了 SupaPose 项目的整体目录结构和各个目录的主要功能划分。它就像一份地图，告诉你不同的区域（文件夹）大致负责什么，以及一些重要地标（关键文件或子目录）在哪里。

与之配套的文档：

- `.mine/ComponentsStructure.md` - 详细描述组件和 UI 层的结构
- `.mine/FeaturesStructure.md` - 详细描述业务功能层的结构
- `.mine/PrinciplesAndPractices.md` - 详细的行为准则和规范

## 项目根目录结构

```
SupaPose/
├── app/                       # 路由层 (Expo Router) - 保持精简，仅负责路由和布局定义
│   ├── (auth)/                # 认证相关路由组
│   ├── (tabs)/                # Tab 导航组
│   ├── (settings)/            # 设置导航组 (Stack)
│   ├── stories/               # 独立的故事访问路径
│   ├── users/                 # 用户相关路由
│   ├── components/            # 应用级组件
│   ├── hooks/                 # 应用级钩子
│   ├── _layout.tsx            # 根布局 (全局 Providers: Theme, Zustand, SafeArea, i18n)
│   ├── +not-found.tsx         # 404 页面
│   └── index.tsx              # 入口/引导页
├── api/                       # API 调用层
│   ├── supabase/              # Supabase 相关 API 封装
│   ├── ai/                    # AI 服务相关
│   └── index.ts               # 统一导出所有 API
├── assets/                    # 静态资源 (图片, 字体等)
│   ├── fonts/                 # 字体文件
│   └── images/                # 图片资源
├── components/                # 可复用的 UI 层 (详见 ComponentsStructure.md)
├── features/                  # 业务功能层 (核心) (详见 FeaturesStructure.md)
├── hooks/                     # 全局共享的自定义 Hooks
│   ├── useAppTheme.ts         # 主题 Hook
│   ├── useAuth.ts             # 认证 Hook
│   └── ...                    # 其他全局 Hooks
├── lib/                       # 核心库配置、全局服务实例、初始化逻辑
│   ├── i18n/                  # 国际化配置
│   ├── store/                 # 全局 Zustand store
│   ├── supabase/              # Supabase 客户端初始化
│   └── theme/                 # 主题系统
├── translations/              # 国际化语言文件
│   ├── en/                    # 英文翻译
│   ├── zh/                    # 中文翻译
│   └── index.ts               # 统一导出所有翻译
├── types/                     # 全局共享的 TypeScript 类型定义
│   ├── navigation.ts          # 导航类型
│   ├── api.ts                 # API 响应类型
│   ├── models.ts              # 数据模型类型
│   ├── index.ts               # 导出所有全局类型
│   └── ...                    # 其他全局类型
├── utils/                     # 全局工具函数、常量
│   ├── format/                # 格式化相关
│   ├── validation/            # 验证相关
│   ├── storage/               # 本地存储工具
│   ├── mockData/              # 模拟数据
│   ├── constants.ts           # 全局常量
│   └── helpers.ts             # 通用辅助函数
├── __tests__/                 # 测试文件
│   ├── components/            # 组件测试
│   ├── hooks/                 # Hook 测试
│   └── features/              # 功能测试
├── .expo/                     # Expo 配置
├── .git/                      # Git 版本控制
├── .mine/                     # 项目核心文档与指南
│   ├── PRD.md                 # 产品需求文档 (Product Requirements Document)
│   ├── ProjectStructure.md    # 本文档 - 项目目录结构概述
│   ├── ComponentsStructure.md # 组件和 UI 层结构详情
│   ├── FeaturesStructure.md   # 业务功能层结构详情
│   ├── PrinciplesAndPractices.md # 开发原则与最佳实践
│   ├── Progress.md            # 项目开发进度追踪
│   ├── API.md                 # API 层设计与接口说明
│   ├── Components.md          # UI 组件库组织与指导
│   ├── State.md               # 全局与功能状态管理策略
│   ├── DesignGuidelines.md    # UI/UX 设计规范细则
│   └── DocsMedia/             # 项目文档相关的图片、截图、设计稿等媒体文件存放目录
├── node_modules/              # 依赖包
├── .gitignore                 # Git 忽略文件
├── app.json                   # Expo 应用配置
├── babel.config.js            # Babel 配置 (含路径别名)
├── metro.config.js            # Metro Bundler 配置
├── tamagui.config.ts          # Tamagui 配置文件 (定义 tokens, themes, fonts, etc.)
├── tsconfig.json              # TypeScript 配置 (含路径别名、strict 模式)
├── package.json               # 项目依赖与脚本
└── pnpm-lock.yaml             # PNPM 锁定文件
```

## 文档导航

- 查看 [ComponentsStructure.md](./.mine/ComponentsStructure.md) 了解组件和 UI 层的详细结构
- 查看 [FeaturesStructure.md](./.mine/FeaturesStructure.md) 了解业务功能层的详细结构
- 查看 [PrinciplesAndPractices.md](./.mine/PrinciplesAndPractices.md) 了解开发原则与最佳实践
