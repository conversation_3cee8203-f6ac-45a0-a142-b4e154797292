import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { MoreVertical, Edit, Trash, X } from 'lucide-react-native';
import { StorySegment } from '@/api/stories';

interface BranchActionsMenuProps {
  segment: StorySegment;
  onRename: (segmentId: string, branchTitle: string) => Promise<StorySegment | null>;
  onDelete: (segmentId: string) => Promise<boolean>;
  isAuthor: boolean;
}

export default function BranchActionsMenu({
  segment,
  onRename,
  onDelete,
  isAuthor,
}: BranchActionsMenuProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  const [menuVisible, setMenuVisible] = useState(false);
  const [renameModalVisible, setRenameModalVisible] = useState(false);
  const [newBranchTitle, setNewBranchTitle] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // 提取分支标题（如果有）
  const extractBranchTitle = (content: string): string => {
    const titleMatch = content.match(/^\[(.*?)\]\s/);
    return titleMatch ? titleMatch[1] : '';
  };

  // 打开重命名模态框
  const handleOpenRenameModal = () => {
    setNewBranchTitle(extractBranchTitle(segment.content));
    setRenameModalVisible(true);
    setMenuVisible(false);
  };

  // 重命名分支
  const handleRename = async () => {
    if (!newBranchTitle.trim()) {
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.branchTitleRequired', 'Branch title is required')
      );
      return;
    }

    setIsProcessing(true);
    try {
      const result = await onRename(segment.id, newBranchTitle.trim());
      if (result) {
        setRenameModalVisible(false);
        Alert.alert(
          t('success', 'Success'),
          t('storyDetail.branchRenamed', 'Branch renamed successfully')
        );
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // 删除分支
  const handleDelete = () => {
    Alert.alert(
      t('storyDetail.deleteBranchTitle', 'Delete Branch'),
      t('storyDetail.deleteBranchConfirm', 'Are you sure you want to delete this branch? This action cannot be undone.'),
      [
        {
          text: t('cancel', 'Cancel'),
          style: 'cancel',
        },
        {
          text: t('delete', 'Delete'),
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              const success = await onDelete(segment.id);
              if (success) {
                setMenuVisible(false);
                // 成功消息会在useStoryBranches中处理
              }
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
    setMenuVisible(false);
  };

  // 如果不是作者，不显示菜单
  if (!isAuthor) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        style={styles.menuButton}
        onPress={() => setMenuVisible(true)}
      >
        <MoreVertical size={18} color={theme.colors.text} />
      </TouchableOpacity>

      {/* 操作菜单 */}
      <Modal
        visible={menuVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setMenuVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setMenuVisible(false)}
        >
          <View style={styles.menuContainer}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleOpenRenameModal}
            >
              <Edit size={18} color={theme.colors.text} />
              <Text style={styles.menuItemText}>
                {t('storyDetail.renameBranch', 'Rename Branch')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, styles.deleteMenuItem]}
              onPress={handleDelete}
            >
              <Trash size={18} color={theme.colors.error} />
              <Text style={[styles.menuItemText, styles.deleteMenuItemText]}>
                {t('storyDetail.deleteBranch', 'Delete Branch')}
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* 重命名模态框 */}
      <Modal
        visible={renameModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setRenameModalVisible(false)}
      >
        <View style={styles.renameModalContainer}>
          <View style={styles.renameModalContent}>
            <View style={styles.renameModalHeader}>
              <Text style={styles.renameModalTitle}>
                {t('storyDetail.renameBranch', 'Rename Branch')}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setRenameModalVisible(false)}
                disabled={isProcessing}
              >
                <X size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            <Text style={styles.inputLabel}>
              {t('storyDetail.branchTitle', 'Branch Title')}
            </Text>
            <TextInput
              style={styles.input}
              value={newBranchTitle}
              onChangeText={setNewBranchTitle}
              placeholder={t('storyDetail.branchTitlePlaceholder', 'Enter branch title')}
              placeholderTextColor={theme.colors.placeholder}
              editable={!isProcessing}
            />

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setRenameModalVisible(false)}
                disabled={isProcessing}
              >
                <Text style={styles.cancelButtonText}>
                  {t('cancel', 'Cancel')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.saveButton, isProcessing && styles.disabledButton]}
                onPress={handleRename}
                disabled={isProcessing}
              >
                <Text style={styles.saveButtonText}>
                  {t('save', 'Save')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}
