import React from 'react';
import { View, FlatList, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { useConversations } from '../../hooks/useConversations';
import ConversationItem from '../ConversationItem';
import EmptyConversationsState from '../EmptyConversationsState';
import { Conversation } from '@/api/messages/types';
import { Plus } from 'lucide-react-native';

export default function MessagesTab() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();

  const {
    conversations,
    isLoading,
    hasMore,
    refreshConversations,
    loadMoreConversations,
  } = useConversations({ autoRefresh: true });

  // 处理对话点击
  const handleConversationPress = (conversation: Conversation) => {
    router.push(`/messages/${conversation.id}`);
  };

  // 处理新建对话
  const handleNewConversation = () => {
    router.push('/messages/new');
  };

  // 渲染列表底部
  const renderFooter = () => {
    if (!isLoading) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    if (isLoading) return null;
    return <EmptyConversationsState onNewConversation={handleNewConversation} />;
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={conversations}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <ConversationItem
            conversation={item}
            onPress={handleConversationPress}
          />
        )}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        onRefresh={refreshConversations}
        refreshing={isLoading && conversations.length === 0}
        onEndReached={loadMoreConversations}
        onEndReachedThreshold={0.5}
        contentContainerStyle={
          conversations.length === 0 ? styles.emptyContentContainer : undefined
        }
      />

      <TouchableOpacity
        style={styles.fab}
        onPress={handleNewConversation}
        activeOpacity={0.7}
      >
        <Plus size={24} color={theme.colors.background} />
      </TouchableOpacity>
    </View>
  );
}