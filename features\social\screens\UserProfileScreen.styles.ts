import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
      padding: 20,
    },
    errorText: {
      color: theme.colors.error,
      textAlign: 'center',
      fontSize: 16,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    avatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      marginRight: 16,
    },
    nameContainer: {
      flex: 1,
    },
    displayName: {
      fontSize: 22,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    username: {
      fontSize: 16,
      color: theme.colors.secondaryText,
      marginTop: 4,
    },
    premiumBadge: {
      backgroundColor: theme.colors.accent,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 8,
    },
    premiumText: {
      color: '#000',
      fontSize: 12,
      fontWeight: 'bold',
    },
    bioContainer: {
      padding: 16,
      paddingTop: 0,
    },
    bioText: {
      fontSize: 16,
      color: theme.colors.text,
      lineHeight: 22,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 16,
      borderTopWidth: StyleSheet.hairlineWidth,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderColor: theme.colors.border,
    },
    statItem: {
      alignItems: 'center',
    },
    statNumber: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    statLabel: {
      fontSize: 14,
      color: theme.colors.secondaryText,
      marginTop: 4,
    },
    statDivider: {
      width: 1,
      backgroundColor: theme.colors.border,
    },
    actionButtonsContainer: {
      flexDirection: 'row',
      padding: 16,
      justifyContent: 'center',
      gap: 12,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 20,
      flex: 1,
    },
    followButton: {
      backgroundColor: theme.colors.primary,
    },
    followingButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    messageButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    actionButtonText: {
      marginLeft: 8,
      fontWeight: '500',
      fontSize: 14,
      color: theme.colors.background,
    },
    followingButtonText: {
      color: theme.colors.primary,
    },
    messageButtonText: {
      color: theme.colors.primary,
    },
    storiesContainer: {
      padding: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
    },
    emptyStoriesContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 32,
      borderWidth: 1,
      borderStyle: 'dashed',
      borderColor: theme.colors.border,
      borderRadius: 8,
    },
    emptyStoriesText: {
      marginTop: 8,
      fontSize: 16,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
  });
