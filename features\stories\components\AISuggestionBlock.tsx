import React, { useMemo } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createAISuggestionBlockStyles } from './AISuggestionBlock.styles';
import AiSuggestionCard from '@/components/creation/AiSuggestionCard'; // Reusable component

interface AISuggestionBlockProps {
  onFetchSuggestions: () => void;
  loadingSuggestions: boolean;
  showSuggestions: boolean;
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  isProcessingSegment: boolean; // To disable AI button while segment is being submitted
}

export default function AISuggestionBlock({
  onFetchSuggestions,
  loadingSuggestions,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  isProcessingSegment,
}: AISuggestionBlockProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = useMemo(() => createAISuggestionBlockStyles(theme), [theme]);

  return (
    <View style={styles.aiSectionContainer}>
      <TouchableOpacity
        style={styles.aiButton} // Specific AI button style
        onPress={onFetchSuggestions}
        disabled={loadingSuggestions || isProcessingSegment}
      >
        {loadingSuggestions ? (
          <ActivityIndicator color={theme.colors.onPrimary} />
        ) : (
          <Text style={styles.buttonText}>
            {t('storyDetail.getAISegmentSuggestions', 'AI 续写建议')}
          </Text>
        )}
      </TouchableOpacity>

      {showSuggestions && loadingSuggestions && (
        <ActivityIndicator
          size="large"
          color={theme.colors.primary}
          style={styles.aiLoadingIndicator}
        />
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length > 0 && (
        <View style={styles.aiSuggestionsListContainer}>
          <Text style={styles.suggestionsTitle}>
            {t('aiSuggestions.title', 'AI 建议:')}
          </Text>
          {suggestions.map((suggestion, index) => (
            <AiSuggestionCard
              key={index}
              suggestion={suggestion}
              onSelect={() => onSelectSuggestion(suggestion)}
              // Assuming AiSuggestionCard handles its own styling internally
            />
          ))}
        </View>
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length === 0 && (
        <Text style={styles.noSuggestionsText}>
          {t(
            'aiSuggestions.noSuggestionsShort', // Using a shorter key for this specific context
            '暂无建议。'
          )}
        </Text>
      )}
    </View>
  );
}
