import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Users } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from '../FeedTab.styles';

interface EmptyStateProps {
  onDiscoverPress?: () => void;
}

export function EmptyState({ onDiscoverPress }: EmptyStateProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.emptyStateContainer}>
      <Users
        size={60}
        color={theme.colors.secondaryText}
        style={styles.emptyStateIcon}
      />
      <Text style={styles.emptyStateTitle}>
        {t('social.feed.emptyStateTitle', '暂无动态')}
      </Text>
      <Text style={styles.emptyStateText}>
        {t(
          'social.feed.empty',
          '暂无动态。关注更多用户以在此处查看他们的故事。'
        )}
      </Text>
      <TouchableOpacity
        style={styles.emptyStateButton}
        onPress={onDiscoverPress}
      >
        <Text style={styles.emptyStateButtonText}>
          {t('social.feed.discoverUsers', '发现用户')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}
