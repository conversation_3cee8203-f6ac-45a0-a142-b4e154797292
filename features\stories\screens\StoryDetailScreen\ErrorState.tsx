import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { createDynamicStyles } from '../StoryDetailScreen.styles';

interface ErrorStateProps {
  errorMessage: string;
  onRetry: () => void;
}

export function ErrorState({ errorMessage, onRetry }: ErrorStateProps) {
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.centered}>
      <Text style={styles.errorText}>{errorMessage}</Text>
      <TouchableOpacity onPress={onRetry} style={styles.button}>
        <Text style={styles.buttonText}>{t('tryAgain', 'Try Again')}</Text>
      </TouchableOpacity>
    </View>
  );
}
