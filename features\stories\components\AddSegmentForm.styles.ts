import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createAddSegmentFormStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    addSegmentSection: {
      paddingTop: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      marginTop: theme.spacing.lg, // Add margin from the content above
    },
    sectionTitle: {
      fontSize: theme.fontSizes.lg, // Consistent with other section titles
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    textInput: {
      backgroundColor: theme.colors.surface,
      color: theme.colors.text,
      padding: theme.spacing.md,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.md,
      fontSize: theme.fontSizes.md,
      textAlignVertical: 'top',
      minHeight: 120, // Increased minHeight for better multiline input
      lineHeight: theme.lineHeights.md, // Ensure consistent line height
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    submitButton: {
      flex: 1,
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
    },
    submitButtonWithBranch: {
      marginLeft: theme.spacing.md,
    },
    branchButton: {
      flex: 1,
      flexDirection: 'row',
      backgroundColor: theme.colors.accent,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
    },
    submitButtonText: {
      color: theme.colors.onPrimary,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
    },
    branchButtonText: {
      color: theme.colors.onPrimary,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
      marginLeft: theme.spacing.xs,
    },
    buttonDisabled: {
      backgroundColor: theme.colors.disabled,
      opacity: 0.7,
    },
  });
};
