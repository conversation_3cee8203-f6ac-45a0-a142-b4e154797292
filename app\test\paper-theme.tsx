import React from 'react';
import { ScrollView, View } from 'react-native';
import { 
  Surface, 
  Text, 
  Button, 
  Card, 
  Chip, 
  TextInput,
  SegmentedButtons,
  ActivityIndicator,
  Icon
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useSettingsStore } from '@/lib/store/settingsStore';

export default function PaperThemeTestScreen() {
  const theme = usePaperTheme();
  const { themeMode, setThemeMode } = useSettingsStore();
  const [segmentedValue, setSegmentedValue] = React.useState('option1');
  const [textValue, setTextValue] = React.useState('');

  const themeButtons = [
    { value: 'light', label: '亮色' },
    { value: 'dark', label: '暗色' },
    { value: 'system', label: '系统' },
  ];

  return (
    <Surface style={{ flex: 1 }}>
      <SafeAreaView style={{ flex: 1 }} edges={['top', 'left', 'right']}>
        <ScrollView 
          style={{ flex: 1 }}
          contentContainerStyle={{
            padding: theme.spacing?.md || 16,
            gap: theme.spacing?.md || 16,
          }}
        >
          <Text variant="headlineMedium">Paper 主题系统测试</Text>
          
          {/* 主题切换 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                主题切换
              </Text>
              <SegmentedButtons
                value={themeMode}
                onValueChange={(value) => setThemeMode(value as any)}
                buttons={themeButtons}
              />
            </Card.Content>
          </Card>

          {/* 颜色展示 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                主题颜色
              </Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: theme.spacing?.xs || 4 }}>
                <Chip style={{ backgroundColor: theme.colors.primary }}>
                  Primary
                </Chip>
                <Chip style={{ backgroundColor: theme.colors.secondary }}>
                  Secondary
                </Chip>
                <Chip style={{ backgroundColor: theme.colors.tertiary }}>
                  Tertiary
                </Chip>
                <Chip style={{ backgroundColor: theme.colors.aiAction }}>
                  AI Action
                </Chip>
              </View>
            </Card.Content>
          </Card>

          {/* 按钮展示 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                按钮样式
              </Text>
              <View style={{ gap: theme.spacing?.sm || 8 }}>
                <Button mode="contained">Contained Button</Button>
                <Button mode="outlined">Outlined Button</Button>
                <Button mode="text">Text Button</Button>
                <Button mode="contained-tonal">Contained Tonal</Button>
                <Button mode="elevated">Elevated Button</Button>
              </View>
            </Card.Content>
          </Card>

          {/* 文本输入 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                文本输入
              </Text>
              <TextInput
                mode="outlined"
                label="测试输入"
                value={textValue}
                onChangeText={setTextValue}
                left={<TextInput.Icon icon="magnify" />}
                right={<TextInput.Icon icon="close" />}
              />
            </Card.Content>
          </Card>

          {/* 分段按钮 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                分段按钮
              </Text>
              <SegmentedButtons
                value={segmentedValue}
                onValueChange={setSegmentedValue}
                buttons={[
                  { value: 'option1', label: '选项1', icon: 'home' },
                  { value: 'option2', label: '选项2', icon: 'star' },
                  { value: 'option3', label: '选项3', icon: 'heart' },
                ]}
              />
            </Card.Content>
          </Card>

          {/* 加载指示器 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                加载指示器
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: theme.spacing?.md || 16 }}>
                <ActivityIndicator size="small" />
                <ActivityIndicator size="large" />
                <Icon source="heart" size={24} color={theme.colors.primary} />
              </View>
            </Card.Content>
          </Card>

          {/* 字体展示 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                字体层级
              </Text>
              <View style={{ gap: theme.spacing?.xs || 4 }}>
                <Text variant="displayLarge">Display Large</Text>
                <Text variant="headlineLarge">Headline Large</Text>
                <Text variant="titleLarge">Title Large</Text>
                <Text variant="bodyLarge">Body Large</Text>
                <Text variant="labelLarge">Label Large</Text>
              </View>
            </Card.Content>
          </Card>

          {/* 间距展示 */}
          <Card mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={{ marginBottom: theme.spacing?.sm || 8 }}>
                间距系统
              </Text>
              <Text variant="bodyMedium">
                XS: {theme.spacing?.xs}px, SM: {theme.spacing?.sm}px, 
                MD: {theme.spacing?.md}px, LG: {theme.spacing?.lg}px, 
                XL: {theme.spacing?.xl}px, XXL: {theme.spacing?.xxl}px
              </Text>
              <Text variant="bodyMedium">
                圆角: {theme.roundness}px
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    </Surface>
  );
}
