import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      alignItems: 'center',
    },
    rankContainer: {
      width: 30,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing.sm,
    },
    rankText: {
      fontFamily: theme.fonts.bold,
      fontSize: 18,
    },
    imageContainer: {
      marginRight: theme.spacing.md,
    },
    image: {
      width: 60,
      height: 60,
      borderRadius: theme.borderRadius.sm,
    },
    placeholderImage: {
      backgroundColor: theme.colors.border,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'space-between',
    },
    title: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    authorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    authorName: {
      fontFamily: theme.fonts.regular,
      fontSize: 14,
      color: theme.colors.secondaryText,
      marginRight: theme.spacing.sm,
    },
    date: {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.tertiaryText,
    },
    statsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    statText: {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.secondaryText,
      marginLeft: theme.spacing.xs,
    },
  });