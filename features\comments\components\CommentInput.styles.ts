import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      marginBottom: theme.spacing.md,
    },
    input: {
      fontFamily: theme.fonts.regular,
      fontSize: 15,
      color: theme.colors.text,
      minHeight: 80,
      textAlignVertical: 'top',
      padding: theme.spacing.sm,
    },
    actions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    cancelButton: {
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.md,
      marginRight: theme.spacing.sm,
    },
    cancelText: {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    submitButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
    },
    submitButtonDisabled: {
      opacity: 0.6,
    },
    submitText: {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.white,
      marginLeft: theme.spacing.xs,
    },
  });