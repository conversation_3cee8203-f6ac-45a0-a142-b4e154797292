import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
      alignItems: 'center',
    },
    rankContainer: {
      width: 30,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing.sm,
    },
    rankText: {
      fontFamily: theme.fonts.bold,
      fontSize: 18,
    },
    avatarContainer: {
      marginRight: theme.spacing.md,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'space-between',
    },
    username: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    fullName: {
      fontFamily: theme.fonts.regular,
      fontSize: 14,
      color: theme.colors.secondaryText,
      marginBottom: theme.spacing.xs,
    },
    statsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    statText: {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.secondaryText,
      marginLeft: theme.spacing.xs,
    },
  });