import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { StoryWithSegments, StorySegment } from '@/api/stories';
import { createDynamicStyles } from '../screens/StoryDetailScreen.styles';

// Components
import StoryDetailHeader from './StoryDetailHeader';
import StorySegmentItem from './StorySegmentItem';
import AddSegmentForm from './AddSegmentForm';
import AISuggestionBlock from './AISuggestionBlock';
import BranchManager from './BranchManager';

interface StoryDetailContentProps {
  story: StoryWithSegments;
  segments: StorySegment[];
  isLiked: boolean;
  likeCount: number;
  isLiking: boolean;
  newSegmentContent: string;
  isSubmittingSegment: boolean;
  showAISegmentSuggestions: boolean;
  loadingAISegmentSuggestions: boolean;
  aiSegmentSuggestions: string[];
  currentSegmentId?: string;
  hasMoreSegments: boolean;
  isLoadingMore: boolean;
  onLoadMoreSegments: () => void;
  onLikeToggle: () => void;
  onContentChange: (text: string) => void;
  onSubmitSegment: () => void;
  onFetchAISuggestions: () => void;
  onSelectAISuggestion: (suggestion: string) => void;
  onBranchChange?: (segmentId: string) => void;
}

export function StoryDetailContent({
  story,
  segments,
  isLiked,
  likeCount,
  isLiking,
  newSegmentContent,
  isSubmittingSegment,
  showAISegmentSuggestions,
  loadingAISegmentSuggestions,
  aiSegmentSuggestions,
  currentSegmentId,
  hasMoreSegments,
  isLoadingMore,
  onLoadMoreSegments,
  onLikeToggle,
  onContentChange,
  onSubmitSegment,
  onFetchAISuggestions,
  onSelectAISuggestion,
  onBranchChange,
}: StoryDetailContentProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);

  // 获取AI建议
  const handleRequestAiSuggestion = async (): Promise<string | null> => {
    if (loadingAISegmentSuggestions) return null;

    onFetchAISuggestions();

    // 等待AI建议生成
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (!loadingAISegmentSuggestions && aiSegmentSuggestions.length > 0) {
          clearInterval(checkInterval);
          resolve(aiSegmentSuggestions[0]);
        } else if (!loadingAISegmentSuggestions) {
          clearInterval(checkInterval);
          resolve(null);
        }
      }, 500);
    });
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      keyboardShouldPersistTaps="handled"
    >
      <StoryDetailHeader
        story={story}
        isLiked={isLiked}
        likeCount={likeCount}
        onLikeToggle={onLikeToggle}
        isLiking={isLiking}
      />

      {/* 分支管理器 */}
      <View style={styles.branchManagerContainer}>
        <BranchManager
          storyId={story.id}
          initialSegmentId={currentSegmentId || segments[0]?.id}
          onBranchChange={onBranchChange}
          onRequestAiSuggestion={handleRequestAiSuggestion}
        />
      </View>

      {/* 调试信息 */}
      <View
        style={{ padding: 10, backgroundColor: '#f0f0f0', marginVertical: 10 }}
      >
        <Text style={{ fontWeight: 'bold' }}>调试信息:</Text>
        <Text>当前段落ID: {currentSegmentId || '无'}</Text>
        <Text>段落数量: {segments.length}</Text>
        <Text>
          段落IDs: {segments.map((s) => s.id.substring(0, 6)).join(', ')}
        </Text>
        <Text>段落关系:</Text>
        {segments.map((segment, index) => (
          <Text key={segment.id}>
            {index + 1}. ID: {segment.id.substring(0, 6)}, 父ID:{' '}
            {segment.parent_segment_id
              ? segment.parent_segment_id.substring(0, 6)
              : 'ROOT'}
            , 子数量:{' '}
            {typeof segment.children_count === 'number'
              ? segment.children_count
              : '未知'}
          </Text>
        ))}
      </View>

      <View style={styles.segmentsSection}>
        <Text style={styles.sectionTitle}>
          {t('storyDetail.storyContent', 'Story Content')}
        </Text>
        {segments && segments.length > 0 ? (
          <View style={{ height: Math.min(segments.length * 200, 2000) }}>
            <FlashList
              data={segments}
              renderItem={({ item, index }) => (
                <StorySegmentItem
                  segment={item}
                  isFirst={index === 0}
                  isLast={index === segments.length - 1}
                  showDivider={index > 0}
                />
              )}
              keyExtractor={(item) => item.id.toString()}
              estimatedItemSize={200}
              scrollEnabled={true} // 允许滚动
              optimizeItemArrangement={true} // 优化项目排列
              drawDistance={400} // 提前渲染的距离
              onEndReached={hasMoreSegments ? onLoadMoreSegments : undefined}
              onEndReachedThreshold={0.5} // 当距离底部50%时触发加载更多
              ListFooterComponent={
                isLoadingMore ? (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <ActivityIndicator
                      size="small"
                      color={theme.colors.primary}
                    />
                    <Text
                      style={{
                        marginTop: 10,
                        color: theme.colors.secondaryText,
                      }}
                    >
                      {t('storyDetail.loadingMore', 'Loading more...')}
                    </Text>
                  </View>
                ) : hasMoreSegments ? (
                  <TouchableOpacity
                    style={{
                      padding: 15,
                      alignItems: 'center',
                      backgroundColor: theme.colors.cardBackground,
                      borderRadius: 8,
                      margin: 10,
                    }}
                    onPress={onLoadMoreSegments}
                  >
                    <Text style={{ color: theme.colors.primary }}>
                      {t('storyDetail.loadMore', 'Load more')}
                    </Text>
                  </TouchableOpacity>
                ) : null
              }
              overrideItemLayout={(layout, item) => {
                // 可以根据内容长度动态调整高度
                layout.size = Math.max(
                  150,
                  Math.min(300, item.content.length / 2)
                );
              }}
            />
          </View>
        ) : (
          <Text style={styles.noSegmentsText}>
            {t(
              'storyDetail.noContent',
              'This story has no content yet. Be the first to add to it!'
            )}
          </Text>
        )}
      </View>

      <AddSegmentForm
        segmentContent={newSegmentContent}
        onContentChange={onContentChange}
        onSubmit={onSubmitSegment}
        isSubmitting={isSubmittingSegment}
        onCreateBranchClick={() => {
          // 当用户点击创建分支按钮时，显示BranchManager组件的创建分支表单
          // 这里可以通过ref或其他方式调用BranchManager组件的方法
          // 或者通过状态控制CreateBranchForm组件的显示
          if (currentSegmentId) {
            // 这里可以实现显示创建分支表单的逻辑
          }
        }}
      />

      <AISuggestionBlock
        onFetchSuggestions={onFetchAISuggestions}
        loadingSuggestions={loadingAISegmentSuggestions}
        showSuggestions={showAISegmentSuggestions}
        suggestions={aiSegmentSuggestions}
        onSelectSuggestion={onSelectAISuggestion}
        isProcessingSegment={isSubmittingSegment}
      />
    </ScrollView>
  );
}
