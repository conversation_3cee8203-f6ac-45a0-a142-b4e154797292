import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { Text } from 'react-native';
import HeaderBar from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Mock expo-router
const mockBack = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    back: mockBack,
  }),
}));

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>
    {children}
  </PaperProvider>
);

describe('HeaderBar', () => {
  beforeEach(() => {
    mockBack.mockClear();
  });

  it('renders with title only', () => {
    const { getByText } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" />
      </TestWrapper>
    );

    expect(getByText('Test Title')).toBeTruthy();
  });

  it('renders with title and subtitle', () => {
    const { getByText } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" subtitle="Test Subtitle" />
      </TestWrapper>
    );

    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Test Subtitle')).toBeTruthy();
  });

  it('shows back button when showBackButton is true', () => {
    const { getByLabelText } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" showBackButton={true} />
      </TestWrapper>
    );

    // react-native-paper Appbar.BackAction has default accessibility label "Back"
    expect(getByLabelText('Back')).toBeTruthy();
  });

  it('does not show back button when showBackButton is false', () => {
    const { queryByLabelText } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" showBackButton={false} />
      </TestWrapper>
    );

    expect(queryByLabelText('Back')).toBeNull();
  });

  it('calls router.back when back button is pressed', () => {
    const { getByLabelText } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" showBackButton={true} />
      </TestWrapper>
    );

    fireEvent.press(getByLabelText('Back'));
    expect(mockBack).toHaveBeenCalledTimes(1);
  });

  it('renders right element when provided', () => {
    const rightElement = <Text testID="right-element">Right Element</Text>;
    const { getByTestId } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" rightElement={rightElement} />
      </TestWrapper>
    );

    expect(getByTestId('right-element')).toBeTruthy();
  });

  it('does not render right element when not provided', () => {
    const { queryByTestId } = render(
      <TestWrapper>
        <HeaderBar title="Test Title" />
      </TestWrapper>
    );

    expect(queryByTestId('right-element')).toBeNull();
  });

  it('renders with all props combined', () => {
    const rightElement = <Text testID="right-element">Right Element</Text>;
    const { getByText, getByLabelText, getByTestId } = render(
      <TestWrapper>
        <HeaderBar 
          title="Test Title" 
          subtitle="Test Subtitle"
          showBackButton={true}
          rightElement={rightElement}
        />
      </TestWrapper>
    );

    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Test Subtitle')).toBeTruthy();
    expect(getByLabelText('Back')).toBeTruthy();
    expect(getByTestId('right-element')).toBeTruthy();
  });

  it('handles empty title gracefully', () => {
    const { getByText } = render(
      <TestWrapper>
        <HeaderBar title="" />
      </TestWrapper>
    );

    // Should still render the component even with empty title
    // The exact behavior depends on how react-native-paper handles empty titles
    expect(() => getByText('')).not.toThrow();
  });
});
