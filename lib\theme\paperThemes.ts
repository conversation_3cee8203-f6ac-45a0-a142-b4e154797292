import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import type { MD3Theme } from 'react-native-paper';

/**
 * 简化的 Paper 主题配置
 * 基于 Material Design 3 最佳实践
 */

// 自定义颜色配置
const customColors = {
  light: {
    primary: 'rgb(103, 80, 164)',
    onPrimary: 'rgb(255, 255, 255)',
    primaryContainer: 'rgb(234, 221, 255)',
    onPrimaryContainer: 'rgb(33, 0, 93)',
    secondary: 'rgb(98, 91, 113)',
    onSecondary: 'rgb(255, 255, 255)',
    secondaryContainer: 'rgb(232, 222, 248)',
    onSecondaryContainer: 'rgb(29, 25, 43)',
    tertiary: 'rgb(125, 82, 96)',
    onTertiary: 'rgb(255, 255, 255)',
    tertiaryContainer: 'rgb(255, 216, 228)',
    onTertiaryContainer: 'rgb(50, 16, 29)',
    // AI 相关特殊颜色
    aiAction: '#1EB999',
    onAiAction: '#FFFFFF',
  },
  dark: {
    primary: 'rgb(208, 188, 255)',
    onPrimary: 'rgb(54, 22, 116)',
    primaryContainer: 'rgb(78, 51, 139)',
    onPrimaryContainer: 'rgb(234, 221, 255)',
    secondary: 'rgb(204, 194, 220)',
    onSecondary: 'rgb(50, 46, 65)',
    secondaryContainer: 'rgb(73, 68, 88)',
    onSecondaryContainer: 'rgb(232, 222, 248)',
    tertiary: 'rgb(225, 187, 200)',
    onTertiary: 'rgb(73, 37, 50)',
    tertiaryContainer: 'rgb(99, 59, 72)',
    onTertiaryContainer: 'rgb(255, 216, 228)',
    // AI 相关特殊颜色
    aiAction: '#1EB999',
    onAiAction: '#FFFFFF',
  },
};

// 简化的间距配置
const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// 创建简化的 Paper 主题
export const paperLightTheme: MD3Theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...customColors.light,
  },
  roundness: 12,
  spacing,
};

// 创建暗色主题
export const paperDarkTheme: MD3Theme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...customColors.dark,
  },
  roundness: 12,
  spacing,
};

// 导出类型
export type AppPaperTheme = typeof paperLightTheme;
