import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { StoryTheme } from '@/types/story';
import {
  BookOpen,
  Rocket,
  Wand,
  Landmark,
  Heart,
  Search,
  Building,
  Check,
} from 'lucide-react-native';
import { createStyles } from './styles';

interface ThemeSelectionCardProps {
  theme: StoryTheme;
  isSelected: boolean;
  onSelect: () => void;
}

export default function ThemeSelectionCard({
  theme: themeProp,
  isSelected,
  onSelect,
}: ThemeSelectionCardProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  const renderIcon = () => {
    const iconColor = isSelected
      ? '#FFFFFF'
      : themeProp.color || appTheme.colors.primary;
    const size = 24;

    switch (themeProp.icon) {
      case 'rocket':
        return <Rocket size={size} color={iconColor} />;
      case 'wand':
        return <Wand size={size} color={iconColor} />;
      case 'search':
        return <Search size={size} color={iconColor} />;
      case 'heart':
        return <Heart size={size} color={iconColor} />;
      case 'landmark':
        return <Landmark size={size} color={iconColor} />;
      case 'building':
        return <Building size={size} color={iconColor} />;
      default:
        return <BookOpen size={size} color={iconColor} />;
    }
  };

  const cardBackgroundColor = isSelected
    ? themeProp.color
    : appTheme.colors.card;
  const textColor = isSelected ? '#FFFFFF' : appTheme.colors.text;
  const descriptionColor = isSelected
    ? '#FFFFFF'
    : appTheme.colors.secondaryText;

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: cardBackgroundColor,
          borderColor: themeProp.color || appTheme.colors.border,
        },
        appTheme.shadows.sm,
      ]}
      onPress={onSelect}
    >
      <Text style={[styles.themeName, { color: textColor }]}>
        {themeProp.name}
      </Text>

      {renderIcon()}

      <Text
        style={[styles.themeDescription, { color: descriptionColor }]}
        numberOfLines={2}
      >
        {themeProp.description}
      </Text>

      {isSelected && (
        <Check size={16} color="#FFFFFF" style={styles.checkmark} />
      )}
    </TouchableOpacity>
  );
}
