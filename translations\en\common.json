{"appName": "<PERSON><PERSON><PERSON><PERSON>", "common": {"submit": "Submit", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemMode": "System Default"}, "tabs": {"home": "Explore", "create": "Create", "stories": "Stories", "social": "Social", "profile": "Profile"}, "premium": "Premium", "loading": "Loading...", "validation": {"required": "This field is required", "emailFormat": "Invalid email address", "minLength": "Must be at least {{count}} characters", "maxLength": "Cannot exceed {{count}} characters"}, "errors": {"general": "Something went wrong. Please try again.", "networkError": "Network error. Please check your connection.", "unauthorized": "You are not authorized to perform this action."}, "success": "Success!", "tryAgain": "Try Again", "error": "Error", "validationError": "Input Error", "emptyStateGlobal": "Nothing to see here yet. Start by exploring or creating!", "aiSuggestions.promptErrorTitle": "Hint", "aiSuggestions.promptError": "Please enter a story title or some content for AI to provide suggestions.", "aiSuggestions.fetchErrorTitle": "Failed to Get Suggestions", "aiSuggestions.fetchError": "Could not fetch AI suggestions. Please try again later.", "storyForm.getAISuggestions": "Get AI Suggestions", "aiSuggestions.title": "AI Suggestions:", "aiSuggestions.noSuggestions": "No suitable suggestions at the moment. Try modifying your input or try again later.", "aiSuggestions.promptErrorSegment": "Some existing content is needed to generate continuation suggestions.", "storyDetail.getAISegmentSuggestions": "AI Story Continuation"}