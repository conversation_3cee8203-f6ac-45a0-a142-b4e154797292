import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { Conversation } from '@/api/messages/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';

interface ConversationItemProps {
  conversation: Conversation;
  onPress: (conversation: Conversation) => void;
}

export default function ConversationItem({
  conversation,
  onPress,
}: ConversationItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t, i18n } = useTranslation();

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 处理对话点击
  const handlePress = () => {
    onPress(conversation);
  };

  // 获取对话的另一个参与者
  const otherParticipant = conversation.other_participant;

  return (
    <TouchableOpacity
      style={[
        styles.container,
        conversation.unread_count && conversation.unread_count > 0 && styles.unreadContainer,
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {otherParticipant && (
        <View style={styles.avatarContainer}>
          {otherParticipant.avatar_url ? (
            <Image
              source={{ uri: otherParticipant.avatar_url }}
              style={styles.avatar}
            />
          ) : (
            <View style={[styles.avatar, styles.placeholderAvatar]} />
          )}
          {conversation.unread_count && conversation.unread_count > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>
                {conversation.unread_count > 99 ? '99+' : conversation.unread_count}
              </Text>
            </View>
          )}
        </View>
      )}

      <View style={styles.contentContainer}>
        <View style={styles.headerContainer}>
          <Text style={styles.name} numberOfLines={1}>
            {otherParticipant?.display_name || otherParticipant?.username || t('messages.unknownUser', '未知用户')}
          </Text>
          <Text style={styles.time}>
            {formatTime(conversation.last_message_at)}
          </Text>
        </View>

        <Text style={styles.message} numberOfLines={1}>
          {conversation.last_message || t('messages.noMessages', '暂无消息')}
        </Text>
      </View>
    </TouchableOpacity>
  );
}