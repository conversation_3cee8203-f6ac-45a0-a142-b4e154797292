import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { BookOpen, Users } from 'lucide-react-native';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';
import { SearchTab } from '../../hooks/useSearch';

interface SearchTabsProps {
  activeTab: SearchTab;
  onTabChange: (tab: SearchTab) => void;
  storiesCount: number;
  usersCount: number;
}

export default function SearchTabs({
  activeTab,
  onTabChange,
  storiesCount,
  usersCount,
}: SearchTabsProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  if (storiesCount === 0 && usersCount === 0) {
    return null;
  }

  return (
    <View style={styles.tabsContainer}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'all' && [
            styles.activeTab,
            { borderBottomColor: theme.colors.primary },
          ],
        ]}
        onPress={() => onTabChange('all')}
      >
        <Text
          style={[
            styles.tabText,
            {
              color:
                activeTab === 'all'
                  ? theme.colors.primary
                  : theme.colors.secondaryText,
            },
          ]}
        >
          {t('social.search.all', '全部')}
        </Text>
      </TouchableOpacity>

      {storiesCount > 0 && (
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'stories' && [
              styles.activeTab,
              { borderBottomColor: theme.colors.primary },
            ],
          ]}
          onPress={() => onTabChange('stories')}
        >
          <BookOpen
            size={16}
            color={
              activeTab === 'stories'
                ? theme.colors.primary
                : theme.colors.secondaryText
            }
          />
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === 'stories'
                    ? theme.colors.primary
                    : theme.colors.secondaryText,
              },
            ]}
          >
            {t('social.search.stories', '故事')} ({storiesCount})
          </Text>
        </TouchableOpacity>
      )}

      {usersCount > 0 && (
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'users' && [
              styles.activeTab,
              { borderBottomColor: theme.colors.primary },
            ],
          ]}
          onPress={() => onTabChange('users')}
        >
          <Users
            size={16}
            color={
              activeTab === 'users'
                ? theme.colors.primary
                : theme.colors.secondaryText
            }
          />
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === 'users'
                    ? theme.colors.primary
                    : theme.colors.secondaryText,
              },
            ]}
          >
            {t('social.search.users', '用户')} ({usersCount})
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
