import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { StorySegment } from '@/api/stories';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { ChevronRight, Home, GitBranch } from 'lucide-react-native';

interface BranchNavigatorProps {
  currentPath: StorySegment[];
  currentChildren: StorySegment[];
  onBranchSelect: (segmentId: string) => void;
  onRootSelect: () => void;
  isLoadingMore?: boolean;
  hasMoreChildren?: boolean;
  onLoadMoreChildren?: () => void;
}

export default function BranchNavigator({
  currentPath,
  currentChildren,
  onBranchSelect,
  onRootSelect,
  isLoadingMore = false,
  hasMoreChildren = false,
  onLoadMoreChildren,
}: BranchNavigatorProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // 格式化段落预览
  const formatPreview = (content: string, maxLength = 30) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // 渲染面包屑导航
  const renderBreadcrumbs = () => {
    if (currentPath.length === 0) {
      return (
        <View style={styles.breadcrumbContainer}>
          <TouchableOpacity
            style={[styles.breadcrumbItem, styles.breadcrumbItemActive]}
            onPress={onRootSelect}
          >
            <Home size={16} color={theme.colors.primary} />
            <Text style={[styles.breadcrumbText, styles.breadcrumbTextActive]}>
              {t('storyDetail.mainBranch', 'Main Branch')}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.breadcrumbContainer}
        contentContainerStyle={styles.breadcrumbContent}
      >
        <TouchableOpacity style={styles.breadcrumbItem} onPress={onRootSelect}>
          <Home size={16} color={theme.colors.text} />
          <Text style={styles.breadcrumbText}>
            {t('storyDetail.mainBranch', 'Main Branch')}
          </Text>
        </TouchableOpacity>

        {currentPath.map((segment, index) => {
          const isLast = index === currentPath.length - 1;
          return (
            <React.Fragment key={segment.id}>
              <ChevronRight
                size={16}
                color={theme.colors.secondaryText}
                style={styles.breadcrumbSeparator}
              />
              <TouchableOpacity
                style={[
                  styles.breadcrumbItem,
                  isLast && styles.breadcrumbItemActive,
                ]}
                onPress={() => onBranchSelect(segment.id)}
              >
                <GitBranch
                  size={16}
                  color={isLast ? theme.colors.primary : theme.colors.text}
                />
                <Text
                  style={[
                    styles.breadcrumbText,
                    isLast && styles.breadcrumbTextActive,
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {formatPreview(segment.content, 20)}
                </Text>
              </TouchableOpacity>
            </React.Fragment>
          );
        })}
      </ScrollView>
    );
  };

  // 渲染子分支列表
  const renderChildBranches = () => {
    console.log(
      'BranchNavigator - currentChildren:',
      currentChildren?.length || 0
    );

    // 确保currentChildren是数组
    const childrenToRender = Array.isArray(currentChildren)
      ? currentChildren
      : [];

    if (childrenToRender.length === 0) {
      return (
        <View style={styles.emptyChildrenContainer}>
          <Text style={styles.emptyChildrenText}>
            {t('storyDetail.noBranches', 'No branches available')}
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={childrenToRender}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.childrenListContent}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.childBranchItem}
            onPress={() => {
              console.log('BranchNavigator - child branch selected:', item.id);
              onBranchSelect(item.id);
            }}
          >
            <View style={styles.childBranchHeader}>
              <GitBranch size={16} color={theme.colors.text} />
              <Text style={styles.childBranchTitle} numberOfLines={1}>
                {t('storyDetail.branch', 'Branch')} {item.order_in_branch + 1}
              </Text>
            </View>
            <Text style={styles.childBranchPreview} numberOfLines={3}>
              {formatPreview(item.content, 80)}
            </Text>
            <View style={styles.childBranchFooter}>
              <Text style={styles.childBranchAuthor}>
                {item.profiles?.username ||
                  t('storyDetail.unknownAuthor', 'Unknown')}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        onEndReached={() => {
          if (hasMoreChildren && onLoadMoreChildren && !isLoadingMore) {
            onLoadMoreChildren();
          }
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={
          isLoadingMore ? (
            <View style={styles.loadingMoreContainer}>
              <ActivityIndicator size="small" color={theme.colors.primary} />
              <Text style={styles.loadingMoreText}>
                {t('storyDetail.loadingMoreBranches', 'Loading more...')}
              </Text>
            </View>
          ) : hasMoreChildren ? (
            <TouchableOpacity
              style={styles.loadMoreButton}
              onPress={onLoadMoreChildren}
              disabled={isLoadingMore}
            >
              <Text style={styles.loadMoreButtonText}>
                {t('storyDetail.loadMoreBranches', 'Load more')}
              </Text>
            </TouchableOpacity>
          ) : null
        }
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.navigatorSection}>
        <Text style={styles.sectionTitle}>
          {t('storyDetail.currentPath', 'Current Path')}
        </Text>
        {renderBreadcrumbs()}
      </View>

      <View style={styles.navigatorSection}>
        <Text style={styles.sectionTitle}>
          {t('storyDetail.availableBranches', 'Available Branches')}
        </Text>
        {renderChildBranches()}
      </View>
    </View>
  );
}
