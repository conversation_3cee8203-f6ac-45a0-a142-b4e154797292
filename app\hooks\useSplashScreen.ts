import { useEffect } from 'react';
import { SplashScreen } from 'expo-router';

interface UseSplashScreenProps {
  fontsLoaded: boolean;
  fontError: Error | null;
  settingsHasHydrated: boolean;
  authHasHydrated: boolean;
  authIsInitialized: boolean;
}

export default function useSplashScreen({
  fontsLoaded,
  fontError,
  settingsHasHydrated,
  authHasHydrated,
  authIsInitialized,
}: UseSplashScreenProps) {
  // Hide splash screen when fonts are loaded and auth state is initialized & settings store hydrated
  useEffect(() => {
    if (
      (fontsLoaded || fontError) &&
      settingsHasHydrated &&
      authHasHydrated &&
      authIsInitialized
    ) {
      SplashScreen.hideAsync();
    }
  }, [
    fontsLoaded,
    fontError,
    settingsHasHydrated,
    authHasHydrated,
    authIsInitialized,
  ]);

  // Check if we should keep showing the splash screen
  const shouldShowSplash =
    (!fontsLoaded && !fontError) ||
    !settingsHasHydrated ||
    !authHasHydrated ||
    !authIsInitialized;

  return { shouldShowSplash };
}
