import React from 'react';
import { View } from 'react-native';
import { Text, Button, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

interface EmptyStoriesStateProps {
  title?: string;
  subtitle?: string;
  actionText?: string;
  onActionPress?: () => void;
  icon?: React.ReactNode; // Allow custom icon
}

export function EmptyStoriesState({
  title,
  subtitle,
  actionText,
  onActionPress,
  icon,
}: EmptyStoriesStateProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  const defaultIcon = (
    <Icon source="pencil" size={48} color={theme.colors.onSurfaceDisabled} />
  );
  const displayedIcon = icon || defaultIcon;

  // Default texts can use translation keys
  const displayedTitle = title || t('emptyStoriesTitle');
  const displayedSubtitle = subtitle || t('emptyStoriesSubtitle');
  const displayedActionText = actionText || t('startCreatingButton');

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: theme.spacing?.xl || 32,
      }}
    >
      <View style={{ marginBottom: theme.spacing?.lg || 24 }}>
        {displayedIcon}
      </View>

      <Text
        variant="headlineSmall"
        style={{
          color: theme.colors.onSurface,
          textAlign: 'center',
          marginBottom: theme.spacing?.sm || 8,
        }}
      >
        {displayedTitle}
      </Text>

      <Text
        variant="bodyMedium"
        style={{
          color: theme.colors.onSurfaceVariant,
          textAlign: 'center',
          marginBottom: theme.spacing?.xl || 32,
          lineHeight: 22,
        }}
      >
        {displayedSubtitle}
      </Text>

      {onActionPress && (
        <Button
          mode="contained"
          icon="plus"
          onPress={onActionPress}
          style={{ marginTop: theme.spacing?.md || 16 }}
        >
          {displayedActionText}
        </Button>
      )}
    </View>
  );
}
