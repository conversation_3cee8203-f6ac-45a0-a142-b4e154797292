import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './EmptyStoriesState.styles';
import { PenLine } from 'lucide-react-native'; // Or another relevant icon
import { useTranslation } from 'react-i18next';

interface EmptyStoriesStateProps {
  title?: string;
  subtitle?: string;
  actionText?: string;
  onActionPress?: () => void;
  icon?: React.ReactNode; // Allow custom icon
}

export function EmptyStoriesState({
  title,
  subtitle,
  actionText,
  onActionPress,
  icon,
}: EmptyStoriesStateProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const defaultIcon = <PenLine size={48} color={theme.colors.secondaryText} />;
  const displayedIcon = icon || defaultIcon;

  // Default texts can use translation keys
  const displayedTitle = title || t('emptyStoriesTitle');
  const displayedSubtitle = subtitle || t('emptyStoriesSubtitle');
  const displayedActionText = actionText || t('startCreatingButton');

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        {displayedIcon}
      </View>
      <Text style={styles.title}>{displayedTitle}</Text>
      <Text style={styles.subtitle}>{displayedSubtitle}</Text>
      {onActionPress && (
        <TouchableOpacity 
          style={styles.button} 
          onPress={onActionPress}
        >
          <PenLine size={20} color={theme.dark ? theme.colors.background : '#FFF'} />
          <Text style={styles.buttonText}>{displayedActionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
} 