# React Native Paper 主题系统使用指南

## 概述

本项目采用了基于 Material Design 3 的 React Native Paper 主题系统，遵循最佳实践，提供简洁、一致的用户界面体验。

## 主题架构

### 1. 主题提供者 (ThemeProvider)

位置：`lib/theme/ThemeProvider.tsx`

特点：
- 集成了 Paper 主题和 Navigation 主题
- 支持亮色/暗色/系统主题切换
- 使用 `adaptNavigationTheme` 和 `deepmerge` 实现主题合并

### 2. 主题配置 (paperThemes.ts)

位置：`lib/theme/paperThemes.ts`

特点：
- 基于 MD3 标准颜色系统
- 简化配置（仅 80 行代码）
- 包含自定义间距和圆角配置

## 使用方法

### 1. 在组件中使用主题

```typescript
import { usePaperTheme } from '@/hooks/usePaperTheme';

function MyComponent() {
  const theme = usePaperTheme();
  
  return (
    <View style={{ 
      padding: theme.spacing?.md,
      backgroundColor: theme.colors.surface 
    }}>
      <Text style={{ color: theme.colors.onSurface }}>
        Hello World
      </Text>
    </View>
  );
}
```

### 2. 使用 Paper 组件

```typescript
import { Surface, Text, Button, Card } from 'react-native-paper';

function MyPaperComponent() {
  return (
    <Surface style={{ flex: 1 }}>
      <Card mode="elevated">
        <Card.Content>
          <Text variant="headlineSmall">标题</Text>
          <Text variant="bodyMedium">内容</Text>
          <Button mode="contained">按钮</Button>
        </Card.Content>
      </Card>
    </Surface>
  );
}
```

## 主题配置

### 颜色系统

- **Primary**: `rgb(103, 80, 164)` (亮色) / `rgb(208, 188, 255)` (暗色)
- **Secondary**: `rgb(98, 91, 113)` (亮色) / `rgb(204, 194, 220)` (暗色)
- **Tertiary**: `rgb(125, 82, 96)` (亮色) / `rgb(225, 187, 200)` (暗色)
- **AI Action**: `#1EB999` (特殊用途)

### 间距系统

```typescript
spacing: {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
}
```

### 圆角配置

- **roundness**: `12px`

## 最佳实践

### 1. 优先使用 Paper 组件

```typescript
// ✅ 推荐
import { Text, Button } from 'react-native-paper';

// ❌ 避免
import { Text, TouchableOpacity } from 'react-native';
```

### 2. 使用语义化颜色

```typescript
// ✅ 推荐
backgroundColor: theme.colors.surface
color: theme.colors.onSurface

// ❌ 避免
backgroundColor: '#FFFFFF'
color: '#000000'
```

### 3. 使用主题间距

```typescript
// ✅ 推荐
padding: theme.spacing?.md

// ❌ 避免
padding: 16
```

### 4. 使用 Text 变体

```typescript
// ✅ 推荐
<Text variant="headlineSmall">标题</Text>
<Text variant="bodyMedium">正文</Text>

// ❌ 避免
<Text style={{ fontSize: 20, fontWeight: 'bold' }}>标题</Text>
```

## 主题切换

### 在设置中切换主题

```typescript
import { useSettingsStore } from '@/lib/store/settingsStore';

function ThemeSettings() {
  const { themeMode, setThemeMode } = useSettingsStore();
  
  return (
    <SegmentedButtons
      value={themeMode}
      onValueChange={setThemeMode}
      buttons={[
        { value: 'light', label: '亮色' },
        { value: 'dark', label: '暗色' },
        { value: 'system', label: '系统' },
      ]}
    />
  );
}
```

## 测试

### 主题测试页面

访问 `/test/paper-theme` 查看所有主题组件的展示效果。

### 单元测试

运行 `npm test paperThemes.test.tsx` 验证主题配置。

## 扩展主题

### 添加自定义颜色

在 `paperThemes.ts` 中的 `customColors` 对象中添加：

```typescript
const customColors = {
  light: {
    // 现有颜色...
    customColor: '#FF5722',
  },
  dark: {
    // 现有颜色...
    customColor: '#FF8A65',
  },
};
```

### 修改间距

在 `spacing` 对象中调整：

```typescript
const spacing = {
  xs: 4,
  sm: 8,
  md: 16,  // 可以修改为其他值
  lg: 24,
  xl: 32,
  xxl: 48,
};
```

## 故障排除

### 1. 主题不生效

确保组件被 `PaperProvider` 包裹：

```typescript
<PaperProvider theme={theme}>
  <YourComponent />
</PaperProvider>
```

### 2. 颜色显示不正确

检查是否使用了正确的颜色属性：

```typescript
// 正确
theme.colors.primary
theme.colors.onPrimary

// 错误
theme.colors.primaryColor
```

### 3. 间距不一致

统一使用主题间距：

```typescript
// 正确
marginTop: theme.spacing?.lg

// 错误
marginTop: 20
```
