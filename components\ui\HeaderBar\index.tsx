import React from 'react';
import { View } from 'react-native';
import { Appbar, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface HeaderBarProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  rightElement?: React.ReactNode;
}

export default function HeaderBar({
  title,
  subtitle,
  showBackButton = false,
  rightElement,
}: HeaderBarProps) {
  const theme = usePaperTheme();
  const router = useRouter();

  // Create custom title content to handle subtitle since it's deprecated in v5.x
  const titleContent = subtitle ? (
    <View>
      <Text variant="titleLarge" style={{ color: theme.colors.onSurface }}>
        {title}
      </Text>
      <Text
        variant="bodySmall"
        style={{ color: theme.colors.onSurfaceVariant }}
      >
        {subtitle}
      </Text>
    </View>
  ) : (
    title
  );

  return (
    <Appbar.Header>
      {showBackButton && <Appbar.BackAction onPress={() => router.back()} />}

      <Appbar.Content title={titleContent} />

      {rightElement && rightElement}
    </Appbar.Header>
  );
}
