import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSettingsStore } from '@/lib/store/settingsStore';
import { OptionsGroup, Option } from '@/components/shared/OptionsGroup'; // Import shared component

export function LanguageOptionsGroup() {
  const { t } = useTranslation();
  const language = useSettingsStore((state) => state.language);
  const setLanguage = useSettingsStore((state) => state.setLanguage);

  // Define language options conforming to the Option interface
  const languageOptions: Option<string>[] = [
    { value: 'en', label: t('english') },
    { value: 'zh', label: t('chinese') },
    // Add more languages here following the same pattern
  ];

  return (
    <OptionsGroup<string>
      options={languageOptions}
      selectedValue={language}
      onSelect={setLanguage}
    />
  );
} 