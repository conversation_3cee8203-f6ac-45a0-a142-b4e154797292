import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { StoryListTabs } from './StoryListTabs';
import { paperLightTheme } from '@/lib/theme/paperThemes';

const mockProps = {
  tabs: ['推荐', '热门', '最新', '关注'],
  activeTab: '推荐',
  onTabPress: jest.fn(),
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <PaperProvider theme={paperLightTheme}>
      {component}
    </PaperProvider>
  );
};

describe('StoryListTabs', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all tabs correctly', () => {
    const { getByText } = renderWithTheme(
      <StoryListTabs {...mockProps} />
    );

    expect(getByText('推荐')).toBeTruthy();
    expect(getByText('热门')).toBeTruthy();
    expect(getByText('最新')).toBeTruthy();
    expect(getByText('关注')).toBeTruthy();
  });

  it('shows active tab correctly', () => {
    const { getByText } = renderWithTheme(
      <StoryListTabs {...mockProps} />
    );

    // The active tab should be visually different (handled by SegmentedButtons)
    const activeTab = getByText('推荐');
    expect(activeTab).toBeTruthy();
  });

  it('calls onTabPress when tab is pressed', () => {
    const { getByText } = renderWithTheme(
      <StoryListTabs {...mockProps} />
    );

    const hotTab = getByText('热门');
    fireEvent.press(hotTab);

    expect(mockProps.onTabPress).toHaveBeenCalledWith('热门');
  });

  it('handles different active tab', () => {
    const { getByText } = renderWithTheme(
      <StoryListTabs {...mockProps} activeTab="热门" />
    );

    expect(getByText('热门')).toBeTruthy();
    expect(getByText('推荐')).toBeTruthy();
  });

  it('renders with empty tabs array', () => {
    const { container } = renderWithTheme(
      <StoryListTabs {...mockProps} tabs={[]} />
    );

    expect(container).toBeTruthy();
  });
});
