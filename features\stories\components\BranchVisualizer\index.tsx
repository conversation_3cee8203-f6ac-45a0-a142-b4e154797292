import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Animated,
  PanResponder,
  Dimensions,
  FlatList,
} from 'react-native';
import { BranchNode, StorySegment } from '@/api/stories';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import {
  ChevronRight,
  ChevronDown,
  GitBranch,
  GitMerge,
} from 'lucide-react-native';
import BranchActionsMenu from '../BranchActionsMenu';
import { useAuthStore } from '@/lib/store/authStore';

interface BranchVisualizerProps {
  branchTree: BranchNode | null;
  currentSegmentId?: string;
  onBranchSelect: (segmentId: string) => void;
  onRenameBranch?: (
    segmentId: string,
    branchTitle: string
  ) => Promise<StorySegment | null>;
  onDeleteBranch?: (segmentId: string) => Promise<boolean>;
  collapsible?: boolean;
  initialScale?: number;
  visualizationStyle?: 'tree' | 'flow' | 'network' | 'timeline';
}

export default function BranchVisualizer({
  branchTree,
  currentSegmentId,
  onBranchSelect,
  onRenameBranch,
  onDeleteBranch,
  collapsible = true,
  initialScale = 1,
  visualizationStyle = 'tree',
}: BranchVisualizerProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { user } = useAuthStore();

  // 状态
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [scale, setScale] = useState(initialScale);
  const pan = useRef(new Animated.ValueXY()).current;

  // 屏幕尺寸
  const { width } = Dimensions.get('window');

  // 状态：扁平化的分支列表（用于优化大型分支结构的性能）
  const [flattenedBranches, setFlattenedBranches] = useState<
    {
      node: BranchNode;
      depth: number;
      isVisible: boolean;
    }[]
  >([]);

  // 将树状结构转换为扁平列表
  const flattenBranchTree = useCallback(
    (
      node: BranchNode | null,
      depth = 0,
      isVisible = true,
      parentExpanded = true
    ) => {
      if (!node) return [];

      const result: {
        node: BranchNode;
        depth: number;
        isVisible: boolean;
      }[] = [];

      // 当前节点是否可见（父节点展开且当前节点可见）
      const nodeIsVisible = isVisible && parentExpanded;

      // 添加当前节点
      result.push({
        node,
        depth,
        isVisible: nodeIsVisible,
      });

      // 当前节点是否展开
      const isExpanded = expandedNodes.has(node.id);

      // 递归添加子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          result.push(
            ...flattenBranchTree(
              child,
              depth + 1,
              nodeIsVisible,
              isExpanded && nodeIsVisible
            )
          );
        });
      }

      return result;
    },
    [expandedNodes]
  );

  // 处理节点展开/折叠
  const toggleNode = useCallback((nodeId: string) => {
    setExpandedNodes((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 更新扁平化的分支列表
  useEffect(() => {
    if (branchTree) {
      const flattened = flattenBranchTree(branchTree);
      setFlattenedBranches(flattened);
    }
  }, [branchTree, expandedNodes, flattenBranchTree]);

  // 平移和缩放手势
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        pan.setOffset({
          x: pan.x._value,
          y: pan.y._value,
        });
        pan.setValue({ x: 0, y: 0 });
      },
      onPanResponderMove: Animated.event([null, { dx: pan.x, dy: pan.y }], {
        useNativeDriver: false,
      }),
      onPanResponderRelease: () => {
        pan.flattenOffset();
      },
    })
  ).current;

  // 处理缩放
  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, 0.5));
  };

  // 重置视图
  const resetView = () => {
    pan.setValue({ x: 0, y: 0 });
    setScale(initialScale);
  };

  // 递归渲染分支节点
  const renderBranchNode = (node: BranchNode | null, depth = 0) => {
    if (!node) return null;

    const isExpanded = expandedNodes.has(node.id);
    const isSelected = node.id === currentSegmentId;
    const hasChildren = node.children && node.children.length > 0;

    // 计算缩进
    const indentStyle = { marginLeft: depth * 20 };

    // 检查当前用户是否是分支的作者
    const isAuthor = user?.id === node.segment.author_id;

    // 提取分支标题（如果有）
    const extractBranchTitle = (content: string): string => {
      const titleMatch = content.match(/^\[(.*?)\]\s/);
      return titleMatch ? titleMatch[1] : '';
    };

    // 获取显示的分支标题
    const displayTitle =
      node.branchTitle ||
      extractBranchTitle(node.segment.content) ||
      t('storyDetail.branch', 'Branch');

    // 获取显示的分支内容预览
    const getContentPreview = (content: string): string => {
      // 如果内容有标题格式，移除标题部分
      const titleRegex = /^\[(.*?)\]\s/;
      const contentWithoutTitle = content.replace(titleRegex, '');
      return (
        contentWithoutTitle.substring(0, 50) +
        (contentWithoutTitle.length > 50 ? '...' : '')
      );
    };

    return (
      <View key={node.id}>
        <View style={[styles.branchNodeContainer, indentStyle]}>
          <TouchableOpacity
            style={[styles.branchNode, isSelected && styles.selectedNode]}
            onPress={() => {
              console.log('BranchVisualizer - branch selected:', node.id);
              if (node.id) {
                console.log('Calling onBranchSelect with ID:', node.id);
                onBranchSelect(node.id);
              } else {
                console.error('Invalid branch node ID');
              }
            }}
          >
            {hasChildren && collapsible && (
              <TouchableOpacity
                style={styles.expandButton}
                onPress={() => toggleNode(node.id)}
              >
                {isExpanded ? (
                  <ChevronDown size={16} color={theme.colors.text} />
                ) : (
                  <ChevronRight size={16} color={theme.colors.text} />
                )}
              </TouchableOpacity>
            )}

            <GitBranch
              size={16}
              color={isSelected ? theme.colors.primary : theme.colors.text}
              style={styles.branchIcon}
            />

            <View style={styles.branchContent}>
              <Text
                style={[
                  styles.branchText,
                  isSelected && styles.selectedBranchText,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {displayTitle}{' '}
                {depth > 0
                  ? `${depth}.${node.segment.order_in_branch + 1}`
                  : ''}
              </Text>

              <Text
                style={styles.branchPreview}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {getContentPreview(node.segment.content)}
              </Text>
            </View>
          </TouchableOpacity>

          {/* 分支操作菜单 */}
          {onRenameBranch && onDeleteBranch && (
            <BranchActionsMenu
              segment={node.segment}
              onRename={onRenameBranch}
              onDelete={onDeleteBranch}
              isAuthor={isAuthor}
            />
          )}
        </View>

        {isExpanded && hasChildren && (
          <View style={styles.childrenContainer}>
            {node.children.map((child) => renderBranchNode(child, depth + 1))}
          </View>
        )}
      </View>
    );
  };

  // 如果没有分支树，显示空状态
  if (!branchTree) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {t('storyDetail.noBranches', 'No branches available')}
        </Text>
      </View>
    );
  }

  // 渲染不同的可视化样式
  const renderVisualization = () => {
    switch (visualizationStyle) {
      case 'flow':
        return renderFlowVisualization();
      case 'network':
        return renderNetworkVisualization();
      case 'timeline':
        return renderTimelineVisualization();
      case 'tree':
      default:
        return renderTreeVisualization();
    }
  };

  // 渲染单个分支节点项（用于FlatList）
  const renderBranchNodeItem = ({
    item,
  }: {
    item: { node: BranchNode; depth: number; isVisible: boolean };
  }) => {
    if (!item.isVisible) return null;

    const { node, depth } = item;
    const isExpanded = expandedNodes.has(node.id);
    const isSelected = node.id === currentSegmentId;
    const hasChildren = node.children && node.children.length > 0;

    // 检查当前用户是否是分支的作者
    const isAuthor = user?.id === node.segment.author_id;

    // 提取分支标题（如果有）
    const extractBranchTitle = (content: string): string => {
      const titleMatch = content.match(/^\[(.*?)\]\s/);
      return titleMatch ? titleMatch[1] : '';
    };

    // 获取显示的分支标题
    const displayTitle =
      node.branchTitle ||
      extractBranchTitle(node.segment.content) ||
      t('storyDetail.branch', 'Branch');

    // 获取显示的分支内容预览
    const getContentPreview = (content: string): string => {
      // 如果内容有标题格式，移除标题部分
      const titleRegex = /^\[(.*?)\]\s/;
      const contentWithoutTitle = content.replace(titleRegex, '');
      return (
        contentWithoutTitle.substring(0, 50) +
        (contentWithoutTitle.length > 50 ? '...' : '')
      );
    };

    // 计算缩进
    const indentStyle = { marginLeft: depth * 20 };

    return (
      <View style={[styles.branchNodeContainer, indentStyle]}>
        <TouchableOpacity
          style={[styles.branchNode, isSelected && styles.selectedNode]}
          onPress={() => {
            console.log(
              'BranchVisualizer FlatList - branch selected:',
              node.id
            );
            if (node.id) {
              console.log(
                'FlatList - Calling onBranchSelect with ID:',
                node.id
              );
              onBranchSelect(node.id);
            } else {
              console.error('FlatList - Invalid branch node ID');
            }
          }}
        >
          {hasChildren && collapsible && (
            <TouchableOpacity
              style={styles.expandButton}
              onPress={() => toggleNode(node.id)}
            >
              {isExpanded ? (
                <ChevronDown size={16} color={theme.colors.text} />
              ) : (
                <ChevronRight size={16} color={theme.colors.text} />
              )}
            </TouchableOpacity>
          )}

          <GitBranch
            size={16}
            color={isSelected ? theme.colors.primary : theme.colors.text}
            style={styles.branchIcon}
          />

          <View style={styles.branchContent}>
            <Text
              style={[
                styles.branchText,
                isSelected && styles.selectedBranchText,
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {displayTitle}{' '}
              {depth > 0 ? `${depth}.${node.segment.order_in_branch + 1}` : ''}
            </Text>

            <Text
              style={styles.branchPreview}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {getContentPreview(node.segment.content)}
            </Text>
          </View>
        </TouchableOpacity>

        {/* 分支操作菜单 */}
        {onRenameBranch && onDeleteBranch && (
          <BranchActionsMenu
            segment={node.segment}
            onRename={onRenameBranch}
            onDelete={onDeleteBranch}
            isAuthor={isAuthor}
          />
        )}
      </View>
    );
  };

  // 树状图可视化
  const renderTreeVisualization = () => {
    // 使用FlatList优化大型分支结构的性能
    return (
      <ScrollView
        horizontal
        contentContainerStyle={styles.scrollContent}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.treeContainer,
            {
              transform: [
                { translateX: pan.x },
                { translateY: pan.y },
                { scale },
              ],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {flattenedBranches.length > 0 ? (
            <FlatList
              data={flattenedBranches}
              renderItem={renderBranchNodeItem}
              keyExtractor={(item) => item.node.id}
              initialNumToRender={20}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={true}
              scrollEnabled={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {t('storyDetail.noBranches', 'No branches available')}
                  </Text>
                </View>
              }
            />
          ) : (
            renderBranchNode(branchTree)
          )}
        </Animated.View>
      </ScrollView>
    );
  };

  // 流程图可视化
  const renderFlowVisualization = () => {
    // 简化的流程图实现，使用相同的树状结构但样式不同
    return (
      <ScrollView
        horizontal
        contentContainerStyle={[styles.scrollContent, styles.flowContent]}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.treeContainer,
            {
              transform: [
                { translateX: pan.x },
                { translateY: pan.y },
                { scale },
              ],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {flattenedBranches.length > 0 ? (
            <FlatList
              data={flattenedBranches}
              renderItem={renderBranchNodeItem}
              keyExtractor={(item) => item.node.id}
              initialNumToRender={20}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={true}
              scrollEnabled={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {t('storyDetail.noBranches', 'No branches available')}
                  </Text>
                </View>
              }
            />
          ) : (
            renderBranchNode(branchTree)
          )}
        </Animated.View>
      </ScrollView>
    );
  };

  // 网络图可视化
  const renderNetworkVisualization = () => {
    // 简化的网络图实现，使用相同的树状结构但样式不同
    return (
      <ScrollView
        horizontal
        contentContainerStyle={[styles.scrollContent, styles.networkContent]}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.treeContainer,
            {
              transform: [
                { translateX: pan.x },
                { translateY: pan.y },
                { scale },
              ],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {flattenedBranches.length > 0 ? (
            <FlatList
              data={flattenedBranches}
              renderItem={renderBranchNodeItem}
              keyExtractor={(item) => item.node.id}
              initialNumToRender={20}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={true}
              scrollEnabled={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {t('storyDetail.noBranches', 'No branches available')}
                  </Text>
                </View>
              }
            />
          ) : (
            renderBranchNode(branchTree)
          )}
        </Animated.View>
      </ScrollView>
    );
  };

  // 时间线可视化
  const renderTimelineVisualization = () => {
    // 简化的时间线实现，使用相同的树状结构但样式不同
    return (
      <ScrollView
        horizontal
        contentContainerStyle={[styles.scrollContent, styles.timelineContent]}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.treeContainer,
            {
              transform: [
                { translateX: pan.x },
                { translateY: pan.y },
                { scale },
              ],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {flattenedBranches.length > 0 ? (
            <FlatList
              data={flattenedBranches}
              renderItem={renderBranchNodeItem}
              keyExtractor={(item) => item.node.id}
              initialNumToRender={20}
              maxToRenderPerBatch={10}
              windowSize={10}
              removeClippedSubviews={true}
              scrollEnabled={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {t('storyDetail.noBranches', 'No branches available')}
                  </Text>
                </View>
              }
            />
          ) : (
            renderBranchNode(branchTree)
          )}
        </Animated.View>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.controlsContainer}>
        <TouchableOpacity style={styles.controlButton} onPress={handleZoomOut}>
          <Text style={styles.controlButtonText}>-</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={handleZoomIn}>
          <Text style={styles.controlButtonText}>+</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={resetView}>
          <Text style={styles.controlButtonText}>Reset</Text>
        </TouchableOpacity>
      </View>

      {renderVisualization()}
    </View>
  );
}
