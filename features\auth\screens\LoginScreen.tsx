import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './LoginScreen.styles'; // Import styles from the new file
import { signInWithEmail } from '@/api/auth';
import { useAuthStore } from '@/lib/store/authStore';
import { Link } from 'expo-router';
import { useTranslation } from 'react-i18next';

export default function LoginScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme); // Use dynamic styles based on theme

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const { signIn } = useAuthStore(); // Get the signIn action from auth store

  const handleLogin = async () => {
    console.log('[LoginAttempt] Attempting to login with email:', email);
    setLoading(true);
    try {
      const { data, error } = await signInWithEmail(email, password);
      // Log the full data and error objects for detailed inspection
      console.log('[LoginResponse] Data:', JSON.stringify(data, null, 2));
      console.log('[LoginResponse] Error:', JSON.stringify(error, null, 2));
      setLoading(false);

      if (error) {
        console.error('[LoginError] Supabase Auth Error:', error);
        // Provide a fallback message for t() in case the key is missing
        Alert.alert(
          t('loginErrorTitle', 'Login Error'),
          error.message ||
            t('authErrors.unknownError', 'An unknown error occurred.')
        );
      } else if (data?.user && data?.session) {
        console.log(
          '[LoginSuccess] User and session received. User ID:',
          data.user.id
        );
        signIn(data.session, data.user);
        // Redirection is handled by _layout.tsx based on auth state
      } else {
        console.warn(
          '[LoginFailed] No error, but no user/session data either. Data:',
          JSON.stringify(data, null, 2)
        );
        Alert.alert(
          t('loginErrorTitle', 'Login Error'),
          t('authErrors.loginFailed', 'Login failed. Please try again.')
        );
      }
    } catch (e: any) {
      console.error('[LoginCatch] Unexpected error during login:', e);
      setLoading(false);
      Alert.alert(
        t('unexpectedErrorTitle', 'Unexpected Error'),
        e.message ||
          t('authErrors.unexpectedError', 'An unexpected error occurred.')
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('loginTitle')}</Text>

      <TextInput
        style={styles.input}
        placeholder={t('emailPlaceholder')}
        placeholderTextColor={theme.colors.placeholder}
        keyboardType="email-address"
        autoCapitalize="none"
        value={email}
        onChangeText={setEmail}
      />

      <TextInput
        style={styles.input}
        placeholder={t('passwordPlaceholder')}
        placeholderTextColor={theme.colors.placeholder}
        secureTextEntry
        value={password}
        onChangeText={setPassword}
      />

      <TouchableOpacity
        style={styles.forgotPasswordContainer}
        onPress={() => router.push('/(auth)/reset-password')}
      >
        <Text style={styles.forgotPasswordText}>
          {t('auth.forgotPasswordLink')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleLogin}
        disabled={loading}
      >
        <Text style={styles.buttonText}>
          {loading ? t('loggingIn') : t('loginButton')}
        </Text>
      </TouchableOpacity>

      <View style={styles.signupContainer}>
        <Text style={styles.signupText}>{t('noAccountText')}</Text>
        <Link href="/(auth)/register" style={styles.signupLink}>
          {t('signupLinkText')}
        </Link>
      </View>
    </View>
  );
}

// Removed temporary styles from here
