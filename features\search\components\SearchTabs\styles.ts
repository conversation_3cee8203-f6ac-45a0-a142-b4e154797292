import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    tabsContainer: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: theme.colors.border,
    },
    tab: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      marginRight: 24,
    },
    activeTab: {
      borderBottomWidth: 2,
    },
    tabText: {
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 4,
    },
  });
