import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles specific to the Feed Tab content
export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    scrollView: {
      flex: 1,
    },
    contentContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md, // Add vertical padding
      gap: theme.spacing.md, // Use gap for spacing between items
      flexGrow: 1, // 让列表能够填充可用空间，使空状态垂直居中
    },
    sectionTitle: {
      fontFamily: theme.fonts.bold,
      fontSize: 18, // Slightly smaller title
      color: theme.colors.text,
      // Removed margin, rely on gap and padding
    },
    emptyStateText: {
      fontFamily: theme.fonts.regular,
      fontSize: 16,
      color: theme.colors.secondaryText,
      textAlign: 'center',
      marginTop: theme.spacing.xl,
    },
    footerContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: theme.spacing.md,
      gap: theme.spacing.xs,
    },
    footerText: {
      fontFamily: theme.fonts.regular,
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    emptyStateContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.xl,
      minHeight: 400, // 确保空状态有足够高度显示
    },
    emptyStateIcon: {
      marginBottom: theme.spacing.lg,
      opacity: 0.7,
    },
    emptyStateTitle: {
      fontFamily: theme.fonts.bold,
      fontSize: 18,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      textAlign: 'center',
    },
    emptyStateText: {
      fontFamily: theme.fonts.regular,
      fontSize: 16,
      color: theme.colors.secondaryText,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
    },
    emptyStateButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.roundness,
    },
    emptyStateButtonText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.onPrimary,
    },
  });
