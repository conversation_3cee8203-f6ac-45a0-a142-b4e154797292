import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import StoryFeedScreen from '@/features/stories/screens/StoryFeedScreen'; // Using our new feed-style component
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

export default function StoryDetailPageRoute() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const theme = useAppTheme();

  if (!id) {
    // This should ideally not happen if navigation is correct
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <Text style={{ color: theme.colors.text }}>Story ID not found.</Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Story', headerShown: false }} />
      {/* Using our new feed-style component with improved branch visualization */}
      <StoryFeedScreen storyId={id} />
    </>
  );
}
