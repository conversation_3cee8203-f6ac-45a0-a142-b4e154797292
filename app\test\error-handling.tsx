import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/StoryTabs';
import { StoryList } from '@/features/stories/components/StoryList';
import { useStoriesScreenTest, TestScenario } from '@/features/stories/hooks/useStoriesScreenTest';

/**
 * Error handling test screen
 * This screen tests the error handling capabilities of the app
 */
export default function ErrorHandlingTest() {
  const theme = useAppTheme();
  const { t } = useTranslation();
  const router = useRouter();
  
  // Use the test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
    setTestScenario,
    currentScenario,
  } = useStoriesScreenTest();
  
  // Error scenarios
  const errorScenarios = [
    {
      id: TestScenario.SUCCESS,
      title: 'Success',
      description: 'No errors, normal operation',
    },
    {
      id: TestScenario.ERROR_NETWORK,
      title: 'Network Error',
      description: 'Simulate a network connection error',
    },
    {
      id: TestScenario.ERROR_SERVER,
      title: 'Server Error',
      description: 'Simulate a server error (500)',
    },
    {
      id: TestScenario.ERROR_AUTH,
      title: 'Authentication Error',
      description: 'Simulate an authentication error',
    },
    {
      id: TestScenario.EMPTY,
      title: 'Empty Data',
      description: 'Simulate empty data response',
    },
  ];
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    scenariosContainer: {
      marginBottom: theme.spacing.md,
    },
    scenarioTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    scenarioCard: {
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 8,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      borderWidth: 1,
    },
    scenarioCardTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    scenarioCardDescription: {
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    backButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
    contentContainer: {
      flex: 1,
    },
    currentScenarioContainer: {
      backgroundColor: `${theme.colors.primary}20`,
      padding: theme.spacing.sm,
      borderRadius: 8,
      marginBottom: theme.spacing.md,
    },
    currentScenarioText: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: 'bold',
    },
  });
  
  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Error Handling Test</Text>
        
        <View style={styles.currentScenarioContainer}>
          <Text style={styles.currentScenarioText}>
            Current Scenario: {errorScenarios.find(s => s.id === currentScenario)?.title || 'Unknown'}
          </Text>
        </View>
        
        <View style={styles.scenariosContainer}>
          <Text style={styles.scenarioTitle}>Error Scenarios</Text>
          
          <ScrollView>
            {errorScenarios.map((scenario) => (
              <TouchableOpacity
                key={scenario.id}
                style={[
                  styles.scenarioCard,
                  {
                    borderColor: currentScenario === scenario.id ? theme.colors.primary : theme.colors.border,
                  },
                ]}
                onPress={() => setTestScenario(scenario.id)}
              >
                <Text style={styles.scenarioCardTitle}>{scenario.title}</Text>
                <Text style={styles.scenarioCardDescription}>{scenario.description}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Back to Tests</Text>
        </TouchableOpacity>
      </View>
      
      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
      
      <View style={styles.contentContainer}>
        <StoryList
          stories={stories}
          onStoryPress={handleStoryPress}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          onRefresh={refreshStories}
          onLoadMore={loadMoreStories}
          hasMoreStories={hasMoreStories}
          error={error}
          onRetry={retryFetch}
          retryCount={retryCount}
        />
      </View>
    </SafeAreaView>
  );
}
