import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  tabsContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.xs, // Add a small padding below the scroll view
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.colors.border, 
    backgroundColor: theme.colors.background,
  },
  scrollViewContent: {
    paddingVertical: theme.spacing.sm,
    gap: theme.spacing.sm, // Use gap for spacing between tabs
    alignItems: 'center', // Center items vertically if needed
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.roundness * 5, // Pill shape
  },
  activeTab: {
    // Background color set dynamically in component
  },
  tabText: {
    fontFamily: theme.fonts.medium,
    fontSize: 14, // Slightly larger text
    marginLeft: theme.spacing.xs,
    // Color set dynamically
  },
}); 