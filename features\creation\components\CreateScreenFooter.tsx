import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './CreateScreenFooter.styles';
import { ArrowRight } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface CreateScreenFooterProps {
  onNextPress: () => void;
  canProceed: boolean;
  nextButtonText?: string; // Allow customizing button text
}

export function CreateScreenFooter({
  onNextPress,
  canProceed,
  nextButtonText,
}: CreateScreenFooterProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const buttonText = nextButtonText || t('common.next', '下一步');
  const activeColor = theme.dark ? theme.colors.background : '#FFF';
  const inactiveColor = theme.colors.disabled;
  const textColor = canProceed ? activeColor : inactiveColor;
  const buttonBackgroundColor = canProceed
    ? theme.colors.primary
    : theme.colors.surface;

  return (
    <View style={styles.footer}>
      <TouchableOpacity
        style={[
          styles.nextButton,
          {
            backgroundColor: buttonBackgroundColor,
            opacity: canProceed ? 1 : 0.5,
          },
        ]}
        onPress={onNextPress}
        disabled={!canProceed}
      >
        <Text style={[styles.nextButtonText, { color: textColor }]}>
          {buttonText}
        </Text>
        <ArrowRight color={textColor} size={20} />
      </TouchableOpacity>
    </View>
  );
}
