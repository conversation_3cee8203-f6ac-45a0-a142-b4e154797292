import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

/**
 * Creates styles for the NewPasswordScreen component based on the current theme
 * 
 * @param theme The current app theme
 * @returns StyleSheet object with themed styles
 */
export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
    },
    title: {
      fontSize: theme.fontSizes.xl,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    instructions: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    input: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      color: theme.colors.text,
      borderWidth: 1,
      borderColor: theme.colors.border,
      fontFamily: theme.fonts.regular,
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.radius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      minHeight: 50,
      justifyContent: 'center',
    },
    buttonDisabled: {
      opacity: 0.7,
    },
    buttonText: {
      color: theme.colors.onPrimary,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
    },
  });
};
