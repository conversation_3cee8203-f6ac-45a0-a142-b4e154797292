import React, { useState } from 'react';
import { 
  View, 
  // Text, // No longer needed directly
  // StyleSheet, // Removed
  // ScrollView, // No longer needed directly
  // TouchableOpacity, // No longer needed directly
  // Image // No longer needed directly
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
// import { AppTheme } from '@/lib/theme/themes'; // No longer needed
import { mockUsers } from '@/utils/mockData'; // Keep mock data source
import HeaderBar from '@/components/ui/HeaderBar';
// Removed unused direct component imports
// import SearchBar from '@/components/ui/SearchBar';
// import ActivityFeedItem from '@/components/social/ActivityFeedItem';
// import UserCard from '@/components/social/UserCard';
// import { Bell, MessageSquare, Search, TrendingUp, Users } from 'lucide-react-native'; // Moved to tabs
import { createStyles } from '@/features/social/screens/SocialScreen.styles';

// Import feature components
import { SocialTabs, SocialTabKey } from '@/features/social/components/SocialTabs';
import { FeedTab } from '@/features/social/components/FeedTab';
import { DiscoverTab } from '@/features/social/components/DiscoverTab';
import { MessagesTab } from '@/features/social/components/MessagesTab';
import { NotificationsTab } from '@/features/social/components/NotificationsTab';

// Define ActivityItem type locally or import from a shared types file
// This might ideally live in @/types/social.ts or similar
interface ActivityItem {
  id: string;
  type: 'comment' | 'branch' | 'like'; 
  user: typeof mockUsers[0]; 
  storyTitle: string;
  content: string;
  time: string;
}

export default function SocialScreen() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const [activeTab, setActiveTab] = useState<SocialTabKey>('feed');

  // Mock activity data (fetch or manage this properly later)
  const activityItems: ActivityItem[] = [
    {
      id: '1',
      type: 'comment', 
      user: mockUsers[0],
      storyTitle: '星际迷航：量子裂隙',
      content: '这个故事真是精彩，期待下一章！',
      time: '2小时前',
    },
    {
      id: '2',
      type: 'branch',
      user: mockUsers[1],
      storyTitle: '迷雾森林的秘密',
      content: '创作了新的分支剧情',
      time: '6小时前',
    },
    {
      id: '3',
      type: 'like',
      user: mockUsers[2],
      storyTitle: '记忆收藏家',
      content: '喜欢了你的故事',
      time: '昨天',
    },
  ];

  // Removed renderTabIcon function (moved to SocialTabs)

  // Render the active tab's component
  const renderActiveTabContent = () => {
    switch (activeTab) {
      case 'feed':
        return <FeedTab activityItems={activityItems} />;
      case 'discover':
        return <DiscoverTab />;
      case 'messages':
        return <MessagesTab />;
      case 'notifications':
        return <NotificationsTab />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <HeaderBar title="社区" />
      
      {/* Use the SocialTabs component */}
      <SocialTabs activeTab={activeTab} onTabPress={setActiveTab} />
      
      <View style={styles.contentContainer}>
        {renderActiveTabContent()} 
      </View>
    </SafeAreaView>
  );
}

// Removed the temporary localStyles definition