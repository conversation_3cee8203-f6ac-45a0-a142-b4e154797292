import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './EditProfileScreen.styles';
import { useTranslation } from 'react-i18next';
import {
  getCurrentUserProfile,
  updateCurrentUserProfile,
  uploadAvatar,
  ProfileUpdateData,
} from '@/api/profiles';
import { useAuthStore } from '@/lib/store/authStore';
import { useRouter } from 'expo-router';
import AvatarPicker from '../components/AvatarPicker';

export default function EditProfileScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  const user = useAuthStore((state) => state.user);

  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [bio, setBio] = useState('');
  const [initialUsername, setInitialUsername] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [newAvatarUri, setNewAvatarUri] = useState<string | null>(null);

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        Alert.alert(t('error'), t('authErrors.userNotFound'));
        setLoading(false);
        router.back();
        return;
      }
      setLoading(true);
      const { data, error } = await getCurrentUserProfile();
      if (error || !data) {
        Alert.alert(t('error'), t('profileErrors.fetchFailed'));
        console.error('Fetch profile error:', error);
      } else {
        setUsername(data.username || '');
        setInitialUsername(data.username || '');
        setFullName(data.full_name || '');
        setBio(data.bio || '');
        setAvatarUrl(data.avatar_url);
      }
      setLoading(false);
    };
    fetchProfile();
  }, [user, t, router]);

  /**
   * Handles the selection of a new avatar image
   * @param uri The local URI of the selected image
   */
  const handleAvatarSelected = async (uri: string) => {
    try {
      setNewAvatarUri(uri);
      setUploadingAvatar(true);

      const { data, error } = await uploadAvatar(uri);

      if (error) {
        console.error('Avatar upload error:', error);
        Alert.alert(
          t('error', 'Error'),
          t(
            'profileErrors.avatarUploadFailed',
            'Failed to upload avatar image.'
          )
        );
      } else if (data) {
        setAvatarUrl(data.avatar_url);
        Alert.alert(
          t('success', 'Success'),
          t('profileErrors.avatarUploadSuccess', 'Avatar updated successfully!')
        );
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      Alert.alert(
        t('error', 'Error'),
        t('profileErrors.avatarUploadFailed', 'Failed to upload avatar image.')
      );
    } finally {
      setUploadingAvatar(false);
      setNewAvatarUri(null);
    }
  };

  const handleSave = async () => {
    if (!user) {
      Alert.alert(t('error'), t('authErrors.userNotFound'));
      return;
    }
    if (!username.trim()) {
      Alert.alert(t('validationError'), t('profileErrors.usernameRequired'));
      return;
    }

    setSaving(true);
    const updates: ProfileUpdateData = {
      full_name: fullName,
      bio: bio,
    };
    // Only include username if it has changed, as it might have unique constraints
    if (username.trim() !== initialUsername) {
      updates.username = username.trim();
    }

    const { data, error } = await updateCurrentUserProfile(updates);
    setSaving(false);

    if (error) {
      console.error('Update profile error:', error);
      // Check for unique constraint violation for username (Postgres error code 23505)
      if (error.code === '23505' && error.message?.includes('username')) {
        Alert.alert(t('error'), t('profileErrors.usernameTaken'));
      } else {
        Alert.alert(t('error'), t('profileErrors.updateFailed'));
      }
    } else {
      Alert.alert(t('success'), t('profileErrors.updateSuccess'));
      // Optionally, update the authStore user's metadata if username changed,
      // or rely on a fresh fetch next time profile is viewed.
      router.back();
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} keyboardShouldPersistTaps="handled">
      <Text style={styles.title}>{t('editProfileTitle', 'Edit Profile')}</Text>

      <View style={styles.avatarSection}>
        <AvatarPicker
          avatarUrl={newAvatarUri || avatarUrl}
          onImageSelected={handleAvatarSelected}
          loading={uploadingAvatar}
          size={120}
        />
      </View>

      <Text style={styles.inputLabel}>{t('usernameLabel', 'Username')}</Text>
      <TextInput
        style={styles.input}
        value={username}
        onChangeText={setUsername}
        placeholder={t('usernamePlaceholder', 'Enter your username')}
        autoCapitalize="none"
      />

      <Text style={styles.inputLabel}>{t('fullNameLabel', 'Full Name')}</Text>
      <TextInput
        style={styles.input}
        value={fullName}
        onChangeText={setFullName}
        placeholder={t('fullNamePlaceholder', 'Enter your full name')}
      />

      <Text style={styles.inputLabel}>{t('bioLabel', 'Bio')}</Text>
      <TextInput
        style={[styles.input, styles.bioInput]}
        value={bio}
        onChangeText={setBio}
        placeholder={t('bioPlaceholder', 'Tell us about yourself')}
        multiline
        numberOfLines={4}
      />

      <TouchableOpacity
        style={styles.button}
        onPress={handleSave}
        disabled={saving || uploadingAvatar}
      >
        <Text style={styles.buttonText}>
          {saving ? t('saving', 'Saving...') : t('saveChanges', 'Save Changes')}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}
