-- Create user_story_interactions table for tracking reading progress and bookmarks
CREATE TABLE IF NOT EXISTS public.user_story_interactions (
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  story_id uuid REFERENCES stories(id) ON DELETE CASCADE NOT NULL,
  is_reading boolean DEFAULT false,
  is_bookmarked boolean DEFAULT false,
  last_read_segment_id uuid REFERENCES story_segments(id),
  last_read_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  PRIMARY KEY (user_id, story_id)
);

-- Enable RLS
ALTER TABLE IF EXISTS public.user_story_interactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own interactions"
  ON public.user_story_interactions FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own interactions"
  ON public.user_story_interactions FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own interactions"
  ON public.user_story_interactions FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create trigger to update updated_at on update
CREATE OR REPLACE FUNCTION update_user_story_interactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_story_interactions_updated_at_trigger
BEFORE UPDATE ON public.user_story_interactions
FOR EACH ROW
EXECUTE FUNCTION update_user_story_interactions_updated_at();