import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { TestBeaker, Smartphone, LayoutList, Gauge, AlertCircle, Sparkles } from 'lucide-react-native';

/**
 * Test index screen
 * This screen provides navigation to different test screens
 */
export default function TestIndex() {
  const theme = useAppTheme();
  const { t } = useTranslation();
  const router = useRouter();
  
  // Test screens
  const testScreens = [
    {
      id: 'stories-screen',
      title: 'Stories Screen Test',
      description: 'Test the stories screen with different scenarios',
      icon: <LayoutList size={24} color={theme.colors.primary} />,
      route: '/test/stories-screen',
    },
    {
      id: 'device-compatibility',
      title: 'Device Compatibility Test',
      description: 'Test the app on different device sizes and orientations',
      icon: <Smartphone size={24} color={theme.colors.primary} />,
      route: '/test/device-compatibility',
    },
    {
      id: 'performance',
      title: 'Performance Test',
      description: 'Test the app with large datasets and measure performance',
      icon: <Gauge size={24} color={theme.colors.primary} />,
      route: '/test/performance',
    },
    {
      id: 'error-handling',
      title: 'Error Handling Test',
      description: 'Test the app with different error scenarios',
      icon: <AlertCircle size={24} color={theme.colors.primary} />,
      route: '/test/error-handling',
    },
    {
      id: 'animations',
      title: 'Animations Test',
      description: 'Test the app animations and transitions',
      icon: <Sparkles size={24} color={theme.colors.primary} />,
      route: '/test/animations',
    },
  ];
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    headerSubtitle: {
      fontSize: 16,
      color: theme.colors.secondaryText,
      marginBottom: theme.spacing.md,
    },
    content: {
      padding: theme.spacing.md,
    },
    testCard: {
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 8,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      flexDirection: 'row',
      alignItems: 'center',
    },
    testCardIcon: {
      marginRight: theme.spacing.md,
    },
    testCardContent: {
      flex: 1,
    },
    testCardTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    testCardDescription: {
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    backButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
  });
  
  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Test Suite</Text>
          <Text style={styles.headerSubtitle}>
            Select a test to run and verify the app functionality
          </Text>
          
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.push('/')}
          >
            <Text style={styles.backButtonText}>Back to App</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.content}>
          {testScreens.map((screen) => (
            <TouchableOpacity
              key={screen.id}
              style={styles.testCard}
              onPress={() => router.push(screen.route)}
            >
              <View style={styles.testCardIcon}>{screen.icon}</View>
              <View style={styles.testCardContent}>
                <Text style={styles.testCardTitle}>{screen.title}</Text>
                <Text style={styles.testCardDescription}>{screen.description}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
