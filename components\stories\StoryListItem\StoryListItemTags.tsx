import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface StoryListItemTagsProps {
  themes: string[] | undefined;
  isCompleted: boolean | undefined;
}

export function StoryListItemTags({
  themes,
  isCompleted,
}: StoryListItemTagsProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <View style={styles.themesRow}>
      {themes?.map((themeName) => (
        <View
          key={themeName}
          style={[
            styles.themeTag,
            { backgroundColor: appTheme.colors.primary + '15' },
          ]}
        >
          <Text style={[styles.themeText, { color: appTheme.colors.primary }]}>
            {themeName}
          </Text>
        </View>
      ))}

      {typeof isCompleted === 'boolean' &&
        (isCompleted ? (
          <View
            style={[
              styles.statusTag,
              { backgroundColor: appTheme.colors.success + '15' },
            ]}
          >
            <Text
              style={[styles.statusText, { color: appTheme.colors.success }]}
            >
              已完结
            </Text>
          </View>
        ) : (
          <View
            style={[
              styles.statusTag,
              { backgroundColor: appTheme.colors.warning + '15' },
            ]}
          >
            <Text
              style={[styles.statusText, { color: appTheme.colors.warning }]}
            >
              连载中
            </Text>
          </View>
        ))}
    </View>
  );
}
