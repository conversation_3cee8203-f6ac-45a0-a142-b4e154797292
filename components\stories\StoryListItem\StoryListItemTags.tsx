import React from 'react';
import { View } from 'react-native';
import { Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface StoryListItemTagsProps {
  themes: string[] | undefined;
  isCompleted: boolean | undefined;
}

export function StoryListItemTags({
  themes,
  isCompleted,
}: StoryListItemTagsProps) {
  const theme = usePaperTheme();

  return (
    <View
      style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: theme.spacing?.xs || 4,
        marginTop: theme.spacing?.sm || 8,
      }}
    >
      {themes?.map((themeName) => (
        <Chip
          key={themeName}
          mode="flat"
          compact
          style={{
            backgroundColor: theme.colors.primaryContainer,
          }}
          textStyle={{
            color: theme.colors.onPrimaryContainer,
            fontSize: 10,
          }}
        >
          {themeName}
        </Chip>
      ))}

      {typeof isCompleted === 'boolean' && (
        <Chip
          mode="flat"
          compact
          style={{
            backgroundColor: isCompleted
              ? theme.colors.tertiaryContainer
              : theme.colors.secondaryContainer,
          }}
          textStyle={{
            color: isCompleted
              ? theme.colors.onTertiaryContainer
              : theme.colors.onSecondaryContainer,
            fontSize: 10,
          }}
        >
          {isCompleted ? '已完结' : '连载中'}
        </Chip>
      )}
    </View>
  );
}
