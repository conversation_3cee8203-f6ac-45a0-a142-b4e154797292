import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { Send } from 'lucide-react-native';

interface MessageInputProps {
  onSend: (message: string) => Promise<void>;
  placeholder?: string;
}

export default function MessageInput({
  onSend,
  placeholder,
}: MessageInputProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSend = async () => {
    if (message.trim() === '' || isSending) {
      return;
    }

    setIsSending(true);
    try {
      await onSend(message);
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        placeholder={placeholder || t('messages.typeSomething', '输入消息...')}
        placeholderTextColor={theme.colors.placeholder}
        value={message}
        onChangeText={setMessage}
        multiline
        maxLength={1000}
        returnKeyType="send"
        onSubmitEditing={handleSend}
      />

      <TouchableOpacity
        style={[
          styles.sendButton,
          message.trim() === '' && styles.disabledSendButton,
        ]}
        onPress={handleSend}
        disabled={message.trim() === '' || isSending}
      >
        {isSending ? (
          <ActivityIndicator size="small" color={theme.colors.background} />
        ) : (
          <Send size={20} color={theme.colors.background} />
        )}
      </TouchableOpacity>
    </View>
  );
}