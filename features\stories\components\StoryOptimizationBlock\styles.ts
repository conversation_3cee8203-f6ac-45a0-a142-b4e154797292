import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStoryOptimizationBlockStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.lg,
    },
    optimizeButton: {
      backgroundColor: theme.colors.secondary,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      minHeight: 48,
    },
    buttonIcon: {
      marginRight: theme.spacing.sm,
    },
    buttonText: {
      color: theme.colors.onPrimary,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
    },
    modalContent: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.lg,
      padding: theme.spacing.lg,
      width: '100%',
      maxWidth: 500,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    modalTitle: {
      fontSize: theme.fontSizes.xl,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    inputLabel: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    contentInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.radius.md,
      padding: theme.spacing.md,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      textAlignVertical: 'top',
      minHeight: 120,
      marginBottom: theme.spacing.lg,
      fontFamily: theme.fonts.regular,
    },
    typeOptions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: theme.spacing.lg,
    },
    typeOption: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.radius.sm,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      marginRight: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
    },
    selectedTypeOption: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    typeOptionText: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
    },
    selectedTypeOptionText: {
      color: theme.colors.onPrimary,
    },
    modalButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: theme.spacing.md,
    },
    cancelButton: {
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      flex: 1,
      marginRight: theme.spacing.md,
      alignItems: 'center',
    },
    cancelButtonText: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
    },
    optimizeModalButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      flex: 1,
      alignItems: 'center',
    },
    optimizeModalButtonText: {
      color: theme.colors.onPrimary,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
    },
    disabledButton: {
      backgroundColor: theme.colors.disabled,
    },
  });
};
