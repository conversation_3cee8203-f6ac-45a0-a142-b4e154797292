import React from 'react';
import { View, Text, Image, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { Story } from '@/api/stories';
import { createDynamicStyles } from '../screens/StoryDetailScreen.styles';

interface StoryHeaderProps {
  story: Story;
  likeCount: number;
  isLiked: boolean;
  isLiking: boolean;
  onLikeToggle: () => void;
}

export function StoryHeader({
  story,
  likeCount,
  isLiked,
  isLiking,
  onLikeToggle,
}: StoryHeaderProps) {
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.headerSection}>
      {story.cover_image_url && (
        <Image
          source={{ uri: story.cover_image_url }}
          style={styles.coverImage}
        />
      )}
      <Text style={styles.title}>{story.title}</Text>
      <Text style={styles.author}>
        {t('storyDetail.by', 'By')}{' '}
        {story.profiles?.username ||
          t('storyDetail.unknownAuthor', 'Unknown Author')}
      </Text>
      <View style={styles.tagsContainer}>
        {story.tags?.map((tag, index) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </View>

      {/* 改进的点赞按钮 - 更明显的视觉反馈 */}
      <TouchableOpacity
        onPress={onLikeToggle}
        style={[styles.likeButton, isLiked && styles.likedButton]}
        disabled={isLiking}
      >
        <Ionicons
          name={isLiked ? 'heart' : 'heart-outline'}
          size={24}
          color={isLiked ? theme.colors.error : theme.colors.text}
        />
        <Text style={[styles.likeCount, isLiked && styles.likedText]}>
          {likeCount}{' '}
          {likeCount === 1
            ? t('storyDetail.like', 'Like')
            : t('storyDetail.likes', 'Likes')}
        </Text>
        {isLiking && (
          <ActivityIndicator
            size="small"
            color={theme.colors.primary}
            style={{ marginLeft: 5 }}
          />
        )}
      </TouchableOpacity>
    </View>
  );
}
