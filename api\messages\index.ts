import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import {
  Conversation,
  Message,
  GetConversationsOptions,
  GetMessagesOptions,
  CreateConversationOptions,
  SendMessageOptions,
} from './types';
import { mockConversations, mockMessages } from '@/utils/mockData/messages';

/**
 * 获取当前用户的对话列表
 */
export async function getConversations(
  options: GetConversationsOptions = {}
): Promise<{ data: Conversation[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0 } = options;

  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    // 使用模拟数据
    let conversations = [...mockConversations];

    // 按最后消息时间排序（降序）
    conversations.sort((a, b) =>
      new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime()
    );

    // 应用分页
    const paginatedConversations = conversations.slice(offset, offset + limit);

    return { data: paginatedConversations, error: null };
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return {
      data: null,
      error: {
        message: 'Failed to fetch conversations',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 获取单个对话详情
 */
export async function getConversationById(
  conversationId: string
): Promise<{ data: Conversation | null; error: PostgrestError | null }> {
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  try {
    // 查找对话
    const conversation = mockConversations.find(c => c.id === conversationId);

    if (!conversation) {
      return {
        data: null,
        error: {
          message: 'Conversation not found',
          details: '',
          hint: '',
          code: '404',
        } as PostgrestError,
      };
    }

    return { data: conversation, error: null };
  } catch (error) {
    console.error('Error fetching conversation:', error);
    return {
      data: null,
      error: {
        message: 'Failed to fetch conversation',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 获取对话中的消息列表
 */
export async function getMessagesByConversationId(
  conversationId: string,
  options: GetMessagesOptions = {}
): Promise<{ data: Message[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0 } = options;

  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    // 过滤消息
    let messages = mockMessages.filter(m => m.conversation_id === conversationId);

    // 按创建时间排序（降序）
    messages.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    // 应用分页
    const paginatedMessages = messages.slice(offset, offset + limit);

    return { data: paginatedMessages, error: null };
  } catch (error) {
    console.error('Error fetching messages:', error);
    return {
      data: null,
      error: {
        message: 'Failed to fetch messages',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 创建新对话
 */
export async function createConversation(
  options: CreateConversationOptions
): Promise<{ data: Conversation | null; error: PostgrestError | null }> {
  const { participant_ids, initial_message } = options;

  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 800));

  try {
    // 创建新对话
    const newConversation: Conversation = {
      id: `conversation-${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      last_message_at: new Date().toISOString(),
      last_message: initial_message || null,
      participants: [],
      unread_count: 0,
    };

    // 添加到模拟数据
    mockConversations.unshift(newConversation);

    // 如果有初始消息，创建消息
    if (initial_message) {
      const newMessage: Message = {
        id: `message-${Date.now()}`,
        conversation_id: newConversation.id,
        sender_id: 'current-user-id', // 假设当前用户ID
        content: initial_message,
        created_at: new Date().toISOString(),
        is_read: false,
      };

      // 添加到模拟数据
      mockMessages.unshift(newMessage);
    }

    return { data: newConversation, error: null };
  } catch (error) {
    console.error('Error creating conversation:', error);
    return {
      data: null,
      error: {
        message: 'Failed to create conversation',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 发送消息
 */
export async function sendMessage(
  options: SendMessageOptions
): Promise<{ data: Message | null; error: PostgrestError | null }> {
  const { conversation_id, content } = options;

  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  try {
    // 查找对话
    const conversationIndex = mockConversations.findIndex(c => c.id === conversation_id);

    if (conversationIndex === -1) {
      return {
        data: null,
        error: {
          message: 'Conversation not found',
          details: '',
          hint: '',
          code: '404',
        } as PostgrestError,
      };
    }

    // 创建新消息
    const newMessage: Message = {
      id: `message-${Date.now()}`,
      conversation_id,
      sender_id: 'current-user-id', // 假设当前用户ID
      content,
      created_at: new Date().toISOString(),
      is_read: false,
    };

    // 添加到模拟数据
    mockMessages.unshift(newMessage);

    // 更新对话的最后消息和时间
    mockConversations[conversationIndex].last_message = content;
    mockConversations[conversationIndex].last_message_at = newMessage.created_at;
    mockConversations[conversationIndex].updated_at = newMessage.created_at;

    return { data: newMessage, error: null };
  } catch (error) {
    console.error('Error sending message:', error);
    return {
      data: null,
      error: {
        message: 'Failed to send message',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 将消息标记为已读
 */
export async function markMessageAsRead(
  messageId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  try {
    // 查找消息
    const messageIndex = mockMessages.findIndex(m => m.id === messageId);

    if (messageIndex === -1) {
      return {
        success: false,
        error: {
          message: 'Message not found',
          details: '',
          hint: '',
          code: '404',
        } as PostgrestError,
      };
    }

    // 更新消息
    mockMessages[messageIndex].is_read = true;

    return { success: true, error: null };
  } catch (error) {
    console.error('Error marking message as read:', error);
    return {
      success: false,
      error: {
        message: 'Failed to mark message as read',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 将整个对话标记为已读
 */
export async function markConversationAsRead(
  conversationId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  try {
    // 更新所有消息
    mockMessages.forEach(message => {
      if (message.conversation_id === conversationId) {
        message.is_read = true;
      }
    });

    // 更新对话的未读消息数量
    const conversationIndex = mockConversations.findIndex(c => c.id === conversationId);
    if (conversationIndex !== -1) {
      mockConversations[conversationIndex].unread_count = 0;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error marking conversation as read:', error);
    return {
      success: false,
      error: {
        message: 'Failed to mark conversation as read',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 获取未读消息数量
 */
export async function getUnreadMessageCount(): Promise<{
  count: number;
  error: PostgrestError | null;
}> {
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 200));

  try {
    // 计算未读消息数量
    const unreadCount = mockMessages.filter(
      message => !message.is_read && message.sender_id !== 'current-user-id'
    ).length;

    return { count: unreadCount, error: null };
  } catch (error) {
    console.error('Error getting unread message count:', error);
    return {
      count: 0,
      error: {
        message: 'Failed to get unread message count',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}