import React from 'react';
import { View, Text, TouchableOpacity, Image, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import { MessageCircle, GitBranch } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './BranchPreviewCard.styles';
import { StorySegment } from '@/api/stories/types';
import { getChildrenCount, getCountValue } from '@/utils/storyHelpers';

interface BranchPreviewCardProps {
  branch: StorySegment;
  onPress: () => void;
  isActive?: boolean;
}

export default function BranchPreviewCard({
  branch,
  onPress,
  isActive = false,
}: BranchPreviewCardProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // Screen width for responsive sizing
  const { width } = Dimensions.get('window');
  const cardWidth = width * 0.75; // Card takes 75% of screen width

  // Format content preview
  const getContentPreview = (content: string, maxLength = 120) => {
    if (!content) return '';

    // Remove any markdown or special formatting
    const plainText = content.replace(/\[.*?\]/g, '').trim();

    if (plainText.length <= maxLength) return plainText;
    return plainText.substring(0, maxLength) + '...';
  };

  // Determine if branch has special status
  const getBranchStatus = () => {
    if (branch.is_main_branch) {
      return {
        label: t('storyDetail.mainBranch', '主线'),
        color: theme.colors.warning,
      };
    }

    if (branch.is_ai_generated) {
      return {
        label: t('storyDetail.aiGenerated', 'AI 精选'),
        color: theme.colors.info,
      };
    }

    // Check if branch is new (less than 24 hours old)
    const createdAt = new Date(branch.created_at);
    const now = new Date();
    const isNew = now.getTime() - createdAt.getTime() < 24 * 60 * 60 * 1000;

    if (isNew) {
      return {
        label: t('storyDetail.new', '新'),
        color: theme.colors.success,
      };
    }

    return null;
  };

  const branchStatus = getBranchStatus();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { width: cardWidth },
        isActive && styles.activeContainer,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Content Preview */}
      <Text style={styles.contentPreview} numberOfLines={4}>
        {getContentPreview(branch.content)}
      </Text>

      {/* Author Info */}
      <View style={styles.authorContainer}>
        <Image
          source={
            branch.profiles?.avatar_url
              ? { uri: branch.profiles.avatar_url }
              : require('@/assets/images/default-avatar.png')
          }
          style={styles.authorAvatar}
        />
        <Text style={styles.authorName} numberOfLines={1}>
          {branch.profiles?.username || t('common.unknownUser', '未知用户')}
        </Text>
      </View>

      {/* Footer with Stats and Status */}
      <View style={styles.footer}>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <MessageCircle size={14} color={theme.colors.secondaryText} />
            <Text style={styles.statText}>
              {getCountValue(branch.comment_count)}
            </Text>
          </View>
          <View style={styles.statItem}>
            <GitBranch size={14} color={theme.colors.secondaryText} />
            <Text style={styles.statText}>
              {getChildrenCount(branch.children_count)}
            </Text>
          </View>
        </View>

        {branchStatus && (
          <View
            style={[
              styles.statusBadge,
              { backgroundColor: branchStatus.color + '20' }, // 20% opacity
            ]}
          >
            <Text style={[styles.statusText, { color: branchStatus.color }]}>
              {branchStatus.label}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}
