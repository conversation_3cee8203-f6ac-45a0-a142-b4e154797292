import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    headerInfo: {
      flex: 1,
      marginLeft: theme.spacing.sm,
    },
    username: {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.text,
    },
    date: {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.secondaryText,
      marginTop: 2,
    },
    content: {
      fontFamily: theme.fonts.regular,
      fontSize: 15,
      color: theme.colors.text,
      lineHeight: 22,
    },
    footer: {
      flexDirection: 'row',
      marginTop: theme.spacing.md,
    },
    footerButton: {
      marginRight: theme.spacing.lg,
    },
    footerButtonText: {
      fontFamily: theme.fonts.medium,
      fontSize: 13,
      color: theme.colors.primary,
    },
    actions: {
      flexDirection: 'row',
    },
    actionButton: {
      padding: theme.spacing.xs,
      marginLeft: theme.spacing.xs,
    },
    editContainer: {
      marginTop: theme.spacing.xs,
    },
    editInput: {
      fontFamily: theme.fonts.regular,
      fontSize: 15,
      color: theme.colors.text,
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.sm,
      padding: theme.spacing.sm,
      minHeight: 80,
      textAlignVertical: 'top',
    },
    editActions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: theme.spacing.sm,
    },
    editButton: {
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      marginLeft: theme.spacing.sm,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
    },
    cancelText: {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.secondaryText,
    },
    saveText: {
      fontFamily: theme.fonts.medium,
      fontSize: 14,
      color: theme.colors.white,
    },
  });