import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles specific to the Content Input step
export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    flex: 1, // Allow content to fill space
    paddingHorizontal: theme.spacing.md,
  },
  title: {
    fontFamily: theme.fonts.bold,
    fontSize: 24,
    color: theme.colors.text,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
  description: {
    fontFamily: theme.fonts.regular,
    fontSize: 14,
    color: theme.colors.secondaryText,
    marginBottom: theme.spacing.lg,
    lineHeight: 20,
  },
  inputContainer: {
    flex: 1, // Make input container grow
    position: 'relative', // For word count positioning
    marginBottom: theme.spacing.md,
  },
  input: {
    flex: 1, // Make text input fill container
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.roundness,
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl, // Extra padding at bottom for word count
    fontSize: 16,
    fontFamily: theme.fonts.regular, // Consider a serif font for story content?
    backgroundColor: theme.colors.surface,
    color: theme.colors.text,
    textAlignVertical: 'top', // Align text to top for multiline
  },
  wordCount: {
    position: 'absolute',
    bottom: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: theme.colors.surface + 'aa', // Slightly transparent background
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.roundness,
  },
  wordCountText: {
    fontSize: 12,
    fontFamily: theme.fonts.regular,
    color: theme.colors.secondaryText,
  },
  aiButton: { // Reusing styles from TitleInputStep, consider a shared Button component later
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.sm,
    borderRadius: theme.roundness,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
    marginBottom: theme.spacing.md,
  },
  aiButtonText: {
    fontFamily: theme.fonts.medium,
    fontSize: 14,
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
  },
}); 