import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.xl,
    },
    message: {
      fontSize: theme.fontSizes.lg,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginTop: theme.spacing.lg,
      marginBottom: theme.spacing.md,
      textAlign: 'center',
    },
    subMessage: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
  });