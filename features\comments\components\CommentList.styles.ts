import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
    },
    title: {
      fontFamily: theme.fonts.bold,
      fontSize: 18,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    listContent: {
      flexGrow: 1,
      paddingBottom: theme.spacing.xl,
    },
    loadingIndicator: {
      marginVertical: theme.spacing.xl,
    },
    errorContainer: {
      padding: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    errorText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.error,
      textAlign: 'center',
    },
    emptyContainer: {
      padding: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
    repliesContainer: {
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      paddingLeft: theme.spacing.xl,
      borderLeftWidth: 2,
      borderLeftColor: theme.colors.border,
    },
    repliesTitle: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      marginLeft: theme.spacing.sm,
    },
    replyContainer: {
      marginLeft: theme.spacing.sm,
    },
  });