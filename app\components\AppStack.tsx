import React from 'react';
import { Stack } from 'expo-router';

export default function AppStack() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      {/* Define all top-level layouts/routes here */}
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="(auth)" options={{ headerShown: false }} />
      <Stack.Screen name="(settings)" options={{ headerShown: false }} />
      <Stack.Screen name="(profile)" options={{ headerShown: false }} />
      <Stack.Screen name="stories/[id]" options={{ headerShown: false }} />
      <Stack.Screen name="rankings" options={{ headerShown: true }} />
      <Stack.Screen name="+not-found" options={{ title: 'Oops!' }} />
    </Stack>
  );
}
