import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './DiscoverTab.styles';
import SearchBar from '@/components/ui/SearchBar';
import UserCard from '@/components/social/UserCard';
import { TopicChip } from './TopicChip';
import { useTranslation } from 'react-i18next';
import { mockUsers } from '@/utils/mockData'; // 暂时使用模拟数据
import { useRouter } from 'expo-router';
import { followUser, unfollowUser, checkIfFollowing } from '@/api/follows';
import { searchUsers } from '@/api/profiles';
import { useAuthStore } from '@/lib/store/authStore';

interface DiscoverTabProps {
  // Props for recommended users and topics if fetched
}

const MOCK_TOPICS = [
  '#AI创作',
  '#科幻世界',
  '#奇幻冒险',
  '#悬疑推理',
  '#都市传说',
  '#历史架空',
  '#青春文学',
  '#职场故事',
];

export function DiscoverTab({}: DiscoverTabProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();
  const currentUser = useAuthStore((state) => state.user);

  // 状态管理
  const [users, setUsers] = useState(mockUsers);
  const [followingUsers, setFollowingUsers] = useState<Set<string>>(new Set());
  const [isFollowingInProgress, setIsFollowingInProgress] = useState<
    string | null
  >(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载用户数据
  useEffect(() => {
    const loadUsers = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // 获取推荐用户
        const { data, error } = await searchUsers('', 10); // 空查询获取所有用户

        if (error) throw error;

        if (data && data.length > 0) {
          // 转换为应用中使用的User类型
          const mappedUsers = data.map((profile) => ({
            id: profile.id,
            username: profile.username || '',
            displayName: profile.full_name || profile.username || '',
            email: '', // 不暴露邮箱
            avatar: profile.avatar_url || '',
            bio: profile.bio || '',
            memberSince: '',
            isPremium: profile.is_premium || false,
            premiumUntil: null,
            followers: profile.followers_count || 0,
            following: profile.following_count || 0,
            storiesCount: profile.stories_count || 0,
            branchesCount: 0,
            likesReceived: 0,
            stats: {
              totalStories: profile.stories_count || 0,
              totalBranches: 0,
              totalLikes: 0,
              totalViews: 0,
              avgCompletionRate: 0,
              popularThemes: [],
              contributionStreak: 0,
              lastActive: '',
            },
          }));

          setUsers(mappedUsers);

          // 检查当前用户关注状态
          if (currentUser) {
            const followingSet = new Set<string>();

            // 对每个用户检查是否已关注
            for (const user of mappedUsers) {
              const { data: isFollowing } = await checkIfFollowing(user.id);
              if (isFollowing) {
                followingSet.add(user.id);
              }
            }

            setFollowingUsers(followingSet);
          }
        }
      } catch (err: any) {
        console.error('Failed to load users:', err);
        setError(err.message);
        // 保留模拟数据作为后备
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, [currentUser]);

  // 处理关注/取消关注用户
  const handleFollowUser = async (userId: string) => {
    if (!currentUser) {
      Alert.alert(
        t('error', '错误'),
        t('social.userProfile.errors.loginRequired', '请先登录')
      );
      return;
    }

    setIsFollowingInProgress(userId);

    try {
      const isCurrentlyFollowing = followingUsers.has(userId);

      if (isCurrentlyFollowing) {
        // 取消关注
        const { success, error } = await unfollowUser(userId);
        if (error) throw error;

        if (success) {
          setFollowingUsers((prev) => {
            const updated = new Set(prev);
            updated.delete(userId);
            return updated;
          });

          // 更新用户数据中的关注者数量
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === userId
                ? { ...user, followers: Math.max(0, user.followers - 1) }
                : user
            )
          );
        }
      } else {
        // 关注用户
        const { data, error } = await followUser(userId);
        if (error) throw error;

        if (data) {
          setFollowingUsers((prev) => {
            const updated = new Set(prev);
            updated.add(userId);
            return updated;
          });

          // 更新用户数据中的关注者数量
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === userId
                ? { ...user, followers: user.followers + 1 }
                : user
            )
          );
        }
      }

      // 显示成功消息
      Alert.alert(
        t('success', '成功'),
        followingUsers.has(userId)
          ? t('social.discover.userUnfollowedSuccess', '已取消关注')
          : t('social.discover.userFollowedSuccess', '关注成功')
      );
    } catch (err) {
      console.error('Follow toggle failed:', err);
      Alert.alert(
        t('error', '错误'),
        t('social.userProfile.followError', '操作失败，请重试')
      );
    } finally {
      setIsFollowingInProgress(null);
    }
  };

  // 处理用户卡片点击事件，跳转到用户资料页面
  const handleUserPress = (userId: string) => {
    console.log('Navigate to user profile:', userId);
    // 跳转到用户个人资料页面
    router.push(`/users/${userId}`);
  };

  const handleTopicPress = (topic: string) => {
    console.log('Topic pressed:', topic);
    // 导航到主题搜索结果
    router.push(`/search?topic=${encodeURIComponent(topic.substring(1))}`); // 移除#号
  };

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    // 这里实现实时搜索逻辑
    if (text.trim().length > 0) {
      // 如果搜索内容不为空，可以导航到搜索结果页
      console.log('Search for:', text);
      // router.push(`/search?q=${encodeURIComponent(text)}`);
    }
  };

  return (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.searchBarContainer}>
        <SearchBar
          onSearch={handleSearch}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <Text style={styles.sectionTitle}>
        {t('social.discover.recommendedAuthorsTitle')}
      </Text>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : (
        <View style={styles.usersGrid}>
          {users.map((user) => (
            <UserCard
              key={user.id}
              user={user}
              onPress={handleUserPress}
              onFollow={() => handleFollowUser(user.id)}
              isFollowing={followingUsers.has(user.id)}
              isFollowingInProgress={isFollowingInProgress === user.id}
            />
          ))}
        </View>
      )}

      <Text style={styles.sectionTitle}>
        {t('social.discover.popularTopicsTitle')}
      </Text>
      <View style={styles.topicsContainer}>
        {MOCK_TOPICS.map((topic) => (
          <TopicChip
            key={topic}
            topic={topic}
            onPress={() => handleTopicPress(topic)}
          />
        ))}
      </View>
    </ScrollView>
  );
}
