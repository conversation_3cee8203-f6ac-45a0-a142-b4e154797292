import React from 'react';
import { render } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import { Avatar } from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>
    {children}
  </PaperProvider>
);

describe('Avatar', () => {
  it('renders Avatar.Image when uri is provided', () => {
    const testUri = 'https://example.com/avatar.jpg';
    const { getByTestId } = render(
      <TestWrapper>
        <Avatar uri={testUri} username="John Doe" size={50} />
      </TestWrapper>
    );

    // Note: This test might need adjustment based on how react-native-paper
    // Avatar.Image renders internally and what testID it provides
    expect(() => getByTestId('avatar-image')).not.toThrow();
  });

  it('renders Avatar.Text when no uri is provided', () => {
    const { getByText } = render(
      <TestWrapper>
        <Avatar username="John Doe" size={50} />
      </TestWrapper>
    );

    // Should render initials "JD" for "John Doe"
    expect(getByText('JD')).toBeTruthy();
  });

  it('renders Avatar.Text with single initial for single name', () => {
    const { getByText } = render(
      <TestWrapper>
        <Avatar username="John" size={50} />
      </TestWrapper>
    );

    // Should render initial "J" for "John"
    expect(getByText('J')).toBeTruthy();
  });

  it('renders Avatar.Text with "?" when no username is provided', () => {
    const { getByText } = render(
      <TestWrapper>
        <Avatar size={50} />
      </TestWrapper>
    );

    // Should render "?" when no username
    expect(getByText('?')).toBeTruthy();
  });

  it('uses default size when not specified', () => {
    const { getByText } = render(
      <TestWrapper>
        <Avatar username="Test User" />
      </TestWrapper>
    );

    // Should render with default size (component should still render)
    expect(getByText('TU')).toBeTruthy();
  });

  it('handles empty username gracefully', () => {
    const { getByText } = render(
      <TestWrapper>
        <Avatar username="" size={50} />
      </TestWrapper>
    );

    // Should render "?" for empty username
    expect(getByText('?')).toBeTruthy();
  });

  it('handles username with multiple spaces', () => {
    const { getByText } = render(
      <TestWrapper>
        <Avatar username="John   Middle   Doe" size={50} />
      </TestWrapper>
    );

    // Should render "JD" (first and last name initials)
    expect(getByText('JD')).toBeTruthy();
  });

  it('prefers image over text when both uri and username are provided', () => {
    const testUri = 'https://example.com/avatar.jpg';
    const { queryByText } = render(
      <TestWrapper>
        <Avatar uri={testUri} username="John Doe" size={50} />
      </TestWrapper>
    );

    // Should not render text initials when image is available
    expect(queryByText('JD')).toBeNull();
  });
});
