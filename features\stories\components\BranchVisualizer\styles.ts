import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden',
      marginVertical: theme.spacing.md,
    },
    scrollContent: {
      padding: theme.spacing.md,
      minWidth: '100%',
    },
    treeContainer: {
      minWidth: '100%',
      paddingBottom: theme.spacing.lg,
    },
    branchNodeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 2,
    },
    branchNode: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing.sm,
      borderRadius: theme.radius.sm,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    selectedNode: {
      backgroundColor: theme.colors.primaryLight,
      borderColor: theme.colors.primary,
    },
    expandButton: {
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 4,
    },
    branchIcon: {
      marginRight: theme.spacing.sm,
    },
    branchContent: {
      flex: 1,
      justifyContent: 'center',
    },
    branchText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    selectedBranchText: {
      color: theme.colors.primary,
      fontFamily: theme.fonts.bold,
    },
    branchPreview: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginTop: 2,
    },
    childrenContainer: {
      marginLeft: theme.spacing.md,
      borderLeftWidth: 1,
      borderLeftColor: theme.colors.border,
      paddingLeft: theme.spacing.xs,
    },
    emptyContainer: {
      padding: theme.spacing.lg,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginVertical: theme.spacing.md,
    },
    emptyText: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
    controlsContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      padding: theme.spacing.xs,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    controlButton: {
      width: 30,
      height: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 2,
      borderRadius: theme.radius.sm,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    controlButtonText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
    },
    // 不同可视化样式的样式
    flowContent: {
      backgroundColor: theme.colors.primaryLight,
    },
    networkContent: {
      backgroundColor: theme.colors.accentLight,
    },
    timelineContent: {
      backgroundColor: theme.colors.successLight,
    },
  });
};
