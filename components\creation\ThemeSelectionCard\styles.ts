import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      width: '48%',
      padding: theme.spacing.md,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      marginBottom: theme.spacing.md,
      alignSelf: 'flex-start',
    },
    themeName: {
      fontFamily: theme.fonts.bold,
      fontSize: theme.fontSizes.md,
      marginBottom: theme.spacing.xs,
    },
    themeDescription: {
      fontFamily: theme.fonts.regular,
      fontSize: theme.fontSizes.xs,
      marginTop: theme.spacing.xs,
      minHeight:
        (theme.fontSizes.xs * theme.lineHeights.xs * 2) / theme.fontSizes.xs,
    },
    checkmark: {
      position: 'absolute',
      top: theme.spacing.xs,
      right: theme.spacing.xs,
    },
  });
