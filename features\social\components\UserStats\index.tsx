import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface UserStatsProps {
  followers: number;
  following: number;
  storiesCount: number;
}

export default function UserStats({ followers, following, storiesCount }: UserStatsProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{followers}</Text>
        <Text style={styles.statLabel}>
          {t('social.userProfile.followers', '粉丝')}
        </Text>
      </View>

      <View style={styles.statDivider} />

      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{following}</Text>
        <Text style={styles.statLabel}>
          {t('social.userProfile.following', '关注中')}
        </Text>
      </View>

      <View style={styles.statDivider} />

      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{storiesCount}</Text>
        <Text style={styles.statLabel}>
          {t('social.userProfile.stories', '故事')}
        </Text>
      </View>
    </View>
  );
}
