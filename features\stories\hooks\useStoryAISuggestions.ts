import { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { getAISuggestions } from '@/api/ai/storyGeneration';
import { StoryWithSegments } from '@/api/stories'; // Assuming StoryWithSegments is the correct type for story

export function useStoryAISuggestions(story: StoryWithSegments | null) {
  const { t } = useTranslation();
  const [aiSegmentSuggestions, setAiSegmentSuggestions] = useState<string[]>(
    []
  );
  const [loadingAISegmentSuggestions, setLoadingAISegmentSuggestions] =
    useState(false);
  const [showAISegmentSuggestions, setShowAISegmentSuggestions] =
    useState(false);
  const [currentSegmentIsAI, setCurrentSegmentIsAI] = useState(false); // This state might be better managed where the segment is actually created

  const handleFetchAISuggestionsForSegment = async () => {
    if (!story || !story.story_segments || story.story_segments.length === 0) {
      Alert.alert(
        t('aiSuggestions.promptErrorTitle', '无法获取建议'),
        t(
          'aiSuggestions.promptErrorSegment',
          '需要一些现有内容来生成续写建议。'
        )
      );
      setShowAISegmentSuggestions(false);
      return;
    }

    const lastSegment = story.story_segments[story.story_segments.length - 1];
    let prompt = `故事标题: ${story.title}\n`;
    prompt += `前面的情节: ...${lastSegment.content.slice(-200)}\n`;
    prompt += `请基于以上内容，写三个可能的续写方向。`;

    setShowAISegmentSuggestions(true);
    setLoadingAISegmentSuggestions(true);
    setAiSegmentSuggestions([]);
    try {
      const response = await getAISuggestions({ prompt });
      if (response.error) {
        setAiSegmentSuggestions([]);
        Alert.alert(
          t('aiSuggestions.fetchErrorTitle', '获取AI建议失败'),
          response.error
        );
      } else if (response.suggestions && response.suggestions.length > 0) {
        setAiSegmentSuggestions(response.suggestions);
      } else {
        setAiSegmentSuggestions([]);
        Alert.alert(
          t('aiSuggestions.fetchErrorTitle', '获取AI建议失败'),
          t('aiSuggestions.noSuggestions', '未能生成建议。')
        );
      }
    } catch (e: any) {
      console.error('Error fetching AI segment suggestions:', e);
      setAiSegmentSuggestions([]);
      Alert.alert(
        t('aiSuggestions.fetchErrorTitle', '获取AI建议失败'),
        e.message || t('aiSuggestions.fetchError', '获取AI建议时发生未知错误。')
      );
    } finally {
      setLoadingAISegmentSuggestions(false);
    }
  };

  // The selection logic might also involve setting content in a form, so this hook might need a callback
  const handleSelectAISuggestion = (
    suggestion: string,
    onSuggestionSelect: (suggestionText: string) => void
  ) => {
    onSuggestionSelect(suggestion); // Call a callback to update the form input
    setCurrentSegmentIsAI(true); // Mark that AI contributed to the current segment being written
    setShowAISegmentSuggestions(false); // Hide suggestions after selection
    // setAiSegmentSuggestions([]); // Optionally clear suggestions
  };

  return {
    aiSegmentSuggestions,
    loadingAISegmentSuggestions,
    showAISegmentSuggestions,
    currentSegmentIsAI, // Expose this flag
    setCurrentSegmentIsAI, // Allow resetting this flag from outside (e.g., after segment submission)
    handleFetchAISuggestionsForSegment,
    handleSelectAISuggestion,
    setShowAISegmentSuggestions, // To allow manual closing
  };
}
