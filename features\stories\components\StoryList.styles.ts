import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    scrollView: {
      flex: 1,
    },
    contentContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      gap: theme.spacing.md, // Gap between list items
    },
    footerContainer: {
      padding: theme.spacing.md,
      alignItems: 'center',
      justifyContent: 'center',
    },
    footerText: {
      color: theme.colors.secondaryText,
      fontSize: 14,
    },
    errorContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.lg,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: 8,
      marginTop: theme.spacing.md,
    },
    retryButtonText: {
      color: theme.colors.onPrimary,
      fontSize: 14,
      fontWeight: 'bold',
      marginLeft: theme.spacing.sm,
    },
    loadingContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    skeletonItem: {
      height: 120,
      backgroundColor: theme.colors.border,
      borderRadius: 8,
      marginBottom: theme.spacing.md,
    },
  });
