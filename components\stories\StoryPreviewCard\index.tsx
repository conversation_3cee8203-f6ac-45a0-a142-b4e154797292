import React, { memo } from 'react';
import {
  View,
  Text,
  Image,
  Pressable,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Story as AppStory } from '@/types/story';
import { Story as ApiStory } from '@/api/stories';
import { ArrowRight } from 'lucide-react-native';
import { BlurView } from 'expo-blur';
import { createStyles } from './styles';

// 支持两种不同的Story类型
type StoryUnion = AppStory | ApiStory;

interface StoryPreviewCardProps {
  story: StoryUnion;
  onPress?: () => void;
}

/**
 * 故事预览卡片组件，展示故事的封面图片、标题和主题标签
 */
const StoryPreviewCard = memo(({ story, onPress }: StoryPreviewCardProps) => {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  // 适配不同的Story类型
  const getTitle = (): string => {
    if ('title' in story) return story.title;
    return '';
  };

  const getCoverImage = (): string => {
    if ('coverImage' in story) return story.coverImage;
    if ('cover_image_url' in story && story.cover_image_url)
      return story.cover_image_url;
    // 默认封面图片
    return 'https://picsum.photos/200/300';
  };

  const getThemes = (): string[] => {
    if ('theme' in story && Array.isArray(story.theme)) return story.theme;
    if ('tags' in story && Array.isArray(story.tags)) return story.tags || [];
    return [];
  };

  // 获取第一个主题标签
  const themes = getThemes();
  const title = getTitle();
  const coverImage = getCoverImage();

  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => [
        styles.container,
        appTheme.shadows.md,
        { opacity: pressed ? 0.9 : 1 },
      ]}
      aria-label={`Story: ${title}`}
      accessibilityRole="button"
    >
      {/* 使用Image替代ImageBackground以提高性能 */}
      <Image
        source={{ uri: coverImage }}
        style={styles.coverImage}
        resizeMode="cover"
      />

      {/* 使用LinearGradient创建更美观的渐变覆盖层 */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.85)']}
        style={styles.overlay}
      >
        <View style={styles.contentContainer}>
          {title && (
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
          )}

          <View style={styles.bottomRow}>
            <View style={styles.themesContainer}>
              {themes.slice(0, 1).map((themeName) => (
                <View key={themeName} style={styles.themeTag}>
                  <Text style={styles.themeText}>{themeName}</Text>
                </View>
              ))}
            </View>

            {Platform.OS === 'ios' ? (
              <BlurView intensity={20} style={styles.arrowContainer}>
                <ArrowRight size={16} color="#FFFFFF" />
              </BlurView>
            ) : (
              <View style={styles.arrowContainer}>
                <ArrowRight size={16} color="#FFFFFF" />
              </View>
            )}
          </View>
        </View>
      </LinearGradient>
    </Pressable>
  );
});

export default StoryPreviewCard;
