import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import CreateStoryForm from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Mock the translation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue,
  }),
}));

const mockProps = {
  title: '',
  onTitleChange: jest.fn(),
  initialContent: '',
  onContentChange: jest.fn(),
  contentFocused: false,
  onContentFocus: jest.fn(),
  onContentBlur: jest.fn(),
  isFormValid: false,
  submitting: false,
  onSubmit: jest.fn(),
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <PaperProvider theme={paperLightTheme}>
      {component}
    </PaperProvider>
  );
};

describe('CreateStoryForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with empty form', () => {
    const { getByPlaceholderText, getByText } = renderWithTheme(
      <CreateStoryForm {...mockProps} />
    );

    expect(getByPlaceholderText('输入故事标题')).toBeTruthy();
    expect(getByPlaceholderText('从前...')).toBeTruthy();
    expect(getByText('创建故事')).toBeTruthy();
  });

  it('shows character count when title is entered', () => {
    const { getByText } = renderWithTheme(
      <CreateStoryForm {...mockProps} title="Test Title" />
    );

    expect(getByText('10/100')).toBeTruthy();
  });

  it('shows content validation when content is entered', () => {
    const { getByText } = renderWithTheme(
      <CreateStoryForm {...mockProps} initialContent="Short content" />
    );

    expect(getByText(/至少需要50个字符/)).toBeTruthy();
  });

  it('calls onTitleChange when title input changes', () => {
    const { getByPlaceholderText } = renderWithTheme(
      <CreateStoryForm {...mockProps} />
    );

    const titleInput = getByPlaceholderText('输入故事标题');
    fireEvent.changeText(titleInput, 'New Title');

    expect(mockProps.onTitleChange).toHaveBeenCalledWith('New Title');
  });

  it('calls onContentChange when content input changes', () => {
    const { getByPlaceholderText } = renderWithTheme(
      <CreateStoryForm {...mockProps} />
    );

    const contentInput = getByPlaceholderText('从前...');
    fireEvent.changeText(contentInput, 'New content');

    expect(mockProps.onContentChange).toHaveBeenCalledWith('New content');
  });

  it('disables submit button when form is invalid', () => {
    const { getByText } = renderWithTheme(
      <CreateStoryForm {...mockProps} isFormValid={false} />
    );

    const submitButton = getByText('创建故事');
    expect(submitButton.props.accessibilityState?.disabled).toBe(true);
  });

  it('shows loading state when submitting', () => {
    const { getByTestId } = renderWithTheme(
      <CreateStoryForm {...mockProps} submitting={true} />
    );

    // Paper Button shows loading indicator when loading prop is true
    expect(getByTestId('button-loading-indicator')).toBeTruthy();
  });

  it('calls onSubmit when submit button is pressed', () => {
    const { getByText } = renderWithTheme(
      <CreateStoryForm {...mockProps} isFormValid={true} />
    );

    const submitButton = getByText('创建故事');
    fireEvent.press(submitButton);

    expect(mockProps.onSubmit).toHaveBeenCalled();
  });
});
