import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { User } from '@/types/user';
import { useAuthStore } from '@/lib/store/authStore';
import { mockUsers } from '@/utils/mockData'; // 临时使用模拟数据
import { getUserProfileById } from '@/api/profiles';
import { followUser, unfollowUser, checkIfFollowing } from '@/api/follows';

interface UseUserProfileProps {
  userId: string;
}

interface UseUserProfileResult {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isFollowing: boolean;
  isFollowingInProgress: boolean;
  handleFollowToggle: () => Promise<void>;
  handleSendMessage: () => void;
}

export function useUserProfile({
  userId,
}: UseUserProfileProps): UseUserProfileResult {
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);

  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFollowingInProgress, setIsFollowingInProgress] = useState(false);

  // 从服务器获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      setIsLoading(true);
      try {
        // 获取用户资料
        const { data: profileData, error: profileError } =
          await getUserProfileById(userId);

        if (profileError || !profileData) {
          throw new Error(
            profileError?.message ||
              t('social.userProfile.errors.userNotFound', '找不到该用户')
          );
        }

        // 转换为应用中使用的User类型
        const userData: User = {
          id: profileData.id,
          username: profileData.username || '',
          displayName: profileData.full_name || profileData.username || '',
          email: '', // 不暴露邮箱
          avatar: profileData.avatar_url || '',
          bio: profileData.bio || '',
          memberSince: '', // 可能需要从其他字段获取
          isPremium: profileData.is_premium || false,
          premiumUntil: null,
          followers: profileData.followers_count || 0,
          following: profileData.following_count || 0,
          storiesCount: profileData.stories_count || 0,
          branchesCount: 0, // 可能需要从其他字段获取
          likesReceived: 0, // 可能需要从其他字段获取
          stats: {
            totalStories: profileData.stories_count || 0,
            totalBranches: 0,
            totalLikes: 0,
            totalViews: 0,
            avgCompletionRate: 0,
            popularThemes: [],
            contributionStreak: 0,
            lastActive: '',
          },
        };

        setUser(userData);

        // 检查当前用户是否关注了该用户
        if (currentUser) {
          const { data: isFollowingData } = await checkIfFollowing(userId);
          setIsFollowing(!!isFollowingData);
        }
      } catch (err: any) {
        console.error('Failed to fetch user data:', err);
        setError(err.message);

        // 临时使用模拟数据作为后备
        const foundUser = mockUsers.find((u) => u.id === userId);
        if (foundUser) {
          setUser(foundUser);
          setError(null);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userId, t, currentUser]);

  // 处理关注/取消关注
  const handleFollowToggle = async () => {
    if (!currentUser) {
      Alert.alert(
        t('error', '错误'),
        t('social.userProfile.errors.loginRequired', '请先登录')
      );
      return;
    }

    setIsFollowingInProgress(true);
    try {
      if (isFollowing) {
        // 取消关注
        const { success, error } = await unfollowUser(userId);
        if (error) throw error;
        if (success) {
          setIsFollowing(false);
          // 更新用户数据中的关注者数量
          if (user) {
            setUser({
              ...user,
              followers: Math.max(0, user.followers - 1),
            });
          }
        }
      } else {
        // 关注用户
        const { data, error } = await followUser(userId);
        if (error) throw error;
        if (data) {
          setIsFollowing(true);
          // 更新用户数据中的关注者数量
          if (user) {
            setUser({
              ...user,
              followers: user.followers + 1,
            });
          }
        }
      }

      // 显示成功消息
      Alert.alert(
        t('success', '成功'),
        isFollowing
          ? t('social.userProfile.unfollowSuccess', '已取消关注')
          : t('social.userProfile.followSuccess', '关注成功')
      );
    } catch (err) {
      console.error('Follow toggle failed:', err);
      Alert.alert(
        t('error', '错误'),
        t('social.userProfile.followError', '操作失败，请重试')
      );
    } finally {
      setIsFollowingInProgress(false);
    }
  };

  // 处理发送消息
  const handleSendMessage = () => {
    // 在这里可以导航到消息页面或打开消息弹窗
    Alert.alert(
      t('info', '信息'),
      t('social.userProfile.messageFeatureNotAvailable', '消息功能即将推出')
    );
  };

  return {
    user,
    isLoading,
    error,
    isFollowing,
    isFollowingInProgress,
    handleFollowToggle,
    handleSendMessage,
  };
}
