import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  section: {
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    // No background needed here, just layout
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: theme.fonts.medium,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%', // Two cards per row with a small gap
    padding: theme.spacing.md,
    borderRadius: theme.roundness,
    backgroundColor: theme.colors.surface, // Use surface color
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.border,
  },
  iconContainer: {
    marginBottom: theme.spacing.sm,
  },
  statValue: {
    fontFamily: theme.fonts.bold,
    fontSize: 20, 
    color: theme.colors.text,
    marginTop: theme.spacing.xs,
  },
  statLabel: {
    fontFamily: theme.fonts.regular,
    fontSize: 12, 
    color: theme.colors.secondaryText, // Use defined secondary text color
    marginTop: theme.spacing.xs,
    textAlign: 'center', 
  },
}); 