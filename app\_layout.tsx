import '@/lib/i18n/config';
import { useEffect } from 'react';
import { SplashScreen } from 'expo-router';
import { ThemeProvider } from '@/lib/theme/ThemeProvider';
import {
  ensureSettingsStoreHydrated,
  useSettingsStore,
} from '@/lib/store/settingsStore';
import { useAuthStore } from '@/lib/store/authStore';

// Import custom hooks and components
import useFontLoader from './hooks/useFontLoader';
import useAuthRedirect from './hooks/useAuthRedirect';
import useSplashScreen from './hooks/useSplashScreen';
import ThemedStatusBar from './components/ThemedStatusBar';
import AppStack from './components/AppStack';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  // Ensure settings store starts its hydration process
  useEffect(() => {
    ensureSettingsStoreHydrated();
  }, []);

  // Load fonts
  const { fontsLoaded, fontError } = useFontLoader();

  // Wait for Zustand store hydration
  const settingsHasHydrated = useSettingsStore((state) => state._hasHydrated);
  const authHasHydrated = useAuthStore((state) => state._hasHydrated);
  const { initializeAuth } = useAuthStore();

  // Initialize auth state on mount if auth store has rehydrated
  useEffect(() => {
    if (authHasHydrated) {
      initializeAuth();
    }
  }, [authHasHydrated, initializeAuth]);

  // Handle authentication-based redirects
  const { authIsInitialized } = useAuthRedirect({
    fontsLoaded,
    fontError,
    settingsHasHydrated,
    authHasHydrated,
  });

  // Handle splash screen
  const { shouldShowSplash } = useSplashScreen({
    fontsLoaded,
    fontError,
    settingsHasHydrated,
    authHasHydrated,
    authIsInitialized,
  });

  // Keep splash screen visible while loading assets
  if (shouldShowSplash) {
    return null;
  }

  return (
    <ThemeProvider>
      <AppStack />
      <ThemedStatusBar />
    </ThemeProvider>
  );
}
