import React, { useMemo } from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStorySegmentItemStyles } from './StorySegmentItem.styles';
import { StorySegment } from '@/api/stories'; // Path to StorySegment type

interface StorySegmentItemProps {
  segment: StorySegment;
  isFirst: boolean;
  isLast: boolean;
  showDivider?: boolean; // Optional: to show divider between items
}

// Helper function from original component, can be moved to a util if used elsewhere
const formatDate = (isoString: string) => {
  const date = new Date(isoString);
  return (
    date.toLocaleDateString() +
    ' ' +
    date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  );
};

export default function StorySegmentItem({
  segment,
  isFirst,
  isLast,
  showDivider = true,
}: StorySegmentItemProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = useMemo(() => createStorySegmentItemStyles(theme), [theme]);

  return (
    <View
      style={[
        styles.segmentCard,
        isFirst && styles.firstSegmentCard,
        isLast && styles.lastSegmentCard,
      ]}
    >
      {showDivider && !isFirst && <View style={styles.segmentDivider} />}
      <Text style={styles.segmentContent}>{segment.content}</Text>
      {segment.is_ai_generated && (
        <Text style={styles.aiIndicator}>
          {t('storyDetail.aiGenerated', 'AI Assisted')}
        </Text>
      )}
      <View style={styles.segmentFooter}>
        <Text style={styles.segmentAuthor}>
          {segment.profiles?.username ||
            t('storyDetail.unknownAuthor', 'Unknown Author')}
        </Text>
        <Text style={styles.segmentTimestamp}>
          {formatDate(segment.created_at)}
        </Text>
      </View>
    </View>
  );
}
