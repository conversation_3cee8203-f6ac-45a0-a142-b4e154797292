import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      // backgroundColor: theme.colors.background, // Already handled by SafeAreaView in profile.tsx route file
    },
    scrollViewContent: {
      paddingBottom: theme.spacing.lg, // Ensure space for content near bottom tab bar if any
    },
    // Centered container for loading/auth prompt states
    container_centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.background, // Explicitly set background for these full-screen states
    },
    authPromptText: {
      fontSize: 16,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
    },
    authButton: {
      width: '80%', // Or a fixed width like 200
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      backgroundColor: theme.colors.primary,
      borderRadius: theme.roundness * 2,
      alignItems: 'center',
      marginBottom: theme.spacing.md,
      minHeight: 48, // Ensure good touch target size
      justifyContent: 'center',
    },
    registerButton: {
      backgroundColor: theme.colors.accent, // Or another color for distinction
      // If using the same color as primary, you can remove this line or adjust styles as needed
    },
    authButtonText: {
      color: theme.colors.onPrimary, // Ensure this color is defined in your theme for primary button text
      fontSize: 16,
      fontFamily: theme.fonts.bold,
    },
    section: {
      marginTop: theme.spacing.lg,
      paddingHorizontal: theme.spacing.md, // Consistent horizontal padding for sections
    },
    sectionTitle: {
      fontSize: 18, // Slightly larger for section titles
      fontFamily: theme.fonts.bold,
      marginBottom: theme.spacing.md,
      color: theme.colors.text, // Use standard text color
    },
    placeholderText: {
      fontSize: 14,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText, // Use secondary text color for placeholders
      textAlign: 'center',
      padding: theme.spacing.md,
    },
    // Add errorText style
    errorText: {
      fontSize: 16,
      fontFamily: theme.fonts.regular,
      color: theme.colors.error, // Use error color from theme
      textAlign: 'center',
      padding: theme.spacing.md,
    },
    // ... any other styles your ProfileScreen might need
  });
