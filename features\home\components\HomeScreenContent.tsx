import React from 'react';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Story as ApiStory } from '@/api/stories';
import { FeaturedStory } from './FeaturedStory';
import { StoryListTabs } from './StoryListTabs';
import { StoryGrid } from './StoryGrid';

interface HomeScreenContentProps {
  featuredStory: ApiStory | null;
  stories: ApiStory[];
  storyListTabs: string[];
  activeStoryListTab: string;
  onTabPress: (tab: string) => void;
  onStoryPress: (storyId: string) => void;
}

export function HomeScreenContent({
  featuredStory,
  stories,
  storyListTabs,
  activeStoryListTab,
  onTabPress,
  onStoryPress,
}: HomeScreenContentProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <>
      {featuredStory && (
        <>
          <Text
            variant="titleLarge"
            style={{
              marginTop: theme.spacing?.lg || 16,
              marginBottom: theme.spacing?.md || 12,
            }}
          >
            {t('homeScreen.sectionTitle.featured')}
          </Text>
          <FeaturedStory
            story={featuredStory}
            onPress={() => onStoryPress(featuredStory.id)}
          />
        </>
      )}

      <StoryListTabs
        tabs={storyListTabs}
        activeTab={activeStoryListTab}
        onTabPress={onTabPress}
      />
      <StoryGrid stories={stories} onStoryPress={onStoryPress} />
    </>
  );
}
