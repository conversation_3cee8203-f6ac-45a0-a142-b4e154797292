import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from '../screens/ProfileScreen.styles';

interface ProfileScreenErrorProps {
  error: string;
}

export function ProfileScreenError({ error }: ProfileScreenErrorProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.container_centered}>
      <Text style={styles.errorText}>{error}</Text>
    </View>
  );
}
