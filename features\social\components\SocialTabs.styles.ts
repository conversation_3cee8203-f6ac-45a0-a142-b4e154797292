import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    tabsContainer: {
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: theme.colors.border, // Use theme border color
      backgroundColor: theme.colors.background, // Match background
    },
    tabs: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: theme.spacing.sm,
    },
    tab: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      alignItems: 'center',
      justifyContent: 'center',
      borderBottomWidth: 2,
      borderBottomColor: 'transparent', // Default transparent border
    },
    activeTab: {
      // Style applied dynamically in component using borderBottomColor
    },
    tabLabel: {
      fontSize: 12,
      marginTop: 4,
      color: theme.colors.secondaryText,
      fontFamily: theme.fonts.regular,
    },
    activeTabLabel: {
      color: theme.colors.primary,
      fontFamily: theme.fonts.medium,
    },
  });
