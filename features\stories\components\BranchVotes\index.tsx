import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { ThumbsUp, ThumbsDown } from 'lucide-react-native';
import { BranchVote, BranchVoteStats } from '@/api/stories';
import { useAuthStore } from '@/lib/store/authStore';

interface BranchVotesProps {
  voteStats: BranchVoteStats | null;
  userVote: BranchVote | null;
  isLoading: boolean;
  isVoting: boolean;
  onVote: (voteType: 'up' | 'down') => Promise<boolean>;
}

export default function BranchVotes({
  voteStats,
  userVote,
  isLoading,
  isVoting,
  onVote,
}: BranchVotesProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { user } = useAuthStore();

  // 处理投票
  const handleVote = async (voteType: 'up' | 'down') => {
    if (isVoting) return;
    await onVote(voteType);
  };

  // 获取投票统计
  const getVoteStats = () => {
    if (!voteStats) {
      return {
        upvotes: 0,
        downvotes: 0,
        total: 0,
      };
    }
    return voteStats;
  };

  // 检查用户是否已投票
  const hasUserVoted = (voteType: 'up' | 'down') => {
    return userVote?.vote_type === voteType;
  };

  // 渲染投票按钮
  const renderVoteButtons = () => {
    const stats = getVoteStats();

    return (
      <View style={styles.voteButtonsContainer}>
        <TouchableOpacity
          style={[
            styles.voteButton,
            hasUserVoted('up') && styles.activeUpvoteButton,
            (!user || isVoting) && styles.disabledButton,
          ]}
          onPress={() => handleVote('up')}
          disabled={!user || isVoting}
        >
          <ThumbsUp
            size={18}
            color={
              hasUserVoted('up')
                ? theme.colors.onPrimary
                : theme.colors.text
            }
          />
          <Text
            style={[
              styles.voteButtonText,
              hasUserVoted('up') && styles.activeVoteButtonText,
            ]}
          >
            {stats.upvotes}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.voteButton,
            hasUserVoted('down') && styles.activeDownvoteButton,
            (!user || isVoting) && styles.disabledButton,
          ]}
          onPress={() => handleVote('down')}
          disabled={!user || isVoting}
        >
          <ThumbsDown
            size={18}
            color={
              hasUserVoted('down')
                ? theme.colors.onPrimary
                : theme.colors.text
            }
          />
          <Text
            style={[
              styles.voteButtonText,
              hasUserVoted('down') && styles.activeVoteButtonText,
            ]}
          >
            {stats.downvotes}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染投票统计
  const renderVoteStats = () => {
    const stats = getVoteStats();
    const totalVotes = stats.upvotes + stats.downvotes;
    
    // 计算投票比例
    const upvotePercentage = totalVotes > 0 ? (stats.upvotes / totalVotes) * 100 : 0;
    const downvotePercentage = totalVotes > 0 ? (stats.downvotes / totalVotes) * 100 : 0;
    
    return (
      <View style={styles.voteStatsContainer}>
        <View style={styles.voteBarContainer}>
          <View
            style={[
              styles.upvoteBar,
              { width: `${upvotePercentage}%` },
            ]}
          />
          <View
            style={[
              styles.downvoteBar,
              { width: `${downvotePercentage}%` },
            ]}
          />
        </View>
        
        <Text style={styles.voteTotalText}>
          {t('storyDetail.voteTotal', 'Total Score')}: {stats.total}
        </Text>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.loadingText}>
            {t('storyDetail.loadingVotes', 'Loading votes...')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {t('storyDetail.branchRating', 'Branch Rating')}
        </Text>
      </View>

      <View style={styles.content}>
        {renderVoteButtons()}
        {renderVoteStats()}

        {!user && (
          <Text style={styles.loginPromptText}>
            {t('storyDetail.loginToVote', 'Please log in to vote')}
          </Text>
        )}
      </View>
    </View>
  );
}
