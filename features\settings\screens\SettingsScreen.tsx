import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
// Removed SafeAreaView as it's handled in the route file
// import { SafeAreaView } from 'react-native-safe-area-context';
// Removed useRouter as it's likely not needed here
// import { useRouter } from 'expo-router';
// Removed unused icons
// import { ChevronLeft, Globe, Moon, Sun, Monitor } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
// Removed unused state getters and setters, as they are managed within child components now
// import { useSettingsStore, ThemeMode } from '@/lib/store/settingsStore';
import { useAppTheme } from '@/hooks/useAppTheme';
// Re-import AppTheme as it's needed by createStyles
import { AppTheme } from '@/lib/theme/themes';
// Import the new child components
import { ThemeOptionsGroup } from '../components/ThemeOptionsGroup';
import { LanguageOptionsGroup } from '../components/LanguageOptionsGroup';
// Import styles from the separated file
import { createStyles } from './SettingsScreen.styles';
import { signOut as supabaseSignOut } from '@/api/auth'; // Renamed to avoid conflict
import { useAuthStore } from '@/lib/store/authStore'; // For local state clear

// Rename component to SettingsScreen
export default function SettingsScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  // Remove unused state variables and setters
  // const themeMode = useSettingsStore((state) => state.themeMode);
  // const setThemeMode = useSettingsStore((state) => state.setThemeMode);
  // const language = useSettingsStore((state) => state.language);
  // const setLanguage = useSettingsStore((state) => state.setLanguage);

  const styles = createStyles(theme);
  const clearAuthStore = useAuthStore((state) => state.signOut); // Get the signOut action

  const handleLogout = async () => {
    // First, clear local auth state
    clearAuthStore();

    // Then, sign out from Supabase
    const { error } = await supabaseSignOut();

    if (error) {
      console.error('Error signing out from Supabase:', error);
      Alert.alert(
        t('logoutErrorTitle', 'Logout Error'),
        error.message ||
          t('logoutErrorGeneric', 'Failed to sign out. Please try again.')
      );
    }
    // No explicit navigation needed here, as the RootLayout should detect
    // the absence of user/session and redirect to the login screen.
  };

  // Removed inline component definitions ThemeOption and LanguageOption

  return (
    // Use View instead of SafeAreaView, as SafeAreaView is usually applied in the layout
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title]}>{t('settings')}</Text>
      </View>

      <ScrollView>
        {' '}
        {/* Added ScrollView for potential future settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('themeSettings')}</Text>
          {/* Render the ThemeOptionsGroup component */}
          <ThemeOptionsGroup />
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('languageSettings')}</Text>
          {/* Render the LanguageOptionsGroup component */}
          <LanguageOptionsGroup />
        </View>
        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t('accountSectionTitle', 'Account')}
          </Text>
          <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
            <Text style={styles.logoutButtonText}>
              {t('logoutButton', 'Log Out')}
            </Text>
          </TouchableOpacity>
        </View>
        {/* Add other settings sections here */}
      </ScrollView>
    </View>
  );
}

// The local definition of createStyles below this line needs to be removed.
// ... existing code ...
