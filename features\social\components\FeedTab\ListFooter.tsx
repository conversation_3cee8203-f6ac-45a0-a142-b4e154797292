import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from '../FeedTab.styles';

interface ListFooterProps {
  isLoading: boolean;
}

export function ListFooter({ isLoading }: ListFooterProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  if (!isLoading) return null;

  return (
    <View style={styles.footerContainer}>
      <ActivityIndicator size="small" color={theme.colors.primary} />
      <Text style={styles.footerText}>
        {t('social.feed.loadingMore', '加载更多...')}
      </Text>
    </View>
  );
}
