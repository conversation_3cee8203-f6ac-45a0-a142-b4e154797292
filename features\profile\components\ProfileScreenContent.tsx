import React from 'react';
import { View, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Profile } from '@/api/profiles';
import { Story as ApiStory } from '@/api/stories';
import { createStyles } from '../screens/ProfileScreen.styles';
import { ProfileHeader } from './ProfileHeader';
import { ProfileStats } from './ProfileStats';
import { ProfileActions } from './ProfileActions';
import { MyStoriesSection } from './MyStoriesSection';

interface ProfileScreenContentProps {
  profile: Profile;
  userStories: ApiStory[];
  storiesLoading: boolean;
  storiesError: string | null;
  onEditProfile: () => void;
  onShareProfile: () => void;
  onStoryPress: (storyId: string) => void;
}

export function ProfileScreenContent({
  profile,
  userStories,
  storiesLoading,
  storiesError,
  onEditProfile,
  onShareProfile,
  onStoryPress,
}: ProfileScreenContentProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollViewContent}>
        <ProfileHeader
          name={
            profile.full_name ||
            profile.username ||
            t('profile.unnamedUser', 'Unnamed User')
          }
          bio={profile.bio || t('profile.noBioPlaceholder', 'No bio yet.')}
          avatarUrl={profile.avatar_url || undefined}
          isPremium={false}
        />
        <ProfileStats posts={15} followers={234} following={189} />
        <ProfileActions
          onEditProfile={onEditProfile}
          onShareProfile={onShareProfile}
        />
        <MyStoriesSection
          stories={userStories}
          onStoryPress={onStoryPress}
          loading={storiesLoading}
          error={storiesError}
        />
      </ScrollView>
    </View>
  );
}
