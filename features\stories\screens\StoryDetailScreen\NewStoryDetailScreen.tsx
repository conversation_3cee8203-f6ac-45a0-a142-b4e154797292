import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, SafeAreaView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { StorySegment } from '@/api/stories/types';

// Hooks
import { useStoryDetails } from '../../hooks/useStoryDetails';
import { useStoryFeed } from '../../hooks/useStoryFeed';
import { useStorySegmentManagement } from '../../hooks/useStorySegmentManagement';

// Components
import StoryDetailHeader from '../../components/StoryDetailHeader';
import StoryFeed from '../../components/StoryFeed';
import AddSegmentForm from '../../components/AddSegmentForm';
import BreadcrumbNavigation from '../../components/BreadcrumbNavigation';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';
import { EmptyState } from './EmptyState';

interface StoryDetailScreenProps {
  storyId: string;
}

export default function NewStoryDetailScreen({ storyId }: StoryDetailScreenProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // Get story details
  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    isLiked,
    likeCount,
    isLiking,
    fetchStoryDetails,
    handleLikeToggle,
  } = useStoryDetails(storyId);

  // Get story feed with virtualization and pagination
  const {
    segments,
    isLoading: isLoadingSegments,
    error: segmentsError,
    hasMoreSegments,
    loadMore: loadMoreSegments,
    refresh: refreshSegments,
    navigateToBranch,
  } = useStoryFeed({
    storyId,
    pageSize: 10,
  });

  // Handle adding new segments
  const {
    newSegmentContent,
    setNewSegmentContent,
    isSubmittingSegment,
    handleAddSegment,
  } = useStorySegmentManagement({
    storyId,
    onSegmentAdded: (newSegment) => {
      refreshSegments();
    },
  });

  // Handle submitting a new segment
  const submitSegment = useCallback(async () => {
    if (!newSegmentContent.trim()) {
      return;
    }

    await handleAddSegment(newSegmentContent.trim());
    setNewSegmentContent('');
  }, [newSegmentContent, handleAddSegment]);

  // Handle branch selection
  const handleBranchSelect = useCallback(
    async (segmentId: string) => {
      await navigateToBranch(segmentId);
    },
    [navigateToBranch]
  );

  // Show loading state
  if (isLoadingDetails && !story) {
    return <LoadingState />;
  }

  // Show error state
  if (storyError) {
    return <ErrorState error={storyError} onRetry={fetchStoryDetails} />;
  }

  // Show empty state
  if (!story) {
    return <EmptyState />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StoryDetailHeader
        story={story}
        isLiked={isLiked}
        likeCount={likeCount}
        onLikeToggle={handleLikeToggle}
        isLiking={isLiking}
      />

      <BreadcrumbNavigation
        storyTitle={story.title}
        currentPath={[]}
        onPathSelect={() => {}}
      />

      <View style={styles.feedContainer}>
        <StoryFeed
          segments={segments}
          isLoading={isLoadingSegments}
          onRefresh={refreshSegments}
          onLoadMore={loadMoreSegments}
          onBranchSelect={handleBranchSelect}
          hasMoreSegments={hasMoreSegments}
        />
      </View>

      <View style={styles.formContainer}>
        <AddSegmentForm
          segmentContent={newSegmentContent}
          onContentChange={setNewSegmentContent}
          onSubmit={submitSegment}
          isSubmitting={isSubmittingSegment}
        />
      </View>
    </SafeAreaView>
  );
}
