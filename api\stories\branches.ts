import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { StorySegment } from './types';

/**
 * 分支节点接口，用于构建树状结构
 */
export interface BranchNode {
  id: string;
  segment: StorySegment;
  children: BranchNode[];
  level: number;
  path: string[]; // 从根节点到当前节点的路径
  branchTitle?: string; // 分支标题（可选）
}

/**
 * 获取故事的所有分支
 *
 * @param storyId 故事ID
 * @returns 包含所有分支的树状结构
 */
export async function getStoryBranches(
  storyId: string
): Promise<{ data: BranchNode | null; error: PostgrestError | null }> {
  try {
    // 获取故事的所有段落
    const { data: segments, error } = await supabase
      .from('story_segments')
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('story_id', storyId)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    if (!segments || segments.length === 0) {
      return { data: null, error: null };
    }

    // 构建分支树
    const branchTree = buildBranchTree(segments as StorySegment[]);
    return { data: branchTree, error: null };
  } catch (error) {
    console.error('Error getting story branches:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 获取特定分支的子分支
 *
 * @param segmentId 段落ID
 * @returns 子分支列表
 */
export async function getBranchChildren(
  segmentId: string
): Promise<{ data: StorySegment[] | null; error: PostgrestError | null }> {
  try {
    // 查询子分支，并计算每个子分支的子分支数量
    const { data, error } = await supabase
      .from('story_segments')
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('parent_segment_id', segmentId)
      .order('order_in_branch', { ascending: true })
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    // 如果没有子分支，直接返回
    if (!data || data.length === 0) {
      return { data: [], error: null };
    }

    // 为每个子分支添加子分支数量
    const segmentsWithCounts = await Promise.all(
      data.map(async (segment) => {
        // 查询子分支数量
        const { count: childrenCount, error: countError } = await supabase
          .from('story_segments')
          .select('id', { count: 'exact', head: true })
          .eq('parent_segment_id', segment.id);

        if (countError) {
          console.error('Error counting children:', countError);
        }

        // 返回带有子分支数量的段落
        return {
          ...segment,
          children_count: childrenCount || 0,
        } as StorySegment;
      })
    );

    console.log('Branch children with counts:', segmentsWithCounts);

    return { data: segmentsWithCounts, error: null };
  } catch (error) {
    console.error('Error getting branch children:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 创建新分支
 *
 * @param storyId 故事ID
 * @param parentSegmentId 父段落ID
 * @param content 分支内容
 * @param branchTitle 分支标题（可选）
 * @param isAiGenerated 是否由AI生成
 * @returns 新创建的段落
 */
export async function createBranch(
  storyId: string,
  parentSegmentId: string,
  content: string,
  branchTitle?: string,
  isAiGenerated: boolean = false
): Promise<{ data: StorySegment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 获取同级分支数量，用于设置order_in_branch
    const { data: siblings, error: countError } = await supabase
      .from('story_segments')
      .select('id')
      .eq('parent_segment_id', parentSegmentId);

    if (countError) {
      throw countError;
    }

    const orderInBranch = siblings ? siblings.length : 0;

    // 创建新分支段落
    const { data, error } = await supabase
      .from('story_segments')
      .insert({
        story_id: storyId,
        author_id: user.id,
        content,
        content_type: 'text',
        parent_segment_id: parentSegmentId,
        order_in_branch: orderInBranch,
        is_ai_generated: isAiGenerated,
        // 如果有分支标题，可以存储在元数据中
        // 这里假设数据库中有metadata字段，如果没有，需要先添加该字段
        // metadata: branchTitle ? { branchTitle } : null,
      })
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .single();

    if (error) {
      throw error;
    }

    return { data: data as StorySegment, error: null };
  } catch (error) {
    console.error('Error creating branch:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 从平面段落列表构建分支树
 *
 * @param segments 段落列表
 * @returns 分支树的根节点
 */
function buildBranchTree(segments: StorySegment[]): BranchNode | null {
  if (!segments || segments.length === 0) {
    return null;
  }

  // 创建段落ID到段落的映射
  const segmentMap = new Map<string, StorySegment>();
  segments.forEach((segment) => {
    segmentMap.set(segment.id, segment);
  });

  // 创建子节点映射
  const childrenMap = new Map<string | null, string[]>();
  segments.forEach((segment) => {
    const parentId = segment.parent_segment_id || null;
    if (!childrenMap.has(parentId)) {
      childrenMap.set(parentId, []);
    }
    childrenMap.get(parentId)?.push(segment.id);
  });

  // 查找根节点（没有父节点的段落）
  const rootSegmentId = childrenMap.get(null)?.[0];
  if (!rootSegmentId) {
    return null;
  }

  // 递归构建树
  function buildNode(
    segmentId: string,
    level: number,
    path: string[]
  ): BranchNode {
    const segment = segmentMap.get(segmentId)!;
    const childIds = childrenMap.get(segmentId) || [];
    const children = childIds
      .map((childId) => buildNode(childId, level + 1, [...path, segmentId]))
      .sort((a, b) => {
        // 按order_in_branch和created_at排序
        if (a.segment.order_in_branch !== b.segment.order_in_branch) {
          return a.segment.order_in_branch - b.segment.order_in_branch;
        }
        return (
          new Date(a.segment.created_at).getTime() -
          new Date(b.segment.created_at).getTime()
        );
      });

    return {
      id: segmentId,
      segment,
      children,
      level,
      path,
      // 这里可以添加分支标题逻辑，如果数据库中有metadata字段
      // branchTitle: segment.metadata?.branchTitle,
    };
  }

  return buildNode(rootSegmentId, 0, []);
}

/**
 * 获取特定分支路径
 *
 * @param storyId 故事ID
 * @param segmentId 当前段落ID
 * @returns 从根节点到当前段落的路径
 */
export async function getBranchPath(
  storyId: string,
  segmentId: string
): Promise<{ data: StorySegment[] | null; error: PostgrestError | null }> {
  try {
    // 获取当前段落
    const { data: currentSegment, error: segmentError } = await supabase
      .from('story_segments')
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('id', segmentId)
      .single();

    if (segmentError || !currentSegment) {
      throw segmentError || new Error('Segment not found');
    }

    const path: StorySegment[] = [currentSegment as StorySegment];
    let parentId = currentSegment.parent_segment_id;

    // 递归获取所有父段落
    while (parentId) {
      const { data: parentSegment, error: parentError } = await supabase
        .from('story_segments')
        .select(
          `
          id, story_id, author_id, content_type, content, parent_segment_id,
          order_in_branch, is_ai_generated, created_at, updated_at,
          likes_count, dislikes_count, bookmarks_count, comment_count,
          profiles!story_segments_author_id_fkey ( id, username, avatar_url )
        `
        )
        .eq('id', parentId)
        .single();

      if (parentError || !parentSegment) {
        throw parentError || new Error('Parent segment not found');
      }

      path.unshift(parentSegment as StorySegment);
      parentId = parentSegment.parent_segment_id;
    }

    return { data: path, error: null };
  } catch (error) {
    console.error('Error getting branch path:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 重命名分支（更新分支元数据）
 *
 * @param segmentId 段落ID
 * @param branchTitle 分支标题
 * @returns 更新后的段落
 */
export async function renameBranch(
  segmentId: string,
  branchTitle: string
): Promise<{ data: StorySegment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 首先检查段落是否存在并且用户是否有权限修改
    const { data: segment, error: checkError } = await supabase
      .from('story_segments')
      .select('id, author_id, story_id')
      .eq('id', segmentId)
      .single();

    if (checkError || !segment) {
      throw checkError || new Error('Segment not found');
    }

    // 检查用户是否是段落的作者
    if (segment.author_id !== user.id) {
      return {
        data: null,
        error: {
          message: 'Permission denied: You can only rename your own branches',
          details: '',
          hint: '',
          code: '403',
        } as PostgrestError,
      };
    }

    // 更新段落的元数据
    // 注意：这里假设数据库中有metadata字段，如果没有，需要先添加该字段
    // 这里我们使用一个临时解决方案，将分支标题存储在content字段的开头，格式为：[分支标题] 内容
    // 在实际项目中，应该添加一个专门的metadata字段或branch_title字段

    // 获取当前内容
    const { data: currentSegment, error: getError } = await supabase
      .from('story_segments')
      .select('content')
      .eq('id', segmentId)
      .single();

    if (getError || !currentSegment) {
      throw getError || new Error('Failed to get current segment content');
    }

    // 检查内容是否已经有标题格式
    const titleRegex = /^\[.*?\]\s/;
    let newContent = currentSegment.content;

    if (titleRegex.test(newContent)) {
      // 如果已经有标题，替换它
      newContent = newContent.replace(titleRegex, `[${branchTitle}] `);
    } else {
      // 如果没有标题，添加一个
      newContent = `[${branchTitle}] ${newContent}`;
    }

    // 更新内容
    const { data, error } = await supabase
      .from('story_segments')
      .update({ content: newContent })
      .eq('id', segmentId)
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .single();

    if (error) {
      throw error;
    }

    return { data: data as StorySegment, error: null };
  } catch (error) {
    console.error('Error renaming branch:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 删除分支
 *
 * @param segmentId 段落ID
 * @returns 操作结果
 */
export async function deleteBranch(
  segmentId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 首先检查段落是否存在并且用户是否有权限删除
    const { data: segment, error: checkError } = await supabase
      .from('story_segments')
      .select('id, author_id, story_id, parent_segment_id')
      .eq('id', segmentId)
      .single();

    if (checkError || !segment) {
      throw checkError || new Error('Segment not found');
    }

    // 检查用户是否是段落的作者
    if (segment.author_id !== user.id) {
      return {
        success: false,
        error: {
          message: 'Permission denied: You can only delete your own branches',
          details: '',
          hint: '',
          code: '403',
        } as PostgrestError,
      };
    }

    // 检查是否有子分支
    const { data: children, error: childrenError } = await supabase
      .from('story_segments')
      .select('id')
      .eq('parent_segment_id', segmentId);

    if (childrenError) {
      throw childrenError;
    }

    // 如果有子分支，不允许删除
    if (children && children.length > 0) {
      return {
        success: false,
        error: {
          message:
            'Cannot delete a branch with children. Delete all child branches first.',
          details: '',
          hint: '',
          code: '400',
        } as PostgrestError,
      };
    }

    // 删除段落
    const { error } = await supabase
      .from('story_segments')
      .delete()
      .eq('id', segmentId);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting branch:', error);
    return {
      success: false,
      error: error as PostgrestError,
    };
  }
}
