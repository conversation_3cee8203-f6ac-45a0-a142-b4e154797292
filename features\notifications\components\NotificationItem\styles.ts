import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.background,
    },
    unreadContainer: {
      backgroundColor: theme.colors.surface,
      borderLeftWidth: 3,
      borderLeftColor: theme.colors.primary,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.surfaceVariant,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    contentContainer: {
      flex: 1,
    },
    actorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    avatar: {
      width: 24,
      height: 24,
      borderRadius: 12,
      marginRight: theme.spacing.xs,
    },
    placeholderAvatar: {
      backgroundColor: theme.colors.placeholder,
    },
    actorName: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    title: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    body: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginBottom: theme.spacing.xs,
    },
    time: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
      color: theme.colors.tertiaryText,
    },
    deleteButton: {
      justifyContent: 'center',
      paddingHorizontal: theme.spacing.md,
    },
    deleteText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.error,
    },
  });