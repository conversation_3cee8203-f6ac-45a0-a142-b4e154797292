import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles for the main StoriesScreen layout
export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    flex: 1, // Allow list or empty state to fill space
  },
  // Styles for tabs, list, empty state will be in their own files
}); 