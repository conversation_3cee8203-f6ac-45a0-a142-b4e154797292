import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles for the main CreateScreen and layout elements
export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  // Styles for step content containers might go here if shared, 
  // but specific step styles will be in their component styles.
  
  // Footer styles can also go here or be separated later
  footer: {
    padding: theme.spacing.md,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: theme.colors.border,
    backgroundColor: theme.colors.background, // Ensure footer matches background
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md, // Make button taller
    borderRadius: theme.roundness * 2, // More rounded
    // Background color and opacity handled dynamically in component
  },
  nextButtonText: {
    fontFamily: theme.fonts.bold,
    fontSize: 16,
    marginRight: theme.spacing.sm,
    // Color handled dynamically
  },
  // Note: Other styles like stepTitle, stepDescription, inputs, etc.
  // will be moved to their respective component style files.
}); 