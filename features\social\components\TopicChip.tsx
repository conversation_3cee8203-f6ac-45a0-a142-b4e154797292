import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './TopicChip.styles';

interface TopicChipProps {
  topic: string;
  onPress?: (topic: string) => void;
}

export function TopicChip({ topic, onPress }: TopicChipProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <TouchableOpacity 
      style={styles.chip} 
      onPress={() => onPress?.(topic)}
      disabled={!onPress}
    >
      <Text style={styles.text}>{topic}</Text>
    </TouchableOpacity>
  );
} 