import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    badge: {
      position: 'absolute',
      top: -5,
      right: -5,
      backgroundColor: theme.colors.error,
      borderRadius: 100,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    badgeSmall: {
      minWidth: 16,
      height: 16,
      paddingHorizontal: 2,
    },
    badgeMedium: {
      minWidth: 20,
      height: 20,
      paddingHorizontal: 4,
    },
    badgeLarge: {
      minWidth: 24,
      height: 24,
      paddingHorizontal: 6,
    },
    text: {
      color: theme.colors.background,
      fontFamily: theme.fonts.medium,
      textAlign: 'center',
    },
    textSmall: {
      fontSize: 10,
    },
    textMedium: {
      fontSize: 12,
    },
    textLarge: {
      fontSize: 14,
    },
  });