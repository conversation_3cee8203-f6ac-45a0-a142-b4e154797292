import React from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import HeaderBar from '@/components/ui/HeaderBar';
import { createStyles } from '@/features/stories/screens/StoriesScreen.styles';
import { useTranslation } from 'react-i18next';

// Import feature components
import { StoryTabs } from '@/features/stories/components/StoryTabs';
import { StoryList } from '@/features/stories/components/StoryList';
import { EmptyStoriesState } from '@/features/stories/components/EmptyStoriesState';
import { useRouter } from 'expo-router';
import { useStoriesScreen } from '@/features/stories/hooks/useStoriesScreen';

export default function StoriesScreen() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const { t } = useTranslation();

  // Use our custom hook for stories screen
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
  } = useStoriesScreen();

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push(`/story/${storyId}`);
  };

  const handleGoToCreate = () => {
    router.push('/(tabs)/create');
  };

  // Render the content based on whether stories exist for the tab
  const renderContent = () => {
    if (!isLoading && !isRefreshing && stories.length === 0) {
      let emptyTitle: string | undefined;
      let emptySubtitle: string | undefined;
      let showCreateButton = activeTab === 'drafts';
      let icon: React.ReactNode | undefined;

      switch (activeTab) {
        case 'drafts':
          emptyTitle = t('stories.emptyDrafts.title', '还没有草稿');
          emptySubtitle = t(
            'stories.emptyDrafts.subtitle',
            '点击创作按钮开始你的第一个故事吧！'
          );
          break;
        case 'published':
          emptyTitle = t('stories.emptyPublished.title', '还没有已发布的故事');
          emptySubtitle = t(
            'stories.emptyPublished.subtitle',
            '完成的故事会出现在这里。'
          );
          break;
        case 'reading':
          emptyTitle = t('stories.emptyReading.title', '没有正在阅读的故事');
          emptySubtitle = t(
            'stories.emptyReading.subtitle',
            '开始阅读或创作新故事吧。'
          );
          break;
        case 'favorites':
          emptyTitle = t('stories.emptyFavorites.title', '还没有喜欢的故事');
          emptySubtitle = t(
            'stories.emptyFavorites.subtitle',
            '为你喜欢的故事点赞吧。'
          );
          break;
        case 'saved':
          emptyTitle = t('stories.emptySaved.title', '还没有收藏的故事');
          emptySubtitle = t(
            'stories.emptySaved.subtitle',
            '收藏你感兴趣的故事稍后阅读。'
          );
          break;
        default:
          emptyTitle = t('stories.emptyGeneric.title', '这里空空如也');
      }

      return (
        <EmptyStoriesState
          title={emptyTitle}
          subtitle={emptySubtitle}
          onActionPress={showCreateButton ? handleGoToCreate : undefined}
          actionText={
            showCreateButton
              ? t('stories.actions.startWriting', '开始创作')
              : undefined
          }
          icon={icon}
        />
      );
    }

    return (
      <StoryList
        stories={stories}
        onStoryPress={handleStoryPress}
        isLoading={isLoading}
        isRefreshing={isRefreshing}
        onRefresh={refreshStories}
        onLoadMore={loadMoreStories}
        hasMoreStories={hasMoreStories}
        error={error}
        onRetry={retryFetch}
        retryCount={retryCount}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <HeaderBar title={t('stories.title', '我的故事')} />

      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />

      <View style={styles.contentContainer}>{renderContent()}</View>
    </SafeAreaView>
  );
}

// Removed the temporary localStyles definition
