import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './SocialTabs.styles';
import { Bell, MessageSquare, Search, TrendingUp } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import NotificationBadge from '@/features/notifications/components/NotificationBadge';
import MessageBadge from '@/features/messages/components/MessageBadge';
import { getUnreadNotificationCount } from '@/api/notifications';
import { getUnreadMessageCount } from '@/api/messages';

export type SocialTabKey = 'feed' | 'discover' | 'messages' | 'notifications';

interface SocialTabsProps {
  activeTab: SocialTabKey;
  onTabPress: (tabKey: SocialTabKey) => void;
}

const TABS: SocialTabKey[] = ['feed', 'discover', 'messages', 'notifications'];

const renderTabIcon = (
  tabKey: SocialTabKey,
  active: boolean,
  theme: ReturnType<typeof useAppTheme>,
  unreadNotificationCount: number = 0,
  unreadMessageCount: number = 0
) => {
  const iconColor = active ? theme.colors.primary : theme.colors.secondaryText;
  const size = 22;

  switch (tabKey) {
    case 'feed':
      return <TrendingUp size={size} color={iconColor} />;
    case 'discover':
      return <Search size={size} color={iconColor} />;
    case 'messages':
      return (
        <View>
          <MessageSquare size={size} color={iconColor} />
          {unreadMessageCount > 0 && tabKey === 'messages' && (
            <MessageBadge count={unreadMessageCount} size="small" />
          )}
        </View>
      );
    case 'notifications':
      return (
        <View>
          <Bell size={size} color={iconColor} />
          {unreadNotificationCount > 0 && tabKey === 'notifications' && (
            <NotificationBadge count={unreadNotificationCount} size="small" />
          )}
        </View>
      );
    default:
      return null;
  }
};

export function SocialTabs({ activeTab, onTabPress }: SocialTabsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0);
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);

  // 获取未读通知数量
  useEffect(() => {
    const fetchUnreadNotificationCount = async () => {
      try {
        const { count } = await getUnreadNotificationCount();
        setUnreadNotificationCount(count);
      } catch (error) {
        console.error('Error fetching unread notification count:', error);
      }
    };

    fetchUnreadNotificationCount();

    // 设置定时器，每分钟刷新一次
    const intervalId = setInterval(fetchUnreadNotificationCount, 60000);

    return () => clearInterval(intervalId);
  }, []);

  // 获取未读消息数量
  useEffect(() => {
    const fetchUnreadMessageCount = async () => {
      try {
        const { count } = await getUnreadMessageCount();
        setUnreadMessageCount(count);
      } catch (error) {
        console.error('Error fetching unread message count:', error);
      }
    };

    fetchUnreadMessageCount();

    // 设置定时器，每分钟刷新一次
    const intervalId = setInterval(fetchUnreadMessageCount, 60000);

    return () => clearInterval(intervalId);
  }, []);

  const getTabLabel = (tabKey: SocialTabKey): string => {
    switch (tabKey) {
      case 'feed':
        return t('social.tabs.feed', '动态');
      case 'discover':
        return t('social.tabs.discover', '发现');
      case 'messages':
        return t('social.tabs.messages', '消息');
      case 'notifications':
        return t('social.tabs.notifications', '通知');
      default:
        return '';
    }
  };

  return (
    <View style={styles.tabsContainer}>
      <View style={styles.tabs}>
        {TABS.map((tabKey) => (
          <TouchableOpacity
            key={tabKey}
            style={[
              styles.tab,
              activeTab === tabKey && {
                borderBottomColor: theme.colors.primary,
              },
            ]}
            onPress={() => onTabPress(tabKey)}
            accessibilityLabel={getTabLabel(tabKey)}
            accessibilityRole="tab"
            accessibilityState={{ selected: activeTab === tabKey }}
          >
            {renderTabIcon(
              tabKey,
              activeTab === tabKey,
              theme,
              unreadNotificationCount,
              unreadMessageCount
            )}
            <Text
              style={[
                styles.tabLabel,
                activeTab === tabKey && styles.activeTabLabel,
              ]}
            >
              {getTabLabel(tabKey)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
