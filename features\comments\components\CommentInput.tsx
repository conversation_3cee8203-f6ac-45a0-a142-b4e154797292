import React, { useState } from 'react';
import { View, TextInput, TouchableOpacity, Text, Alert, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './CommentInput.styles';
import { Comment, addStoryComment } from '@/api/comments';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/lib/store/authStore';
import { Ionicons } from '@expo/vector-icons';

interface CommentInputProps {
  storyId: string;
  parentCommentId?: string | null;
  onCommentAdded: (comment: Comment) => void;
  onCancel?: () => void;
  placeholder?: string;
}

export function CommentInput({
  storyId,
  parentCommentId = null,
  onCommentAdded,
  onCancel,
  placeholder,
}: CommentInputProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);
  
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async () => {
    if (!currentUser) {
      Alert.alert(
        t('error', '错误'),
        t('comments.loginRequired', '请先登录后再评论')
      );
      return;
    }
    
    if (content.trim() === '') {
      Alert.alert(
        t('error', '错误'),
        t('comments.emptyCommentError', '评论内容不能为空')
      );
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const { data, error } = await addStoryComment(
        storyId,
        content,
        parentCommentId || undefined
      );
      
      if (error) throw error;
      
      if (data) {
        onCommentAdded(data);
        setContent('');
      }
    } catch (err) {
      console.error('Failed to add comment:', err);
      Alert.alert(
        t('error', '错误'),
        t('comments.addError', '发表评论失败，请重试')
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        value={content}
        onChangeText={setContent}
        placeholder={placeholder || t('comments.placeholder', '发表评论...')}
        placeholderTextColor={theme.colors.secondaryText}
        multiline
        maxLength={1000}
      />
      
      <View style={styles.actions}>
        {onCancel && (
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onCancel}
            disabled={isSubmitting}
          >
            <Text style={styles.cancelText}>{t('cancel', '取消')}</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[
            styles.submitButton,
            (content.trim() === '' || isSubmitting) && styles.submitButtonDisabled,
          ]}
          onPress={handleSubmit}
          disabled={content.trim() === '' || isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color={theme.colors.white} />
          ) : (
            <>
              <Ionicons name="send" size={16} color={theme.colors.white} />
              <Text style={styles.submitText}>{t('comments.submit', '发送')}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}