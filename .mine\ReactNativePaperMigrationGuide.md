# React Native Paper 迁移指南

## 概述

本文档提供了将现有组件从传统的 StyleSheet 方式迁移到 react-native-paper 的标准流程和最佳实践。

## 迁移原则

### 1. 技术栈要求
- 使用 `react-native-paper@5.14.5` (MD3)
- 使用 `usePaperTheme` hook 替代 `useAppTheme`
- 优先使用 react-native-paper 组件替代原生组件
- 移除 StyleSheet，使用主题驱动的样式

### 2. 代码质量要求
- 保持文件行数在 200 行以内
- 保持组件功能完整性
- 确保类型安全
- 添加适当的测试

## 迁移流程

### 第一步：分析现有组件

1. **评估组件复杂度**
   - 简单组件：直接替换
   - 复杂组件：分步骤迁移

2. **识别可替换的元素**
   - `View` → react-native-paper 组件或保持原样
   - `Text` → `Text` with `variant` prop
   - `TextInput` → `TextInput` from react-native-paper
   - `TouchableOpacity` → `Button` 或 `IconButton`
   - `StyleSheet` → 主题属性和组件 props

### 第二步：更新导入

**之前：**
```typescript
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
```

**之后：**
```typescript
import { TextInput, Button, Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
```

### 第三步：组件重构

#### 3.1 TextInput 迁移示例

**之前：**
```typescript
<View style={styles.container}>
  <Search size={18} color={theme.colors.secondaryText} />
  <TextInput
    style={styles.input}
    placeholder="搜索..."
    value={value}
    onChangeText={onChangeText}
  />
  <TouchableOpacity onPress={onClear}>
    <X size={16} color={theme.colors.secondaryText} />
  </TouchableOpacity>
</View>
```

**之后：**
```typescript
<TextInput
  mode="outlined"
  placeholder="搜索..."
  value={value}
  onChangeText={onChangeText}
  left={<TextInput.Icon icon="magnify" />}
  right={
    showClearButton ? (
      <TextInput.Icon icon="close" onPress={onClear} />
    ) : undefined
  }
/>
```

#### 3.2 Button 迁移示例

**之前：**
```typescript
<TouchableOpacity style={styles.button} onPress={onPress}>
  <Text style={styles.buttonText}>按钮文本</Text>
</TouchableOpacity>
```

**之后：**
```typescript
<Button mode="contained" onPress={onPress}>
  按钮文本
</Button>
```

#### 3.3 Text 迁移示例

**之前：**
```typescript
<Text style={styles.title}>标题</Text>
<Text style={styles.body}>正文内容</Text>
```

**之后：**
```typescript
<Text variant="titleLarge">标题</Text>
<Text variant="bodyMedium">正文内容</Text>
```

### 第四步：主题集成

#### 4.1 使用主题 Hook

```typescript
const theme = usePaperTheme();
```

#### 4.2 组件级主题覆盖

```typescript
<Button
  mode="contained"
  theme={{ roundness: theme.roundness }}
>
  自定义圆角按钮
</Button>
```

### 第五步：清理工作

1. **删除不再需要的文件**
   - 删除 `styles.ts` 文件
   - 移除未使用的导入

2. **更新类型定义**
   - 确保 Props 类型正确
   - 移除样式相关的 Props

## 常见组件映射

| 原生组件 | React Native Paper 组件 | 说明 |
|---------|------------------------|-----|
| `TextInput` | `TextInput` | 支持 mode, left/right icons |
| `TouchableOpacity` + `Text` | `Button` | 多种 mode: contained, outlined, text |
| `TouchableOpacity` + Icon | `IconButton` | 支持 containerColor, iconColor |
| `Text` | `Text` | 使用 variant prop 定义样式 |
| `View` (卡片样式) | `Card` | 支持 mode: elevated, contained |
| `Modal` | `Portal` + `Dialog` | 更好的层级管理 |

## 主题变量映射

| 自定义主题 | React Native Paper MD3 |
|-----------|----------------------|
| `theme.colors.primary` | `theme.colors.primary` |
| `theme.colors.background` | `theme.colors.background` |
| `theme.colors.surface` | `theme.colors.surface` |
| `theme.colors.text` | `theme.colors.onSurface` |
| `theme.fonts.regular` | `theme.fonts.bodyMedium.fontFamily` |
| `theme.radius.md` | `theme.roundness` |

## 测试策略

### 1. 单元测试
- 测试组件渲染
- 测试用户交互
- 测试主题应用

### 2. 集成测试
- 测试主题切换
- 测试响应式布局
- 测试功能完整性

## 迁移检查清单

- [ ] 组件功能保持完整
- [ ] 移除所有 StyleSheet 使用
- [ ] 使用 react-native-paper 组件
- [ ] 使用 `usePaperTheme` hook
- [ ] 文件行数 ≤ 200 行
- [ ] 删除不需要的文件
- [ ] 添加/更新测试
- [ ] 验证主题切换
- [ ] 验证响应式表现

## 注意事项

1. **渐进式迁移**：一次迁移一个组件，避免大规模破坏性更改
2. **保持向后兼容**：确保迁移不影响现有功能
3. **性能考虑**：注意 react-native-paper 组件的性能特征
4. **主题一致性**：确保所有组件使用统一的主题系统

## 示例：完整的迁移案例

参考 `components/ui/SearchBar` 的迁移实现，这是一个完整的迁移示例，展示了：
- 从原生组件到 react-native-paper 组件的转换
- StyleSheet 的移除
- 主题系统的集成
- 功能的保持和增强
