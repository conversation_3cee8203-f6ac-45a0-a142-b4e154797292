import React from 'react';
import { View } from 'react-native';
import { Text, IconButton } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface StoryListItemHeaderProps {
  title: string;
  onOptionsPress?: () => void;
}

export function StoryListItemHeader({
  title,
  onOptionsPress,
}: StoryListItemHeaderProps) {
  const theme = usePaperTheme();

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Text
        variant="titleMedium"
        numberOfLines={1}
        style={{ flex: 1, marginRight: theme.spacing?.sm || 8 }}
      >
        {title}
      </Text>

      {onOptionsPress && (
        <IconButton icon="dots-vertical" size={20} onPress={onOptionsPress} />
      )}
    </View>
  );
}
