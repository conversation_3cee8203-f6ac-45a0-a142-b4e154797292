import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MoreVertical } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface StoryListItemHeaderProps {
  title: string;
  onOptionsPress?: () => void;
}

export function StoryListItemHeader({
  title,
  onOptionsPress,
}: StoryListItemHeaderProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
      <Text
        style={[styles.title, { color: appTheme.colors.text }]}
        numberOfLines={1}
      >
        {title}
      </Text>

      {onOptionsPress && (
        <TouchableOpacity onPress={onOptionsPress}>
          <MoreVertical size={20} color={appTheme.colors.secondaryText} />
        </TouchableOpacity>
      )}
    </View>
  );
}
