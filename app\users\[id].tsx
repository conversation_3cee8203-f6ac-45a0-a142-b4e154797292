import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import UserProfileScreen from '@/features/social/screens/UserProfileScreen';
import { useTranslation } from 'react-i18next';

export default function UserProfilePageRoute() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const theme = useAppTheme();
  const { t } = useTranslation();

  if (!id) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: theme.colors.background,
        }}
      >
        <Text style={{ color: theme.colors.text }}>
          {t('userProfile.errors.idNotFound', '用户ID未找到')}
        </Text>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: t('userProfile.title', '用户资料'),
          headerShown: true,
        }}
      />
      <UserProfileScreen userId={id} />
    </>
  );
}
