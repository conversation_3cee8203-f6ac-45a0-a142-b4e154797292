import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import SearchBar from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Mock i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue,
  }),
}));

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>
    {children}
  </PaperProvider>
);

describe('SearchBar', () => {
  it('renders correctly with default props', () => {
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <SearchBar />
      </TestWrapper>
    );

    expect(getByPlaceholderText('搜索故事、作者、标签...')).toBeTruthy();
  });

  it('renders with custom placeholder', () => {
    const customPlaceholder = 'Custom search placeholder';
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <SearchBar placeholder={customPlaceholder} />
      </TestWrapper>
    );

    expect(getByPlaceholderText(customPlaceholder)).toBeTruthy();
  });

  it('calls onChangeText when text changes', () => {
    const mockOnChangeText = jest.fn();
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <SearchBar onChangeText={mockOnChangeText} />
      </TestWrapper>
    );

    const input = getByPlaceholderText('搜索故事、作者、标签...');
    fireEvent.changeText(input, 'test search');

    expect(mockOnChangeText).toHaveBeenCalledWith('test search');
  });

  it('calls onSearch when submit is triggered', () => {
    const mockOnSearch = jest.fn();
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <SearchBar onSearch={mockOnSearch} />
      </TestWrapper>
    );

    const input = getByPlaceholderText('搜索故事、作者、标签...');
    fireEvent.changeText(input, 'test search');
    fireEvent(input, 'submitEditing');

    expect(mockOnSearch).toHaveBeenCalledWith('test search');
  });

  it('shows clear button when there is text', () => {
    const { getByPlaceholderText, getByTestId } = render(
      <TestWrapper>
        <SearchBar value="test" />
      </TestWrapper>
    );

    // The clear button should be visible when there's text
    // Note: This test might need adjustment based on how react-native-paper
    // renders the clear icon internally
    expect(getByPlaceholderText('搜索故事、作者、标签...')).toBeTruthy();
  });

  it('clears text when clear button is pressed', () => {
    const mockOnChangeText = jest.fn();
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <SearchBar value="test" onChangeText={mockOnChangeText} />
      </TestWrapper>
    );

    // This test would need to be adjusted based on how to access
    // the clear button in react-native-paper TextInput
    expect(getByPlaceholderText('搜索故事、作者、标签...')).toBeTruthy();
  });
});
