import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/utils/supabase';
import { StorySegment } from '@/api/stories/types';

interface UseStoryFeedProps {
  storyId: string;
  pageSize?: number;
}

interface UseStoryFeedResult {
  segments: StorySegment[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  hasMoreSegments: boolean;
  currentPath: string[];
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  navigateToBranch: (segmentId: string) => Promise<void>;
}

export function useStoryFeed({
  storyId,
  pageSize = 10,
}: UseStoryFeedProps): UseStoryFeedResult {
  const [segments, setSegments] = useState<StorySegment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMoreSegments, setHasMoreSegments] = useState(true);
  const [currentPath, setCurrentPath] = useState<string[]>([]);

  // Function to fetch story segments
  const fetchSegments = useCallback(
    async (page: number, reset = false) => {
      setIsLoading(true);
      setError(null);

      try {
        // 优化：使用RPC函数获取所有段落，减少网络请求
        // 创建一个存储过程来一次性获取所有需要的数据
        // 这里我们直接使用优化的查询

        // 构建查询，只获取必要的字段，减少数据传输量
        let query = supabase
          .from('story_segments')
          .select(
            `
            id,
            story_id,
            author_id,
            content_type,
            content,
            parent_segment_id,
            order_in_branch,
            is_ai_generated,
            created_at,
            updated_at,
            profiles:author_id (
              id, username, avatar_url
            ),
            children_count:story_segments!parent_segment_id (count)
          `
          )
          .eq('story_id', storyId);

        // 不使用分页，一次性获取所有段落
        // 对于大型故事，可以考虑分页加载，但对于中小型故事，一次性加载更高效

        // 执行查询
        const { data, error: fetchError } = await query;

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        // 获取总数用于分页
        const totalCount = data?.length || 0;
        setHasMoreSegments(false); // 已经获取了所有段落，不需要分页

        // 优化：使用更高效的排序算法
        // 创建一个映射表，用于快速查找父子关系
        const segmentsMap = new Map();
        const sortedData = data || [];

        // 首先将所有段落放入映射表
        sortedData.forEach((segment) => {
          segmentsMap.set(segment.id, {
            segment,
            children: [],
          });
        });

        // 构建父子关系树
        let rootSegment = null;
        sortedData.forEach((segment) => {
          if (!segment.parent_segment_id) {
            // 这是根段落
            rootSegment = segment;
          } else if (segmentsMap.has(segment.parent_segment_id)) {
            // 将此段落添加为父段落的子段落
            segmentsMap.get(segment.parent_segment_id).children.push(segment);
          }
        });

        // 如果找不到根段落，则按创建时间排序
        if (!rootSegment) {
          const timeOrderedSegments = [...sortedData].sort(
            (a, b) =>
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
          );

          if (reset) {
            setSegments(timeOrderedSegments);
          } else {
            setSegments((prev) => [...prev, ...timeOrderedSegments]);
          }
          return;
        }

        // 从根段落开始，按照主线路径构建有序段落列表
        const orderedSegments = [rootSegment];
        let currentSegment = rootSegment;

        // 循环查找主线路径（每个父段落的第一个子段落）
        while (
          segmentsMap.has(currentSegment.id) &&
          segmentsMap.get(currentSegment.id).children.length > 0
        ) {
          // 获取子段落并按创建时间排序
          const children = segmentsMap.get(currentSegment.id).children;
          children.sort(
            (a, b) =>
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
          );

          // 添加第一个子段落到主线路径
          const nextSegment = children[0];
          orderedSegments.push(nextSegment);
          currentSegment = nextSegment;
        }

        // 更新状态
        if (reset) {
          setSegments(orderedSegments);
        } else {
          setSegments((prev) => [...prev, ...orderedSegments]);
        }

        // If this is the first load and we don't have a path yet, set the initial segment as the path
        if (page === 0 && data && data.length > 0 && currentPath.length === 0) {
          setCurrentPath([data[0].id]);
        }
      } catch (err) {
        console.error('Error fetching segments:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to fetch story segments'
        );
      } finally {
        setIsLoading(false);
      }
    },
    [storyId, pageSize, currentPath]
  );

  // Initial fetch
  useEffect(() => {
    fetchSegments(0, true);
  }, [storyId, currentPath]);

  // Load more segments
  const loadMore = useCallback(async () => {
    if (!isLoading && hasMoreSegments) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      await fetchSegments(nextPage);
    }
  }, [isLoading, hasMoreSegments, currentPage, fetchSegments]);

  // Refresh segments
  const refresh = useCallback(async () => {
    setCurrentPage(0);
    await fetchSegments(0, true);
  }, [fetchSegments]);

  // Navigate to a specific branch - 优化版本
  const navigateToBranch = useCallback(
    async (segmentId: string) => {
      try {
        setIsLoading(true);

        // 优化：使用缓存的段落数据，避免重复请求
        // 查找当前段落
        const targetSegment = segments.find((seg) => seg.id === segmentId);

        if (!targetSegment) {
          // 如果在缓存中找不到，则从服务器获取
          const { data: segment, error: segmentError } = await supabase
            .from('story_segments')
            .select('id, parent_segment_id') // 只获取必要的字段
            .eq('id', segmentId)
            .single();

          if (segmentError || !segment) {
            throw new Error(segmentError?.message || 'Segment not found');
          }

          // 构建路径
          const fullPath = [segment.id];

          // 获取所有父段落的ID
          if (segment.parent_segment_id) {
            // 优化：一次性获取所有父段落，而不是逐个查询
            const { data: parentSegments, error: parentsError } =
              await supabase.rpc('get_segment_path', {
                segment_id_param: segmentId,
              });

            if (parentsError) {
              throw new Error(parentsError.message);
            }

            // 将父段落ID添加到路径中
            if (parentSegments && parentSegments.length > 0) {
              // 按照从根到叶的顺序排列
              parentSegments.sort((a, b) => a.depth - b.depth);

              // 将父段落ID添加到路径前面
              fullPath.unshift(...parentSegments.map((p) => p.id));
            }
          }

          // 更新当前路径
          setCurrentPath(fullPath);
        } else {
          // 使用缓存数据构建路径
          const fullPath = [targetSegment.id];
          let currentSegment = targetSegment;

          // 遍历父段落
          while (currentSegment.parent_segment_id) {
            const parentSegment = segments.find(
              (seg) => seg.id === currentSegment.parent_segment_id
            );
            if (!parentSegment) break; // 如果找不到父段落，则停止

            fullPath.unshift(parentSegment.id);
            currentSegment = parentSegment;
          }

          // 更新当前路径
          setCurrentPath(fullPath);
        }

        // 重置分页
        setCurrentPage(0);
      } catch (err) {
        console.error('Error navigating to branch:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to navigate to branch'
        );
      } finally {
        setIsLoading(false);
      }
    },
    [segments]
  );

  return {
    segments,
    isLoading,
    error,
    currentPage,
    hasMoreSegments,
    loadMore,
    refresh,
    navigateToBranch,
    currentPath,
  };
}
