import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { MessageSquare, Plus } from 'lucide-react-native';

interface EmptyConversationsStateProps {
  onNewConversation?: () => void;
}

export default function EmptyConversationsState({
  onNewConversation,
}: EmptyConversationsStateProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <MessageSquare size={48} color={theme.colors.secondaryText} />
      <Text style={styles.message}>
        {t('messages.emptyState', '暂无消息')}
      </Text>
      <Text style={styles.subMessage}>
        {t(
          'messages.emptyStateSubMessage',
          '与其他用户开始对话，分享你的创作灵感'
        )}
      </Text>

      {onNewConversation && (
        <TouchableOpacity
          style={styles.button}
          onPress={onNewConversation}
          activeOpacity={0.7}
        >
          <Plus size={16} color={theme.colors.background} />
          <Text style={styles.buttonText}>
            {t('messages.newConversation', '新建对话')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}