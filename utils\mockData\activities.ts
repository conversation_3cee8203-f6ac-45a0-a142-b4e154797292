import { mockUsers } from './users';

// Define ActivityItem type
export interface ActivityItem {
  id: string;
  type: 'comment' | 'branch' | 'like';
  user: (typeof mockUsers)[0];
  storyTitle: string;
  content: string;
  time: string;
}

// Sample data for generating random activities
const STORY_TITLES = [
  '星际迷航：量子裂隙',
  '迷雾森林的秘密',
  '记忆收藏家',
  '机械之心',
  '幻境花园',
  '黎明破晓',
  '暗夜守护者',
];

const TIME_FRAMES = [
  '刚刚',
  '5分钟前',
  '20分钟前',
  '1小时前',
  '3小时前',
  '昨天',
  '前天',
];

const COMMENTS = [
  '这个故事真是精彩，期待下一章！',
  '情节发展很有趣！',
  '我喜欢主角的性格塑造。',
  '写得太棒了，很有创意！',
  '这个转折出乎意料！',
];

/**
 * Generates random activity items for the social feed
 * @param count Number of activity items to generate
 * @returns Array of random activity items
 */
export function generateRandomActivities(count: number): ActivityItem[] {
  const types = ['comment', 'branch', 'like'] as const;

  return Array(count)
    .fill(0)
    .map((_, index) => {
      const type = types[Math.floor(Math.random() * types.length)];
      const user = mockUsers[Math.floor(Math.random() * mockUsers.length)];
      const title = STORY_TITLES[Math.floor(Math.random() * STORY_TITLES.length)];
      const time = TIME_FRAMES[Math.floor(Math.random() * TIME_FRAMES.length)];

      let content = '';
      if (type === 'comment') {
        content = COMMENTS[Math.floor(Math.random() * COMMENTS.length)];
      } else if (type === 'branch') {
        content = '创作了新的分支剧情';
      } else {
        content = '喜欢了你的故事';
      }

      return {
        id: `generated-${Date.now()}-${index}-${Math.random()
          .toString(36)
          .substring(2, 9)}`,
        type,
        user,
        storyTitle: title,
        content,
        time,
      };
    });
}
