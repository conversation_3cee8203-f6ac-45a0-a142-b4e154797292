import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    contentContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      flexGrow: 1,
    },
    nodeContainer: {
      position: 'relative', // 为了定位创作按钮
    },
    sameLevelCreationButton: {
      position: 'absolute',
      right: -16, // 按钮中心定在边框位置
      top: '50%', // 垂直居中
      marginTop: -16, // 调整垂直居中位置
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 3,
      shadowColor: '#000000', // 使用固定颜色替代 theme.colors.shadow
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    childCreationButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 5, // 增加阴影效果，使按钮更加突出
      shadowColor: '#000000', // 使用固定颜色替代 theme.colors.shadow
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 3,
      zIndex: 10, // 确保按钮在其他元素之上
      marginTop: -16, // 向上偏移半个按钮高度，使按钮中心点位于边线上
    },
    footerContainer: {
      padding: theme.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    },
    footerText: {
      fontSize: 14,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginLeft: theme.spacing.sm,
    },
    emptyContainer: {
      padding: theme.spacing.xl * 2,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyText: {
      fontSize: 16,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
  });
