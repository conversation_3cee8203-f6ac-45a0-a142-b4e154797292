import React from 'react';
import { View } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface HomeScreenErrorProps {
  error: string;
  onRetry: () => void;
}

export function HomeScreenError({ error, onRetry }: HomeScreenErrorProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: theme.spacing?.lg || 20,
        padding: theme.spacing?.lg || 16,
      }}
    >
      <Text
        variant="bodyMedium"
        style={{
          color: theme.colors.error,
          marginBottom: theme.spacing?.md || 16,
          textAlign: 'center',
        }}
      >
        {error}
      </Text>
      <Button mode="contained" onPress={onRetry}>
        {t('tryAgain', '重试')}
      </Button>
    </View>
  );
}
