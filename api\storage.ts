import { supabase } from '@/utils/supabase';
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { Platform } from 'react-native';

/**
 * Uploads a file to Supabase Storage
 * 
 * @param filePath The local file path (URI) to upload
 * @param bucket The storage bucket name
 * @param path The path within the bucket where the file should be stored
 * @param fileOptions Additional file options like contentType
 * @returns An object containing the file data or error
 */
export async function uploadFile(
  filePath: string,
  bucket: string,
  path: string,
  fileOptions?: {
    contentType?: string;
    upsert?: boolean;
  }
): Promise<{
  data: { path: string; fullPath: string } | null;
  error: Error | null;
}> {
  try {
    // For React Native, we need to convert the file to a base64 string first
    // and then to an ArrayBuffer
    const fileInfo = await FileSystem.getInfoAsync(filePath);
    
    if (!fileInfo.exists) {
      return {
        data: null,
        error: new Error('File does not exist'),
      };
    }

    // Get file extension from path
    const fileExt = filePath.split('.').pop()?.toLowerCase() || '';
    
    // Determine content type based on file extension if not provided
    const contentType = fileOptions?.contentType || 
      (fileExt === 'jpg' || fileExt === 'jpeg' ? 'image/jpeg' : 
       fileExt === 'png' ? 'image/png' : 
       fileExt === 'gif' ? 'image/gif' : 
       fileExt === 'webp' ? 'image/webp' : 
       'application/octet-stream');

    // Read the file as base64
    const base64 = await FileSystem.readAsStringAsync(filePath, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Convert base64 to ArrayBuffer
    const arrayBuffer = decode(base64);

    // Generate a unique filename
    const fileName = `${Date.now()}.${fileExt}`;
    const fullPath = `${path}/${fileName}`;

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(fullPath, arrayBuffer, {
        contentType,
        upsert: fileOptions?.upsert || false,
      });

    if (error) {
      console.error('Error uploading file:', error);
      return { data: null, error };
    }

    // Return the path to the uploaded file
    return {
      data: {
        path: fileName,
        fullPath: data.path,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in uploadFile:', error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error('Unknown error occurred'),
    };
  }
}

/**
 * Gets a public URL for a file in Supabase Storage
 * 
 * @param bucket The storage bucket name
 * @param path The path to the file within the bucket
 * @returns The public URL for the file
 */
export function getPublicUrl(bucket: string, path: string): string {
  const { data } = supabase.storage.from(bucket).getPublicUrl(path);
  return data.publicUrl;
}

/**
 * Deletes a file from Supabase Storage
 * 
 * @param bucket The storage bucket name
 * @param path The path to the file within the bucket
 * @returns An object indicating success or error
 */
export async function deleteFile(
  bucket: string,
  path: string
): Promise<{ error: Error | null }> {
  try {
    const { error } = await supabase.storage.from(bucket).remove([path]);
    return { error };
  } catch (error) {
    console.error('Error in deleteFile:', error);
    return {
      error: error instanceof Error ? error : new Error('Unknown error occurred'),
    };
  }
}
