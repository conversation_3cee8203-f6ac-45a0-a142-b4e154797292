import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './AuthorRankingItem.styles';
import { Profile } from '@/api/profiles';
import { useTranslation } from 'react-i18next';
import { Avatar } from '@/components/ui/Avatar';
import { BookOpen, Users } from 'lucide-react-native';

interface AuthorRankingItemProps {
  author: Profile;
  rank: number;
  onPress: (authorId: string) => void;
}

export function AuthorRankingItem({
  author,
  rank,
  onPress,
}: AuthorRankingItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const handlePress = () => {
    onPress(author.id);
  };

  // Get rank color
  const getRankColor = () => {
    switch (rank) {
      case 1:
        return theme.colors.gold;
      case 2:
        return theme.colors.silver;
      case 3:
        return theme.colors.bronze;
      default:
        return theme.colors.secondaryText;
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.rankContainer}>
        <Text style={[styles.rankText, { color: getRankColor() }]}>
          {rank}
        </Text>
      </View>

      <View style={styles.avatarContainer}>
        <Avatar
          size={60}
          uri={author.avatar_url || ''}
          username={author.username || ''}
        />
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.username} numberOfLines={1}>
          {author.username || t('rankings.unknownAuthor', '未知作者')}
        </Text>

        <Text style={styles.fullName} numberOfLines={1}>
          {author.full_name || ''}
        </Text>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <BookOpen size={14} color={theme.colors.secondaryText} />
            <Text style={styles.statText}>
              {author.stories_count || 0} {t('rankings.stories', '故事')}
            </Text>
          </View>

          <View style={styles.statItem}>
            <Users size={14} color={theme.colors.secondaryText} />
            <Text style={styles.statText}>
              {author.followers_count || 0} {t('rankings.followers', '粉丝')}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}