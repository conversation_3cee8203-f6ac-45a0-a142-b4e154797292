import React from 'react';
import { Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from '../FeedTab.styles';

export function ListHeader() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <Text style={styles.sectionTitle}>
      {t('social.feed.activityFeedTitle', '最新动态')}
    </Text>
  );
}
