import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => StyleSheet.create({
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md, // Add horizontal padding for alignment
  },
  avatarContainer: {
    marginBottom: theme.spacing.md,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  name: {
    fontSize: 22,
    fontFamily: theme.fonts.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  bio: {
    fontSize: 14,
    fontFamily: theme.fonts.regular,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  // Add styles for the premium badge
  premiumBadge: {
    position: 'absolute',
    top: theme.spacing.md, // Adjust position relative to avatar
    right: theme.spacing.md, 
    backgroundColor: '#FFD700', // Gold color
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: 10, 
    elevation: 3, // Add some shadow
  },
  premiumText: {
    fontFamily: theme.fonts.bold,
    fontSize: 10, 
    color: '#000', // Black text on gold
    marginLeft: 2,
  },
}); 