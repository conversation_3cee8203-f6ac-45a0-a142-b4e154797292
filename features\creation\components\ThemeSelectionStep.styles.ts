import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles specific to the Theme Selection step
export const createStyles = (theme: AppTheme) => StyleSheet.create({
  container: {
    flex: 1, // Take up available space
    paddingHorizontal: theme.spacing.md,
  },
  title: {
    fontFamily: theme.fonts.bold,
    fontSize: 24, // Consistent title size
    color: theme.colors.text,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
  description: {
    fontFamily: theme.fonts.regular,
    fontSize: 14,
    color: theme.colors.secondaryText,
    marginBottom: theme.spacing.lg,
    lineHeight: 20,
  },
  scrollView: {
    flex: 1, // Allow scroll view to take remaining space
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: theme.spacing.lg, // Padding at the bottom of the grid
    // Add gap using margin on cards or justify content with space-around if preferred
  },
}); 