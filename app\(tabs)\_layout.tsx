import { Tabs, useRouter } from 'expo-router';
import {
  Book,
  PenLine,
  Compass,
  Users,
  User,
  Settings,
} from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';

export default function TabLayout() {
  const theme = useAppTheme();
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.tabIconDefault,
        tabBarStyle: {
          backgroundColor: theme.colors.background,
          borderTopColor: theme.colors.border,
          elevation: 0,
          borderTopWidth: 1,
          height: 60,
          paddingBottom: theme.spacing.xs,
          paddingTop: theme.spacing.xs,
        },
        tabBarLabelStyle: {
          fontSize: 10,
          fontFamily: theme.fonts.medium,
        },
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t('tabs.home'),
          tabBarIcon: ({ color }) => <Compass color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          title: t('tabs.create'),
          tabBarIcon: ({ color }) => <PenLine color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="stories"
        options={{
          title: t('tabs.stories'),
          tabBarIcon: ({ color }) => <Book color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="social"
        options={{
          title: t('tabs.social'),
          tabBarIcon: ({ color }) => <Users color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t('tabs.profile'),
          headerShown: true,
          headerStyle: {
            backgroundColor: theme.colors.background,
            boxShadow: 'none',
            borderBottomWidth: 1,
            borderBottomColor: theme.colors.border,
          },
          headerTitleStyle: {
            color: theme.colors.text,
            fontFamily: theme.fonts.bold,
          },
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.push('/(settings)')}
              style={{ marginRight: theme.spacing.md }}
            >
              <Settings size={24} color={theme.colors.text} />
            </TouchableOpacity>
          ),
          tabBarIcon: ({ color }) => <User color={color} size={24} />,
        }}
      />
    </Tabs>
  );
}
