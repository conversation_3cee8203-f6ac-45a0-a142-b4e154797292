import React from 'react';
import { View } from 'react-native';
import { Text, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface FeaturedStoryCardStatsProps {
  authorName: string;
  views: number;
  likes: number;
}

export function FeaturedStoryCardStats({
  authorName,
  views,
  likes,
}: FeaturedStoryCardStatsProps) {
  const theme = usePaperTheme();

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing?.sm || 8,
      }}
    >
      <Text variant="bodyMedium" style={{ color: '#FFFFFF' }}>
        @{authorName}
      </Text>

      <View
        style={{
          flexDirection: 'row',
          gap: theme.spacing?.md || 12,
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: theme.spacing?.xs || 4,
          }}
        >
          <Icon source="eye" size={12} color="#FFFFFF" />
          <Text variant="bodySmall" style={{ color: '#FFFFFF' }}>
            {views}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: theme.spacing?.xs || 4,
          }}
        >
          <Icon source="heart" size={12} color="#FFFFFF" />
          <Text variant="bodySmall" style={{ color: '#FFFFFF' }}>
            {likes}
          </Text>
        </View>
      </View>
    </View>
  );
}
