import React from 'react';
import { View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { BookOpen } from 'lucide-react-native';
import { Story } from '@/api/stories';
import StoryPreviewCard from '@/components/stories/StoryPreviewCard';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface UserStoriesProps {
  stories: Story[];
  userName: string;
  onStoryPress: (storyId: string) => void;
}

export default function UserStories({ stories, userName, onStoryPress }: UserStoriesProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.storiesContainer}>
      <Text style={styles.sectionTitle}>
        {t('social.userProfile.userStories', '{{name}}的故事', {
          name: userName,
        })}
      </Text>

      {stories.length > 0 ? (
        stories.map((story) => (
          <StoryPreviewCard
            key={story.id}
            story={story}
            onPress={() => onStoryPress(story.id)}
          />
        ))
      ) : (
        <View style={styles.emptyStoriesContainer}>
          <BookOpen size={24} color={theme.colors.secondaryText} />
          <Text style={styles.emptyStoriesText}>
            {t('social.userProfile.noStories', '这个用户还没有发布故事')}
          </Text>
        </View>
      )}
    </View>
  );
}
