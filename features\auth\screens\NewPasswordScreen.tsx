import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './NewPasswordScreen.styles';
import { updatePassword } from '@/api/auth';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/lib/store/authStore';

/**
 * NewPasswordScreen component
 * 
 * This screen allows users to set a new password after receiving a password reset link.
 * It is accessed via a deep link from the reset password email.
 */
export default function NewPasswordScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const { signIn } = useAuthStore();

  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  /**
   * Handles the password update
   */
  const handleUpdatePassword = async () => {
    // Validate passwords
    if (!newPassword || !confirmPassword) {
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.fieldRequired')
      );
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.passwordTooShort')
      );
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.passwordsDoNotMatch')
      );
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await updatePassword(newPassword);
      setLoading(false);

      if (error) {
        console.error('Update password error:', error);
        Alert.alert(
          t('auth.errorTitle'),
          error.message || t('auth.updatePasswordFailed')
        );
      } else {
        Alert.alert(
          t('auth.successTitle'),
          t('auth.updatePasswordSuccess')
        );
        
        // If we have session data, sign in the user
        if (data?.session && data?.user) {
          signIn(data.session, data.user);
        } else {
          // Otherwise, redirect to login
          router.replace('/(auth)/login');
        }
      }
    } catch (error) {
      setLoading(false);
      console.error('Update password error:', error);
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.networkError')
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('auth.newPasswordTitle')}</Text>
      
      <Text style={styles.instructions}>
        {t('auth.newPasswordInstructions')}
      </Text>

      <TextInput
        style={styles.input}
        placeholder={t('auth.newPasswordLabel')}
        placeholderTextColor={theme.colors.placeholder}
        secureTextEntry
        value={newPassword}
        onChangeText={setNewPassword}
      />

      <TextInput
        style={styles.input}
        placeholder={t('auth.confirmPasswordLabel')}
        placeholderTextColor={theme.colors.placeholder}
        secureTextEntry
        value={confirmPassword}
        onChangeText={setConfirmPassword}
      />

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleUpdatePassword}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color={theme.colors.onPrimary} />
        ) : (
          <Text style={styles.buttonText}>
            {t('auth.submitButton')}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
}
