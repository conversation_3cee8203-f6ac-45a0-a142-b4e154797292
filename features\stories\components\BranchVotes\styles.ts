import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden',
      marginVertical: theme.spacing.md,
    },
    header: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    title: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
    },
    content: {
      padding: theme.spacing.md,
    },
    voteButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: theme.spacing.md,
    },
    voteButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.radius.md,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginHorizontal: theme.spacing.sm,
    },
    activeUpvoteButton: {
      backgroundColor: theme.colors.success,
      borderColor: theme.colors.success,
    },
    activeDownvoteButton: {
      backgroundColor: theme.colors.error,
      borderColor: theme.colors.error,
    },
    voteButtonText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginLeft: theme.spacing.xs,
    },
    activeVoteButtonText: {
      color: theme.colors.onPrimary,
    },
    disabledButton: {
      opacity: 0.5,
    },
    voteStatsContainer: {
      marginTop: theme.spacing.sm,
    },
    voteBarContainer: {
      height: 8,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.full,
      overflow: 'hidden',
      flexDirection: 'row',
    },
    upvoteBar: {
      height: '100%',
      backgroundColor: theme.colors.success,
    },
    downvoteBar: {
      height: '100%',
      backgroundColor: theme.colors.error,
    },
    voteTotalText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      textAlign: 'center',
      marginTop: theme.spacing.sm,
    },
    loadingContainer: {
      padding: theme.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      marginTop: theme.spacing.sm,
    },
    loginPromptText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      textAlign: 'center',
      marginTop: theme.spacing.md,
    },
  });
};
