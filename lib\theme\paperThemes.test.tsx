import React from 'react';
import { render } from '@testing-library/react-native';
import { PaperProvider, Button, Text } from 'react-native-paper';
import { paperLightTheme, paperDarkTheme } from './paperThemes';

// 测试组件
const TestComponent = () => (
  <>
    <Text variant="headlineSmall">测试标题</Text>
    <Button mode="contained">测试按钮</Button>
  </>
);

describe('Paper 主题系统', () => {
  it('亮色主题应该正确渲染', () => {
    const { getByText } = render(
      <PaperProvider theme={paperLightTheme}>
        <TestComponent />
      </PaperProvider>
    );

    expect(getByText('测试标题')).toBeTruthy();
    expect(getByText('测试按钮')).toBeTruthy();
  });

  it('暗色主题应该正确渲染', () => {
    const { getByText } = render(
      <PaperProvider theme={paperDarkTheme}>
        <TestComponent />
      </PaperProvider>
    );

    expect(getByText('测试标题')).toBeTruthy();
    expect(getByText('测试按钮')).toBeTruthy();
  });

  it('主题应该包含自定义颜色', () => {
    expect(paperLightTheme.colors.primary).toBe('rgb(103, 80, 164)');
    expect(paperDarkTheme.colors.primary).toBe('rgb(208, 188, 255)');
    
    // 测试 AI 相关颜色
    expect(paperLightTheme.colors.aiAction).toBe('#1EB999');
    expect(paperDarkTheme.colors.aiAction).toBe('#1EB999');
  });

  it('主题应该包含间距配置', () => {
    expect(paperLightTheme.spacing?.md).toBe(16);
    expect(paperDarkTheme.spacing?.lg).toBe(24);
  });

  it('主题应该包含圆角配置', () => {
    expect(paperLightTheme.roundness).toBe(12);
    expect(paperDarkTheme.roundness).toBe(12);
  });
});
