-- Create a function to check if a column exists in a table
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION column_exists(
  table_name text,
  column_name text
) RETURNS boolean AS $$
DECLARE
  column_exists boolean;
BEGIN
  -- Check if the column exists in the table
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = column_exists.table_name
      AND column_name = column_exists.column_name
  ) INTO column_exists;
  
  RETURN column_exists;
END;
$$ LANGUAGE plpgsql;