import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes'; // Assuming AppTheme is the type of your theme

export const createStoryDetailHeaderStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    headerSection: {
      marginBottom: theme.spacing.lg,
      alignItems: 'center', // Center align items like title, author for a cleaner look
    },
    coverImage: {
      width: '100%',
      height: 250, // Slightly larger cover image
      borderRadius: theme.radius.lg, // More pronounced radius
      marginBottom: theme.spacing.md,
      backgroundColor: theme.colors.surface, // Placeholder bg color
    },
    title: {
      fontSize: theme.fontSizes.xxl, // Larger title
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.sm,
    },
    author: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      marginBottom: theme.spacing.md,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center', // Center tags
      marginBottom: theme.spacing.md,
    },
    tag: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.radius.md,
      margin: theme.spacing.xs, // Use margin for better spacing around tags
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    tagText: {
      color: theme.colors.primary,
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
    },
    likeButtonContainer: {
      marginTop: theme.spacing.md,
      alignItems: 'center',
    },
    likeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg, // More padding for a larger touch target
      borderRadius: theme.radius.xl,
      backgroundColor: theme.colors.surface, // Button with a background
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: theme.colors.text, // Adding subtle shadow for depth
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 3,
    },
    likedButton: {
      backgroundColor: theme.colors.primary, // Liked button has primary background
      borderColor: theme.colors.primary,
    },
    likeText: {
      marginLeft: theme.spacing.sm,
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text, // Default text color
    },
    likedText: {
      color: theme.colors.onPrimary, // Text color for liked state (on primary background)
    },
    likeIcon: {
      // Styles for the icon if needed, color is handled dynamically
    },
    activityIndicator: {
      marginLeft: theme.spacing.sm,
    },
  });
};
