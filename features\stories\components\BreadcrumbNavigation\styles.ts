import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      paddingVertical: theme.spacing.sm,
    },
    scrollContent: {
      paddingHorizontal: theme.spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
    },
    breadcrumbItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm,
      borderRadius: theme.roundness,
      marginHorizontal: theme.spacing.xs,
    },
    activeBreadcrumbItem: {
      backgroundColor: theme.colors.primary + '20', // 20% opacity
    },
    breadcrumbText: {
      fontSize: 14,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginLeft: theme.spacing.xs,
    },
    activeBreadcrumbText: {
      color: theme.colors.primary,
    },
  });
