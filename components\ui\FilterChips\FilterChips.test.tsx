import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { PaperProvider } from 'react-native-paper';
import FilterChips, { FilterOption } from './index';
import { paperLightTheme } from '@/lib/theme/paperThemes';

// Wrapper component to provide theme context
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <PaperProvider theme={paperLightTheme}>
    {children}
  </PaperProvider>
);

const mockOptions: FilterOption[] = [
  { id: '1', label: 'Option 1' },
  { id: '2', label: 'Option 2' },
  { id: '3', label: 'Option 3' },
];

describe('FilterChips', () => {
  it('renders all filter options', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <FilterChips
          options={mockOptions}
          selectedIds={[]}
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    expect(getByText('Option 1')).toBeTruthy();
    expect(getByText('Option 2')).toBeTruthy();
    expect(getByText('Option 3')).toBeTruthy();
  });

  it('calls onSelect when a chip is pressed', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <FilterChips
          options={mockOptions}
          selectedIds={[]}
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    fireEvent.press(getByText('Option 1'));
    expect(mockOnSelect).toHaveBeenCalledWith('1');
  });

  it('shows selected state for selected chips', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <FilterChips
          options={mockOptions}
          selectedIds={['1', '3']}
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // Note: Testing selected state might require checking specific props
    // or styles that react-native-paper Chip applies when selected
    expect(getByText('Option 1')).toBeTruthy();
    expect(getByText('Option 3')).toBeTruthy();
  });

  it('handles empty options array', () => {
    const mockOnSelect = jest.fn();
    const { queryByText } = render(
      <TestWrapper>
        <FilterChips
          options={[]}
          selectedIds={[]}
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    expect(queryByText('Option 1')).toBeNull();
  });

  it('handles multiSelect prop (functionality test)', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <FilterChips
          options={mockOptions}
          selectedIds={['1']}
          onSelect={mockOnSelect}
          multiSelect={true}
        />
      </TestWrapper>
    );

    // The multiSelect prop doesn't change rendering but affects usage
    fireEvent.press(getByText('Option 2'));
    expect(mockOnSelect).toHaveBeenCalledWith('2');
  });

  it('handles single select mode', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <FilterChips
          options={mockOptions}
          selectedIds={['1']}
          onSelect={mockOnSelect}
          multiSelect={false}
        />
      </TestWrapper>
    );

    fireEvent.press(getByText('Option 2'));
    expect(mockOnSelect).toHaveBeenCalledWith('2');
  });

  it('renders with no selected items', () => {
    const mockOnSelect = jest.fn();
    const { getByText } = render(
      <TestWrapper>
        <FilterChips
          options={mockOptions}
          selectedIds={[]}
          onSelect={mockOnSelect}
        />
      </TestWrapper>
    );

    // All options should be rendered but none selected
    expect(getByText('Option 1')).toBeTruthy();
    expect(getByText('Option 2')).toBeTruthy();
    expect(getByText('Option 3')).toBeTruthy();
  });
});
