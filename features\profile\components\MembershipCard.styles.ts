import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    card: {
      marginHorizontal: theme.spacing.md, // Align with screen padding
      borderRadius: theme.radius.md, // Assuming theme.roundness * 2 maps to a medium radius
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary, // Use primary color for emphasis
      marginBottom: theme.spacing.lg,
      // elevation: 4,
      // shadowColor: '#000',
      // shadowOffset: { width: 0, height: 2 },
      // shadowOpacity: 0.1,
      // shadowRadius: 4,
      boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    iconContainer: {
      marginRight: theme.spacing.md,
      // Add background or specific styling for icon if needed
    },
    textContainer: {
      flex: 1,
    },
    title: {
      fontFamily: theme.fonts.bold,
      fontSize: 16,
      color: theme.dark ? '#000' : '#FFF', // Ensure contrast
      marginBottom: theme.spacing.xs,
    },
    description: {
      fontFamily: theme.fonts.regular,
      fontSize: 14,
      color: theme.dark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.9)',
      lineHeight: 20,
    },
    cta: {
      fontFamily: theme.fonts.bold,
      fontSize: 14,
      color: theme.dark ? '#000' : '#FFF',
      alignSelf: 'flex-end',
      marginTop: theme.spacing.sm,
    },
  });
