import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { Story } from '../stories';
import { Profile } from '../profiles';

export type RankingPeriod = 'day' | 'week' | 'month' | 'all';

export interface RankingOptions {
  limit?: number;
  offset?: number;
}

/**
 * 获取热门故事排行榜
 * @param period 时间段
 * @param options 选项
 * @returns 故事列表
 */
export async function getTopStories(
  period: RankingPeriod = 'all',
  options: RankingOptions = {}
): Promise<{ data: Story[] | null; error: PostgrestError | null }> {
  const { limit = 10, offset = 0 } = options;
  
  // 计算时间范围
  let fromDate: Date | null = null;
  const now = new Date();
  
  if (period === 'day') {
    fromDate = new Date(now);
    fromDate.setDate(fromDate.getDate() - 1);
  } else if (period === 'week') {
    fromDate = new Date(now);
    fromDate.setDate(fromDate.getDate() - 7);
  } else if (period === 'month') {
    fromDate = new Date(now);
    fromDate.setMonth(fromDate.getMonth() - 1);
  }
  
  let query = supabase
    .from('stories')
    .select(`
      id, title, author_id, cover_image_url, created_at, updated_at, status, visibility, tags,
      profiles!stories_author_id_fkey ( id, username, avatar_url )
    `)
    .eq('status', 'published')
    .eq('visibility', 'public');
  
  // 应用时间筛选
  if (fromDate) {
    query = query.gte('created_at', fromDate.toISOString());
  }
  
  // 如果数据库中有 likes_count 字段，优先使用它
  if (await hasColumn('stories', 'likes_count')) {
    query = query.order('likes_count', { ascending: false });
  } else {
    // 否则按更新时间排序
    query = query.order('updated_at', { ascending: false });
  }
  
  // 应用分页
  query = query.range(offset, offset + limit - 1);
  
  const { data, error } = await query;
  
  if (error) {
    console.error('Error getting top stories:', error);
    return { data: null, error };
  }
  
  return { data: data as Story[], error: null };
}

/**
 * 获取热门作者排行榜
 * @param period 时间段
 * @param options 选项
 * @returns 作者列表
 */
export async function getTopAuthors(
  period: RankingPeriod = 'all',
  options: RankingOptions = {}
): Promise<{ data: Profile[] | null; error: PostgrestError | null }> {
  const { limit = 10, offset = 0 } = options;
  
  // 计算时间范围
  let fromDate: Date | null = null;
  const now = new Date();
  
  if (period === 'day') {
    fromDate = new Date(now);
    fromDate.setDate(fromDate.getDate() - 1);
  } else if (period === 'week') {
    fromDate = new Date(now);
    fromDate.setDate(fromDate.getDate() - 7);
  } else if (period === 'month') {
    fromDate = new Date(now);
    fromDate.setMonth(fromDate.getMonth() - 1);
  }
  
  // 获取作者及其故事的点赞总数
  let query = supabase.rpc('get_top_authors', {
    from_date: fromDate?.toISOString() || '1970-01-01',
    limit_param: limit,
    offset_param: offset,
  });
  
  const { data, error } = await query;
  
  if (error) {
    // 如果 RPC 不存在，使用备用方法
    console.error('Error getting top authors using RPC:', error);
    
    // 备用方法：获取所有作者，按 followers_count 排序
    const { data: backupData, error: backupError } = await supabase
      .from('users')
      .select(`
        id, username, display_name, avatar_url, bio, is_premium, followers_count, following_count, stories_count
      `)
      .order('followers_count', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (backupError) {
      console.error('Error getting top authors using backup method:', backupError);
      return { data: null, error: backupError };
    }
    
    // 转换为 Profile 类型
    const profiles = backupData.map(user => ({
      id: user.id,
      username: user.username,
      full_name: user.display_name,
      avatar_url: user.avatar_url,
      bio: user.bio,
      is_premium: user.is_premium,
      followers_count: user.followers_count,
      following_count: user.following_count,
      stories_count: user.stories_count,
    }));
    
    return { data: profiles, error: null };
  }
  
  // 获取作者详细信息
  const authorIds = data.map(item => item.author_id);
  
  const { data: authorsData, error: authorsError } = await supabase
    .from('users')
    .select(`
      id, username, display_name, avatar_url, bio, is_premium, followers_count, following_count, stories_count
    `)
    .in('id', authorIds);
  
  if (authorsError) {
    console.error('Error getting authors details:', authorsError);
    return { data: null, error: authorsError };
  }
  
  // 转换为 Profile 类型并按原始排序顺序排序
  const authorMap = new Map(authorsData.map(author => [author.id, author]));
  const profiles = data
    .map(item => {
      const author = authorMap.get(item.author_id);
      if (!author) return null;
      
      return {
        id: author.id,
        username: author.username,
        full_name: author.display_name,
        avatar_url: author.avatar_url,
        bio: author.bio,
        is_premium: author.is_premium,
        followers_count: author.followers_count,
        following_count: author.following_count,
        stories_count: author.stories_count,
        total_likes: item.total_likes, // 添加点赞总数
      };
    })
    .filter(Boolean) as Profile[];
  
  return { data: profiles, error: null };
}

/**
 * 检查表中是否存在指定列
 * @param tableName 表名
 * @param columnName 列名
 * @returns 是否存在
 */
async function hasColumn(tableName: string, columnName: string): Promise<boolean> {
  try {
    // 使用 information_schema 查询列是否存在
    const { data, error } = await supabase.rpc('column_exists', {
      table_name: tableName,
      column_name: columnName,
    });
    
    if (error) {
      console.error(`Error checking if column ${columnName} exists in ${tableName}:`, error);
      // 默认假设列存在，避免查询失败导致功能不可用
      return true;
    }
    
    return !!data;
  } catch (error) {
    console.error(`Unexpected error checking column existence:`, error);
    // 默认假设列存在，避免查询失败导致功能不可用
    return true;
  }
}