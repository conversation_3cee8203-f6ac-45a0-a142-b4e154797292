import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface LoadingStateProps {
  isLoading: boolean;
}

export function LoadingState({ isLoading }: LoadingStateProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  if (!isLoading) return null;

  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
  );
}

interface ErrorStateProps {
  error: string | null;
  defaultMessage?: string;
}

export function ErrorState({ error, defaultMessage }: ErrorStateProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  if (!error) return null;

  return (
    <View style={styles.errorContainer}>
      <Text style={styles.errorText}>
        {error ||
          defaultMessage ||
          t('social.userProfile.errors.generic', '加载用户资料时出错')}
      </Text>
    </View>
  );
}
