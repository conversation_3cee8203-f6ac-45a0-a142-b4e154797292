import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories';
import { createDynamicStyles } from '../screens/StoryDetailScreen.styles';

interface StorySegmentsListProps {
  segments: StorySegment[];
  formatDate: (isoString: string) => string;
}

export function StorySegmentsList({
  segments,
  formatDate,
}: StorySegmentsListProps) {
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);
  const { t } = useTranslation();

  if (!segments || segments.length === 0) {
    return (
      <Text style={styles.noSegmentsText}>
        {t(
          'storyDetail.noContent',
          'This story has no content yet. Be the first to add to it!'
        )}
      </Text>
    );
  }

  return (
    <>
      {segments.map((segment, index) => (
        <View
          key={segment.id}
          style={[
            styles.segmentCard,
            index === 0 && styles.firstSegmentCard,
            index === segments.length - 1 && styles.lastSegmentCard,
          ]}
        >
          {index > 0 && <View style={styles.segmentDivider} />}
          <Text style={styles.segmentContent}>{segment.content}</Text>

          <View style={styles.segmentFooter}>
            <Text style={styles.segmentAuthor}>
              {segment.profiles?.username ||
                t('storyDetail.unknownAuthor', 'Unknown Author')}
            </Text>
            <Text style={styles.segmentTimestamp}>
              {formatDate(segment.created_at)}
            </Text>
          </View>
        </View>
      ))}
    </>
  );
}
