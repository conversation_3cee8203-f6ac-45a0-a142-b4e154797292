import React, { useMemo } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStoryDetailHeaderStyles } from './StoryDetailHeader.styles';
import { StoryWithSegments } from '@/api/stories'; // Assuming this path is correct

interface StoryDetailHeaderProps {
  story: StoryWithSegments; // Use the full story object for now, can be optimized later
  isLiked: boolean;
  likeCount: number;
  onLikeToggle: () => void;
  isLiking: boolean;
}

export default function StoryDetailHeader({
  story,
  isLiked,
  likeCount,
  onLikeToggle,
  isLiking,
}: StoryDetailHeaderProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = useMemo(() => createStoryDetailHeaderStyles(theme), [theme]);

  if (!story) return null; // Should not happen if parent component handles loading

  return (
    <View style={styles.headerSection}>
      {story.cover_image_url && (
        <Image
          source={{ uri: story.cover_image_url }}
          style={styles.coverImage}
          resizeMode="cover"
        />
      )}
      <Text style={styles.title}>{story.title}</Text>
      <Text style={styles.author}>
        {t('storyDetail.by', 'By')}{' '}
        {story.profiles?.username ||
          t('storyDetail.unknownAuthor', 'Unknown Author')}
      </Text>

      {story.tags && story.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {story.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
      )}

      <View style={styles.likeButtonContainer}>
        <TouchableOpacity
          onPress={onLikeToggle}
          style={[styles.likeButton, isLiked && styles.likedButton]}
          disabled={isLiking}
        >
          <Ionicons
            name={isLiked ? 'heart' : 'heart-outline'}
            size={24}
            color={isLiked ? theme.colors.onPrimary : theme.colors.primary} // Adjusted color for liked state
            style={styles.likeIcon}
          />
          <Text style={[styles.likeText, isLiked && styles.likedText]}>
            {likeCount}{' '}
            {likeCount === 1
              ? t('storyDetail.like', 'Like')
              : t('storyDetail.likes', 'Likes')}
          </Text>
          {isLiking && (
            <ActivityIndicator
              size="small"
              color={isLiked ? theme.colors.onPrimary : theme.colors.primary} // Consistent with icon color
              style={styles.activityIndicator}
            />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
