import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

export function SearchResultsLoading() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.centerContainer}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
  );
}
