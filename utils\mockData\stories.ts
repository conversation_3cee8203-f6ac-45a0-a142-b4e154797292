import { Story } from '../../types/story';

// Mock stories
export const mockStories: Story[] = [
  {
    id: '1',
    title: '星际迷航：量子裂隙',
    coverImage:
      'https://images.pexels.com/photos/1169754/pexels-photo-1169754.jpeg',
    theme: ['科幻', '冒险'],
    mainBranchId: 'branch1',
    authorId: 'user1',
    authorName: '星辰大海',
    createdAt: '2023-09-01T08:00:00Z',
    updatedAt: '2023-09-15T14:30:00Z',
    isCompleted: false,
    likes: 256,
    views: 1204,
    branches: {
      branch1: {
        id: 'branch1',
        content:
          '太空年历3042年，人类已经在银河系建立了繁荣的殖民地。星际飞船"探索者"号在例行巡航时，意外发现了一个量子裂隙，船长李瑞决定派出探索小队...',
        authorId: 'user1',
        authorName: '星辰大海',
        isAiGenerated: false,
        createdAt: '2023-09-01T08:00:00Z',
        likes: 156,
        parentBranchId: null,
        childBranchIds: ['branch2', 'branch3'],
      },
      branch2: {
        id: 'branch2',
        content:
          '探索小队由首席科学家张明、安全官罗伯特和外星生物学家艾莉组成。他们乘坐着小型穿梭机缓缓接近裂隙，各种仪器开始疯狂地发出警报...',
        authorId: 'user3',
        authorName: '追光者',
        isAiGenerated: false,
        createdAt: '2023-09-05T10:25:00Z',
        likes: 89,
        parentBranchId: 'branch1',
        childBranchIds: [],
      },
      branch3: {
        id: 'branch3',
        content:
          '李瑞船长否决了探索小队的提议，决定让"探索者"号整艘船直接进入裂隙。引擎全速启动，船身开始震颤，周围的空间扭曲成奇异的形状...',
        authorId: 'user2',
        authorName: 'AI助手',
        isAiGenerated: true,
        createdAt: '2023-09-03T16:45:00Z',
        likes: 102,
        parentBranchId: 'branch1',
        childBranchIds: [],
      },
    },
    summary:
      '在遥远的未来，一艘星际飞船发现了一个神秘的量子裂隙，这个发现将改变人类对宇宙的认知。',
    isAiAssisted: true,
    isPremium: false,
  },
  {
    id: '2',
    title: '迷雾森林的秘密',
    coverImage:
      'https://images.pexels.com/photos/925743/pexels-photo-925743.jpeg',
    theme: ['奇幻', '冒险'],
    mainBranchId: 'branch1',
    authorId: 'user2',
    authorName: '幻境漫步',
    createdAt: '2023-08-15T09:30:00Z',
    updatedAt: '2023-09-10T11:20:00Z',
    isCompleted: true,
    likes: 342,
    views: 1876,
    branches: {
      branch1: {
        id: 'branch1',
        content:
          '小镇边缘的迷雾森林一直是禁地，传说进入其中的人再也没有回来过。但当村里的孩子们接连失踪后，年轻的猎人艾伦决定铤而走险...',
        authorId: 'user2',
        authorName: '幻境漫步',
        isAiGenerated: false,
        createdAt: '2023-08-15T09:30:00Z',
        likes: 198,
        parentBranchId: null,
        childBranchIds: ['branch2'],
      },
      branch2: {
        id: 'branch2',
        content:
          '艾伦带着他的猎弓和一把银匕首，踏入了迷雾笼罩的森林。浓雾中，树木扭曲成诡异的形状，不远处传来奇怪的低语声...',
        authorId: 'user2',
        authorName: '幻境漫步',
        isAiGenerated: false,
        createdAt: '2023-08-16T14:20:00Z',
        likes: 144,
        parentBranchId: 'branch1',
        childBranchIds: [],
      },
    },
    summary:
      '一个勇敢的猎人进入被诅咒的迷雾森林，寻找失踪的孩子们，却发现了一个超越想象的奇幻世界。',
    isAiAssisted: false,
    isPremium: true,
  },
  {
    id: '3',
    title: '记忆收藏家',
    coverImage:
      'https://images.pexels.com/photos/3693788/pexels-photo-3693788.jpeg',
    theme: ['悬疑', '都市'],
    mainBranchId: 'branch1',
    authorId: 'user3',
    authorName: '追光者',
    createdAt: '2023-09-10T16:45:00Z',
    updatedAt: '2023-09-16T09:10:00Z',
    isCompleted: false,
    likes: 189,
    views: 890,
    branches: {
      branch1: {
        id: 'branch1',
        content:
          '张警官接到一起离奇的案件：城市里出现了数十名失忆患者，他们都丢失了同一天的记忆。唯一的线索是每个人手腕上都有一个小小的针孔...',
        authorId: 'user3',
        authorName: '追光者',
        isAiGenerated: false,
        createdAt: '2023-09-10T16:45:00Z',
        likes: 112,
        parentBranchId: null,
        childBranchIds: ['branch2'],
      },
      branch2: {
        id: 'branch2',
        content:
          '循着线索，张警官来到了城郊的一家名为"记忆博物馆"的私人展览馆。馆主是一位和蔼的老人，展厅里陈列着无数标注日期的水晶瓶...',
        authorId: 'user1',
        authorName: '星辰大海',
        isAiGenerated: false,
        createdAt: '2023-09-12T20:35:00Z',
        likes: 77,
        parentBranchId: 'branch1',
        childBranchIds: [],
      },
    },
    summary: '一名警探调查城市中的集体失忆案件，揭开了关于人类记忆的惊人秘密。',
    isAiAssisted: true,
    isPremium: false,
  },
];
