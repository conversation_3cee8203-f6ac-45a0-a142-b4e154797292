import React from 'react';
import { View } from 'react-native';
import { Text, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface StoryListItemStatsProps {
  views: number;
  likes: number;
  updatedAt: string | undefined;
  formatDate: (dateString: string | undefined) => string;
}

export function StoryListItemStats({
  views,
  likes,
  updatedAt,
  formatDate,
}: StoryListItemStatsProps) {
  const theme = usePaperTheme();

  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: theme.spacing?.xs || 4,
      }}
    >
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: theme.spacing?.xs || 4,
        }}
      >
        <Icon
          source="calendar"
          size={12}
          color={theme.colors.onSurfaceVariant}
        />
        <Text
          variant="bodySmall"
          style={{ color: theme.colors.onSurfaceVariant }}
        >
          {formatDate(updatedAt)}
        </Text>
      </View>

      <View
        style={{
          flexDirection: 'row',
          gap: theme.spacing?.md || 12,
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: theme.spacing?.xs || 4,
          }}
        >
          <Icon source="eye" size={12} color={theme.colors.onSurfaceVariant} />
          <Text
            variant="bodySmall"
            style={{ color: theme.colors.onSurfaceVariant }}
          >
            {views}
          </Text>
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: theme.spacing?.xs || 4,
          }}
        >
          <Icon
            source="heart"
            size={12}
            color={theme.colors.onSurfaceVariant}
          />
          <Text
            variant="bodySmall"
            style={{ color: theme.colors.onSurfaceVariant }}
          >
            {likes}
          </Text>
        </View>
      </View>
    </View>
  );
}
