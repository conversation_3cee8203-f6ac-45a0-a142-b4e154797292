import React from 'react';
import { View, Text } from 'react-native';
import { Eye, Heart, Calendar } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface StoryListItemStatsProps {
  views: number;
  likes: number;
  updatedAt: string | undefined;
  formatDate: (dateString: string | undefined) => string;
}

export function StoryListItemStats({
  views,
  likes,
  updatedAt,
  formatDate,
}: StoryListItemStatsProps) {
  const appTheme = useAppTheme();
  const styles = createStyles(appTheme);

  return (
    <View style={styles.infoRow}>
      <View style={styles.stat}>
        <Calendar size={12} color={appTheme.colors.secondaryText} />
        <Text
          style={[styles.statText, { color: appTheme.colors.secondaryText }]}
        >
          {formatDate(updatedAt)}
        </Text>
      </View>

      <View style={styles.statsGroup}>
        <View style={styles.stat}>
          <Eye size={12} color={appTheme.colors.secondaryText} />
          <Text
            style={[styles.statText, { color: appTheme.colors.secondaryText }]}
          >
            {views}
          </Text>
        </View>

        <View style={styles.stat}>
          <Heart size={12} color={appTheme.colors.secondaryText} />
          <Text
            style={[styles.statText, { color: appTheme.colors.secondaryText }]}
          >
            {likes}
          </Text>
        </View>
      </View>
    </View>
  );
}
