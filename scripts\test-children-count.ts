/**
 * 测试 children_count 字段处理
 * 
 * 这个脚本用于测试 getChildrenCount 函数对不同格式的 children_count 字段的处理
 */
import { getChildrenCount } from '../utils/storyHelpers';

// 测试用例
const testCases = [
  { 
    name: '数字类型', 
    input: 5, 
    expected: 5 
  },
  { 
    name: '空数组', 
    input: [], 
    expected: 0 
  },
  { 
    name: '普通数组', 
    input: [1, 2, 3], 
    expected: 3 
  },
  { 
    name: '对象带 count 属性', 
    input: { count: 10 }, 
    expected: 10 
  },
  { 
    name: '普通对象', 
    input: { foo: 'bar' }, 
    expected: 1 
  },
  { 
    name: 'null', 
    input: null, 
    expected: 0 
  },
  { 
    name: 'undefined', 
    input: undefined, 
    expected: 0 
  },
  { 
    name: 'useStoryFeed 格式 - 有子分支', 
    input: [{ count: 3 }], 
    expected: 3 
  },
  { 
    name: 'useStoryFeed 格式 - 无子分支', 
    input: [{ count: 0 }], 
    expected: 0 
  }
];

// 运行测试
console.log('测试 getChildrenCount 函数:');
console.log('==========================');

let passCount = 0;
let failCount = 0;

testCases.forEach((testCase, index) => {
  const result = getChildrenCount(testCase.input);
  const passed = result === testCase.expected;
  
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log(`  输入: ${JSON.stringify(testCase.input)}`);
  console.log(`  期望: ${testCase.expected}`);
  console.log(`  结果: ${result}`);
  console.log(`  状态: ${passed ? '✅ 通过' : '❌ 失败'}`);
  console.log('');
  
  if (passed) {
    passCount++;
  } else {
    failCount++;
  }
});

console.log('==========================');
console.log(`测试结果: ${passCount} 通过, ${failCount} 失败`);
