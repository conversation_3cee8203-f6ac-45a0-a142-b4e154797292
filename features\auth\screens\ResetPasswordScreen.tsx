import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './ResetPasswordScreen.styles';
import { resetPassword } from '@/api/auth';
import { Link, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

/**
 * ResetPasswordScreen component
 * 
 * This screen allows users to request a password reset by entering their email address.
 * It sends a reset password link to the provided email.
 */
export default function ResetPasswordScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  /**
   * Handles the password reset request
   */
  const handleResetPassword = async () => {
    // Validate email
    if (!email) {
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.fieldRequired')
      );
      return;
    }

    if (!emailRegex.test(email)) {
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.invalidEmail')
      );
      return;
    }

    setLoading(true);
    try {
      const { error } = await resetPassword(email);
      setLoading(false);

      if (error) {
        console.error('Reset password error:', error);
        Alert.alert(
          t('auth.errorTitle'),
          error.message || t('auth.resetPasswordFailed')
        );
      } else {
        Alert.alert(
          t('auth.successTitle'),
          t('auth.resetPasswordSuccess'),
          [
            {
              text: 'OK',
              onPress: () => router.replace('/(auth)/login')
            }
          ]
        );
      }
    } catch (error) {
      setLoading(false);
      console.error('Reset password error:', error);
      Alert.alert(
        t('auth.errorTitle'),
        t('auth.networkError')
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('auth.resetPasswordTitle')}</Text>
      
      <Text style={styles.instructions}>
        {t('auth.resetPasswordInstructions')}
      </Text>

      <TextInput
        style={styles.input}
        placeholder={t('auth.emailLabel')}
        placeholderTextColor={theme.colors.placeholder}
        keyboardType="email-address"
        autoCapitalize="none"
        value={email}
        onChangeText={setEmail}
      />

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleResetPassword}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color={theme.colors.onPrimary} />
        ) : (
          <Text style={styles.buttonText}>
            {t('auth.resetPasswordButton')}
          </Text>
        )}
      </TouchableOpacity>

      <View style={styles.loginContainer}>
        <Text style={styles.loginText}>{t('auth.switchToLogin')}</Text>
        <Link href="/(auth)/login" style={styles.loginLink}>
          {t('auth.loginButton')}
        </Link>
      </View>
    </View>
  );
}
