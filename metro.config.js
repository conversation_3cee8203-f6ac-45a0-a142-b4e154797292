// Learn more https://docs.expo.dev/guides/customizing-metro/
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Disable package exports support for compatibility with certain packages (e.g., supabase-js)
// See: https://expo.dev/changelog/sdk-53#the-packagejsonexports-field-is-now-enabled-by-default-in-metro-bundler
config.resolver.unstable_enablePackageExports = false;

module.exports = config; 