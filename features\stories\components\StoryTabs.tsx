import React from 'react';
import { SegmentedButtons } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

export type StoryTabKey =
  | 'drafts'
  | 'published'
  | 'reading'
  | 'favorites'
  | 'saved';

interface StoryTabsProps {
  activeTab: StoryTabKey;
  onTabPress: (tabKey: StoryTabKey) => void;
}

const TABS: StoryTabKey[] = [
  'drafts',
  'published',
  'reading',
  'favorites',
  'saved',
];

const getTabIcon = (tabKey: StoryTabKey): string => {
  switch (tabKey) {
    case 'drafts':
      return 'pencil';
    case 'published':
      return 'book-open-variant';
    case 'reading':
      return 'clock-outline';
    case 'favorites':
      return 'heart';
    case 'saved':
      return 'bookmark';
    default:
      return 'help-circle';
  }
};

const getTabTitleKey = (tabKey: StoryTabKey): string => {
  switch (tabKey) {
    case 'drafts':
      return 'storyTabDrafts';
    case 'published':
      return 'storyTabPublished';
    case 'reading':
      return 'storyTabReading';
    case 'favorites':
      return 'storyTabFavorites';
    case 'saved':
      return 'storyTabSaved';
    default:
      return '';
  }
};

export function StoryTabs({ activeTab, onTabPress }: StoryTabsProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();

  // Convert tabs to SegmentedButtons format
  const buttons = TABS.map((tabKey) => ({
    value: tabKey,
    label: t(getTabTitleKey(tabKey)),
    icon: getTabIcon(tabKey),
  }));

  return (
    <SegmentedButtons
      value={activeTab}
      onValueChange={onTabPress}
      buttons={buttons}
      style={{
        marginHorizontal: theme.spacing?.md || 16,
        marginVertical: theme.spacing?.sm || 8,
      }}
    />
  );
}
