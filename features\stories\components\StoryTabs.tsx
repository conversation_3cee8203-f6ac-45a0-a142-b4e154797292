import React from 'react';
import { ScrollView, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './StoryTabs.styles';
import { Book, Bookmark, Clock, Heart, PenLine } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

export type StoryTabKey = 'drafts' | 'published' | 'reading' | 'favorites' | 'saved';

interface StoryTabsProps {
  activeTab: StoryTabKey;
  onTabPress: (tabKey: StoryTabKey) => void;
}

const TABS: StoryTabKey[] = ['drafts', 'published', 'reading', 'favorites', 'saved'];

const renderTabIcon = (tabKey: StoryTabKey, active: boolean, theme: ReturnType<typeof useAppTheme>) => {
  const iconColor = active ? theme.colors.primary : theme.colors.secondaryText;
  const size = 18; // Slightly smaller icon
  switch (tabKey) {
    case 'drafts': return <PenLine size={size} color={iconColor} />;
    case 'published': return <Book size={size} color={iconColor} />;
    case 'reading': return <Clock size={size} color={iconColor} />;
    case 'favorites': return <Heart size={size} color={iconColor} />;
    case 'saved': return <Bookmark size={size} color={iconColor} />;
    default: return null;
  }
};

const getTabTitleKey = (tabKey: StoryTabKey): string => {
  switch (tabKey) {
    case 'drafts': return 'storyTabDrafts';
    case 'published': return 'storyTabPublished';
    case 'reading': return 'storyTabReading';
    case 'favorites': return 'storyTabFavorites';
    case 'saved': return 'storyTabSaved';
    default: return '';
  }
}

export function StoryTabs({ activeTab, onTabPress }: StoryTabsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false} 
      style={styles.tabsContainer} // Apply container style here
      contentContainerStyle={styles.scrollViewContent}
    >
      {TABS.map((tabKey) => {
        const isActive = activeTab === tabKey;
        const textColor = isActive ? theme.colors.primary : theme.colors.secondaryText;
        const backgroundColor = isActive ? `${theme.colors.primary}1A` : 'transparent'; // Use alpha primary for active background

        return (
          <TouchableOpacity
            key={tabKey}
            style={[styles.tab, { backgroundColor }]}
            onPress={() => onTabPress(tabKey)}
          >
            {renderTabIcon(tabKey, isActive, theme)}
            <Text style={[styles.tabText, { color: textColor }]}>
              {t(getTabTitleKey(tabKey))}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
} 