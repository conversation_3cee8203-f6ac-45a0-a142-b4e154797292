import React, { useState } from 'react';
import { View } from 'react-native';
import { Menu, Button, Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';

export interface SortOption {
  id: string;
  label: string;
}

interface SortSelectorProps {
  options: SortOption[];
  selectedId: string;
  onSelect: (id: string) => void;
  label?: string;
}

export default function SortSelector({
  options,
  selectedId,
  onSelect,
  label,
}: SortSelectorProps) {
  const [menuVisible, setMenuVisible] = useState(false);
  const theme = usePaperTheme();
  const { t } = useTranslation();

  const selectedOption = options.find((option) => option.id === selectedId);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const handleSelect = (id: string) => {
    onSelect(id);
    closeMenu();
  };

  return (
    <View style={{ paddingHorizontal: 16, paddingVertical: 8 }}>
      {label && (
        <Text variant="labelMedium" style={{ marginBottom: 8 }}>
          {label}
        </Text>
      )}

      <Menu
        visible={menuVisible}
        onDismiss={closeMenu}
        anchor={
          <Button
            mode="outlined"
            onPress={openMenu}
            icon="chevron-down"
            contentStyle={{ flexDirection: 'row-reverse' }}
          >
            {selectedOption?.label || options[0]?.label}
          </Button>
        }
      >
        {options.map((option) => (
          <Menu.Item
            key={option.id}
            title={option.label}
            onPress={() => handleSelect(option.id)}
            trailingIcon={option.id === selectedId ? 'check' : undefined}
          />
        ))}
      </Menu>
    </View>
  );
}
