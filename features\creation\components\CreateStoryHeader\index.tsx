import React from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export default function CreateStoryHeader() {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <View style={{ marginBottom: theme.spacing?.lg || 24 }}>
      <Text
        variant="headlineSmall"
        style={{ marginBottom: theme.spacing?.sm || 8 }}
      >
        {t('createStoryTitle', '创建新故事')}
      </Text>
      <Text
        variant="bodyMedium"
        style={{ color: theme.colors.onSurfaceVariant }}
      >
        {t('storyForm.createDescription', '开始你的创作之旅，写下你的故事')}
      </Text>
    </View>
  );
}
