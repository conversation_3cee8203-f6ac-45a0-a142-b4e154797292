# 业务功能层结构详情

本文档详细描述了 SupaPose 项目中业务功能层的结构。业务功能层是应用程序的核心，包含各个功能模块的业务逻辑和状态管理。

## 功能模块目录结构

```
features/                  # 业务功能层 (核心)
├── auth/                  # 认证功能模块
│   ├── components/        # 认证相关组件 (LoginForm, RegisterForm) (样式将使用 Tamagui)
│   ├── screens/           # 页面组件 (LoginScreen, RegisterScreen) (样式将使用 Tamagui)
│   ├── hooks/             # 认证特定 hooks
│   ├── store/             # 认证状态管理
│   ├── services/          # 认证业务逻辑
│   └── types/             # 认证模块类型定义
├── home/                  # 首页功能模块
│   ├── components/        # 首页特定组件
│   │   ├── ThemeCarousel/ # 主题轮播组件
│   │   │   └── index.tsx  # 组件入口 (样式使用 Tamagui props/variants)
│   │   ├── FeaturedStory/ # 精选故事组件
│   │   │   └── index.tsx  # 组件入口 (样式使用 Tamagui props/variants)
│   │   ├── StoryListTabs/ # 故事列表标签页组件
│   │   │   └── index.tsx  # 组件入口 (样式使用 Tamagui props/variants)
│   │   ├── StoryGrid/     # 故事网格组件
│   │   │   └── index.tsx  # 组件入口 (样式使用 Tamagui props/variants)
│   │   ├── HomeScreenLoading.tsx # 首页加载状态组件 (样式使用 Tamagui props/variants)
│   │   ├── HomeScreenError.tsx   # 首页错误状态组件 (样式使用 Tamagui props/variants)
│   │   └── HomeScreenContent.tsx # 首页内容组件 (样式使用 Tamagui props/variants)
│   ├── hooks/             # 首页特定钩子
│   │   └── useHomeScreenData.ts  # 首页数据获取钩子
│   ├── screens/           # 页面组件
│   │   └── HomeScreen.tsx # 首页屏幕组件 (样式使用 Tamagui props/variants)
│   └── ...                # 其他首页相关代码
├── creation/              # 创作功能模块
│   ├── components/        # 创作特定组件
│   │   ├── CreateStoryHeader/  # 创建故事头部组件
│   │   │   └── index.tsx      # 组件入口 (样式使用 Tamagui props/variants)
│   │   ├── CreateStoryForm/   # 创建故事表单组件
│   │   │   └── index.tsx      # 组件入口 (样式使用 Tamagui props/variants)
│   │   └── AISuggestionsSection/ # AI 建议部分组件
│   │       └── index.tsx      # 组件入口 (样式使用 Tamagui props/variants)
│   ├── hooks/             # 创作特定 hooks
│   │   ├── useCreateStory.ts  # 创建故事 hook
│   │   └── useAISuggestions.ts # AI 建议 hook
│   ├── screens/           # 页面组件
│   │   └── CreateStoryScreen.tsx # 创建故事页面 (样式使用 Tamagui props/variants)
│   └── ...                # 其他创作相关代码
├── stories/               # 故事功能模块
│   ├── components/        # 故事特定组件 (所有组件样式使用 Tamagui props/variants)
│   │   ├── StoryDetailHeader/      # 故事详情页头部组件
│   │   ├── StorySegmentItem/       # 故事详情页单个段落组件
│   │   ├── AddSegmentForm/         # 故事详情页添加段落表单组件
│   │   ├── AISuggestionBlock/      # 故事详情页AI建议组件
│   │   ├── EmptyStoriesState/      # 空故事状态组件
│   │   ├── StoryAddSegment/        # 添加故事段落组件
│   │   ├── StoryHeader/            # 故事列表等页面的头部组件
│   │   ├── StoryList/              # 故事列表组件
│   │   ├── StorySegmentsList/      # 故事段落列表组件
│   │   ├── StoryTabs/              # 故事相关的标签页组件
│   │   ├── StoryDetailLoading.tsx  # 故事详情加载状态组件
│   │   ├── StoryDetailError.tsx    # 故事详情错误状态组件
│   │   ├── StoryDetailEmpty.tsx    # 故事详情为空状态组件
│   │   └── StoryDetailContent.tsx  # 故事详情内容组件
│   ├── screens/           # 页面组件
│   │   ├── StoryListScreen/        # 故事列表页面
│   │   │   └── index.tsx           # 组件入口 (样式使用 Tamagui props/variants)
│   │   └── StoryDetailScreen.tsx   # 故事详情页面 (样式使用 Tamagui props/variants)
│   ├── hooks/             # 故事特定 hooks
│   │   ├── useStoryDetails.ts
│   │   ├── useStoryAISuggestions.ts
│   │   └── useStorySegmentManagement.ts
│   └── ...                # 其他故事相关代码
├── social/                # 社交功能模块
│   ├── components/        # 社交特定组件
│   │   ├── UserProfileHeader/  # 用户资料头部组件
│   │   │   ├── index.tsx      # 组件入口
│   │   │   └── styles.ts      # 组件样式
│   │   ├── UserBio/           # 用户简介组件
│   │   │   ├── index.tsx      # 组件入口
│   │   │   └── styles.ts      # 组件样式
│   │   ├── UserStats/         # 用户统计信息组件
│   │   │   ├── index.tsx      # 组件入口
│   │   │   └── styles.ts      # 组件样式
│   │   ├── UserActionButtons/ # 用户操作按钮组件
│   │   │   ├── index.tsx      # 组件入口
│   │   │   └── styles.ts      # 组件样式
│   │   ├── UserStories/       # 用户故事列表组件
│   │   │   ├── index.tsx      # 组件入口
│   │   │   └── styles.ts      # 组件样式
│   │   ├── LoadingAndErrorStates/ # 加载和错误状态组件
│   │   │   ├── index.tsx      # 组件入口
│   │   │   └── styles.ts      # 组件样式
│   │   ├── DiscoverTab.tsx    # 发现标签页组件
│   │   └── FeedTab/           # 动态标签页组件
│   │       ├── index.tsx      # 组件入口
│   │       ├── EmptyState.tsx # 空状态组件
│   │       ├── ListHeader.tsx # 列表头部组件
│   │       └── ListFooter.tsx # 列表底部组件
│   ├── hooks/             # 社交特定 hooks
│   │   ├── useUserProfile.ts  # 用户资料 hook
│   │   └── useUserStories.ts  # 用户故事 hook
│   ├── screens/           # 页面组件
│   │   ├── UserProfileScreen.tsx # 用户资料页面
│   │   ├── UserProfileScreen.styles.ts # 用户资料页面样式
│   │   └── SocialScreen.tsx   # 社交页面
│   └── ...                # 其他社交相关代码
├── profile/               # 个人资料功能模块
│   ├── components/        # 个人资料特定组件
│   │   ├── ProfileScreenLoading.tsx # 个人资料加载状态组件
│   │   ├── ProfileScreenError.tsx   # 个人资料错误状态组件
│   │   ├── ProfileScreenAuth.tsx    # 个人资料认证状态组件
│   │   └── ProfileScreenContent.tsx # 个人资料内容组件
│   ├── hooks/             # 个人资料特定钩子
│   │   └── useProfileData.ts        # 个人资料数据获取钩子
│   ├── screens/           # 页面组件 (ProfileScreen)
│   ├── types/             # 个人资料模块类型定义
│   └── ...                # 其他个人资料相关代码
├── search/                # 搜索功能模块
│   ├── components/        # 搜索特定组件
│   │   ├── SearchTabs/    # 搜索标签页组件
│   │   │   ├── index.tsx  # 组件入口
│   │   │   └── styles.ts  # 组件样式
│   │   └── SearchResults/ # 搜索结果组件
│   │       ├── index.tsx  # 组件入口
│   │       ├── styles.ts  # 组件样式
│   │       ├── SearchResultsLoading.tsx # 搜索结果加载状态组件
│   │       ├── SearchResultsEmpty.tsx   # 搜索结果为空状态组件
│   │       ├── StoriesResults.tsx       # 故事搜索结果组件
│   │       ├── UsersResults.tsx         # 用户搜索结果组件
│   │       └── SectionHeader.tsx        # 搜索结果分区头部组件
│   ├── hooks/             # 搜索特定 hooks
│   │   └── useSearch.ts   # 搜索 hook
│   └── screens/           # 页面组件
│       ├── SearchScreen.tsx # 搜索页面
│       └── SearchScreen.styles.ts # 搜索页面样式
└── settings/              # 设置功能模块
    ├── components/        # 设置特定组件
    ├── screens/           # 页面组件 (SettingsScreen)
    └── ...                # 其他设置相关代码
```

## 功能模块设计原则

1. **模块化**：每个功能模块应该是独立的，可以单独开发和测试。
2. **内聚性**：相关的功能应该放在一起，形成一个内聚的模块。
3. **低耦合**：模块之间的依赖应该尽可能少，通过明确的接口进行通信。
4. **可测试性**：模块应该设计得易于测试，避免复杂的状态管理和副作用。
5. **可扩展性**：模块应该设计得易于扩展，以便添加新的功能。

## 功能模块组织结构

每个功能模块都应该遵循以下组织结构：

- **components/**：模块特定的组件，只在该模块内使用。
- **screens/**：页面组件，负责渲染整个页面。
- **hooks/**：模块特定的钩子，用于处理业务逻辑和状态管理。
- **store/**：模块特定的状态管理，使用 Zustand 实现。
- **services/**：模块特定的业务逻辑，如 API 调用和数据处理。
- **types/**：模块特定的类型定义。

## 相关文档

- [ProjectStructure.md](./.mine/ProjectStructure.md) - 项目整体结构概述
- [ComponentsStructure.md](./.mine/ComponentsStructure.md) - 组件和 UI 层结构详情
- [PrinciplesAndPractices.md](./.mine/PrinciplesAndPractices.md) - 开发原则与最佳实践
