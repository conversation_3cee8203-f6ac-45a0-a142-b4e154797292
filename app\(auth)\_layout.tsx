import { Stack } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';

export default function AuthLayout() {
  const theme = useAppTheme();
  return (
    <Stack
      screenOptions={{
        headerShown: false, // Hide header for auth screens
        contentStyle: { backgroundColor: theme.colors.background },
      }}
    >
      {/* Auth routes will be defined here */}
    </Stack>
  );
}
