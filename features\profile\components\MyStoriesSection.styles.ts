import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    section: {
      // Use margin from ProfileScreen.styles, only add bottom margin if needed
      marginBottom: theme.spacing.lg,
    },
    titleContainer: {
      paddingHorizontal: theme.spacing.md, // Align title with screen padding
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    scrollView: {
      // No specific styles needed for the scroll view itself usually
      // 或者为 FlatList 添加一些基础样式，如果需要的话
    },
    scrollViewContent: {
      paddingHorizontal: theme.spacing.md, // Add padding to the content
      paddingVertical: theme.spacing.sm, // Optional vertical padding for cards
    },
    placeholderText: {
      fontSize: 14,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      textAlign: 'center',
      paddingHorizontal: theme.spacing.md, // Ensure placeholder is also padded
    },
    loader: {
      // 添加 loader 样式
      marginTop: theme.spacing.md,
      alignItems: 'center', // 居中加载指示器
    },
  });
