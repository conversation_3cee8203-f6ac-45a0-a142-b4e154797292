import React from 'react';
import { Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface PremiumBadgeProps {
  visible: boolean;
}

export function PremiumBadge({ visible }: PremiumBadgeProps) {
  const theme = usePaperTheme();

  if (!visible) return null;

  return (
    <Chip
      mode="flat"
      icon="crown"
      compact
      style={{
        backgroundColor: theme.colors.tertiary,
        position: 'absolute',
        top: 8,
        right: 8,
        zIndex: 1,
      }}
      textStyle={{
        color: theme.colors.onTertiary,
        fontSize: 10,
      }}
    >
      VIP
    </Chip>
  );
}
