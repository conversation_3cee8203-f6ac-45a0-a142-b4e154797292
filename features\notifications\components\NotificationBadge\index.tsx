import React, { useEffect } from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { getUnreadNotificationCount } from '@/api/notifications';

interface NotificationBadgeProps {
  count?: number;
  size?: 'small' | 'medium' | 'large';
  autoRefresh?: boolean;
  refreshInterval?: number;
  onCountChange?: (count: number) => void;
}

export default function NotificationBadge({
  count: propCount,
  size = 'medium',
  autoRefresh = false,
  refreshInterval = 60000, // 1分钟
  onCountChange,
}: NotificationBadgeProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const [count, setCount] = React.useState(propCount || 0);

  // 如果外部传入了 count，使用外部的 count
  useEffect(() => {
    if (propCount !== undefined) {
      setCount(propCount);
    }
  }, [propCount]);

  // 如果没有外部传入 count，自动获取未读通知数量
  useEffect(() => {
    if (propCount === undefined) {
      fetchUnreadCount();
    }
  }, [propCount]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && propCount === undefined) {
      const intervalId = setInterval(() => {
        fetchUnreadCount();
      }, refreshInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoRefresh, refreshInterval, propCount]);

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    try {
      const { count: unreadCount, error } = await getUnreadNotificationCount();
      if (error) {
        throw new Error(error.message);
      }
      setCount(unreadCount);
      onCountChange?.(unreadCount);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  };

  // 如果没有未读通知，不显示徽章
  if (count === 0) {
    return null;
  }

  // 根据 size 获取样式
  const getBadgeStyle = () => {
    switch (size) {
      case 'small':
        return styles.badgeSmall;
      case 'large':
        return styles.badgeLarge;
      case 'medium':
      default:
        return styles.badgeMedium;
    }
  };

  // 根据 size 获取文本样式
  const getTextStyle = () => {
    switch (size) {
      case 'small':
        return styles.textSmall;
      case 'large':
        return styles.textLarge;
      case 'medium':
      default:
        return styles.textMedium;
    }
  };

  // 格式化数量
  const formatCount = (count: number) => {
    if (count > 99) {
      return '99+';
    }
    return count.toString();
  };

  return (
    <View style={[styles.badge, getBadgeStyle()]}>
      <Text style={[styles.text, getTextStyle()]}>{formatCount(count)}</Text>
    </View>
  );
}