import { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { getAISuggestions } from '@/api/ai/storyGeneration';

interface UseAISuggestionsProps {
  getPrompt: () => string;
  onSelectSuggestion: (suggestion: string) => void;
}

export function useAISuggestions({
  getPrompt,
  onSelectSuggestion,
}: UseAISuggestionsProps) {
  const { t } = useTranslation();
  
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [loadingAISuggestions, setLoadingAISuggestions] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);

  const handleFetchAISuggestions = async () => {
    const prompt = getPrompt();
    
    if (!prompt.trim()) {
      Alert.alert(
        t('aiSuggestions.promptErrorTitle', '提示'),
        t(
          'aiSuggestions.promptError',
          '请输入故事标题或一些内容，以便 AI 提供建议。'
        )
      );
      return;
    }
    
    setShowAISuggestions(true);
    setLoadingAISuggestions(true);
    setAiSuggestions([]); // Clear previous suggestions
    
    try {
      const response = await getAISuggestions({ prompt });
      
      if (response.error) {
        setAiSuggestions([]);
        Alert.alert(
          t('aiSuggestions.fetchErrorTitle', '获取建议失败'),
          response.error
        );
      } else if (response.suggestions && response.suggestions.length > 0) {
        setAiSuggestions(response.suggestions);
      } else {
        setAiSuggestions([]);
        Alert.alert(
          t('aiSuggestions.fetchErrorTitle', '获取建议失败'),
          t(
            'aiSuggestions.noSuggestions',
            '暂时没有合适的建议，尝试修改你的输入或稍后再试。'
          )
        );
      }
    } catch (error) {
      console.error('Error fetching AI suggestions:', error);
      setAiSuggestions([]);
      Alert.alert(
        t('aiSuggestions.fetchErrorTitle', '获取建议失败'),
        error.message ||
          t('aiSuggestions.fetchError', '获取 AI 建议时发生错误。')
      );
    } finally {
      setLoadingAISuggestions(false);
    }
  };

  const handleSelectAISuggestion = (suggestion: string) => {
    onSelectSuggestion(suggestion);
    setShowAISuggestions(false);
    setAiSuggestions([]);
  };

  return {
    aiSuggestions,
    loadingAISuggestions,
    showAISuggestions,
    handleFetchAISuggestions,
    handleSelectAISuggestion,
  };
}
