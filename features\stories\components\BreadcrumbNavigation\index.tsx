import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ChevronRight, Home } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface BreadcrumbNavigationProps {
  storyTitle: string;
  currentPath: string[];
  pathNames?: string[];
  onPathSelect: (index: number) => void;
}

export default function BreadcrumbNavigation({
  storyTitle,
  currentPath,
  pathNames = [],
  onPathSelect,
}: BreadcrumbNavigationProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // Ensure pathNames has the same length as currentPath
  const displayNames =
    currentPath &&
    Array.isArray(currentPath) &&
    pathNames.length === currentPath.length
      ? pathNames
      : currentPath && Array.isArray(currentPath)
      ? currentPath.map((_, index) =>
          index === 0
            ? t('storyDetail.mainBranch', '主线')
            : t('storyDetail.branch', '分支') + ' ' + (index + 1)
        )
      : [];

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Story Title */}
        <TouchableOpacity
          style={styles.breadcrumbItem}
          onPress={() => onPathSelect(-1)} // -1 indicates the story root
        >
          <Home size={16} color={theme.colors.text} />
          <Text style={styles.breadcrumbText} numberOfLines={1}>
            {storyTitle}
          </Text>
        </TouchableOpacity>

        {/* Path Items */}
        {currentPath &&
          Array.isArray(currentPath) &&
          currentPath.map((pathId, index) => (
            <React.Fragment key={pathId}>
              <ChevronRight size={16} color={theme.colors.secondaryText} />
              <TouchableOpacity
                style={[
                  styles.breadcrumbItem,
                  index === currentPath.length - 1 &&
                    styles.activeBreadcrumbItem,
                ]}
                onPress={() => onPathSelect(index)}
              >
                <Text
                  style={[
                    styles.breadcrumbText,
                    index === currentPath.length - 1 &&
                      styles.activeBreadcrumbText,
                  ]}
                  numberOfLines={1}
                >
                  {displayNames[index]}
                </Text>
              </TouchableOpacity>
            </React.Fragment>
          ))}
      </ScrollView>
    </View>
  );
}
