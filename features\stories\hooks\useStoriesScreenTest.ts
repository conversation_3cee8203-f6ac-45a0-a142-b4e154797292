import { useState, useEffect } from 'react';
import { Story } from '@/api/stories';
import { StoryTabKey } from '../components/StoryTabs';
import { generateMockStories } from '@/utils/mockData';

/**
 * Test scenarios for the useStoriesScreen hook
 */
export enum TestScenario {
  SUCCESS = 'success',
  EMPTY = 'empty',
  LOADING = 'loading',
  ERROR_NETWORK = 'error_network',
  ERROR_SERVER = 'error_server',
  ERROR_AUTH = 'error_auth',
  LARGE_DATASET = 'large_dataset',
}

/**
 * Interface for the test hook result
 */
interface UseStoriesScreenTestResult {
  stories: Story[];
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  activeTab: StoryTabKey;
  setActiveTab: (tab: StoryTabKey) => void;
  refreshStories: () => Promise<void>;
  loadMoreStories: () => Promise<void>;
  hasMoreStories: boolean;
  retryFetch: () => Promise<void>;
  retryCount: number;
  setTestScenario: (scenario: TestScenario) => void;
  currentScenario: TestScenario;
}

/**
 * A test hook that simulates the useStoriesScreen hook for testing purposes
 * This allows testing different scenarios without making actual API calls
 */
export function useStoriesScreenTest(): UseStoriesScreenTestResult {
  const [activeTab, setActiveTab] = useState<StoryTabKey>('drafts');
  const [stories, setStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMoreStories, setHasMoreStories] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [currentScenario, setCurrentScenario] = useState<TestScenario>(TestScenario.SUCCESS);
  
  const storiesPerPage = 10;
  
  // Generate mock stories based on the current scenario
  const getMockStoriesForScenario = (scenario: TestScenario, tab: StoryTabKey, page: number): Story[] => {
    switch (scenario) {
      case TestScenario.EMPTY:
        return [];
      case TestScenario.LARGE_DATASET:
        // Generate 100+ stories for large dataset testing
        return generateMockStories(100 + page * storiesPerPage).slice(
          page * storiesPerPage, 
          (page + 1) * storiesPerPage
        );
      case TestScenario.SUCCESS:
      default:
        // Generate 10-20 stories for normal testing
        return generateMockStories(20).slice(
          page * storiesPerPage, 
          (page + 1) * storiesPerPage
        );
    }
  };
  
  // Simulate fetching stories
  const fetchStories = async (page: number = 0, refresh: boolean = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    
    setError(null);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    try {
      // Simulate different scenarios
      switch (currentScenario) {
        case TestScenario.ERROR_NETWORK:
          throw new Error('Network request failed. Please check your connection and try again.');
        case TestScenario.ERROR_SERVER:
          throw new Error('Server error. Please try again later.');
        case TestScenario.ERROR_AUTH:
          throw new Error('Authentication failed. Please sign in again.');
        case TestScenario.LOADING:
          // Keep loading state active
          return;
        case TestScenario.EMPTY:
        case TestScenario.SUCCESS:
        case TestScenario.LARGE_DATASET:
          const mockStories = getMockStoriesForScenario(currentScenario, activeTab, page);
          
          if (page === 0 || refresh) {
            setStories(mockStories);
          } else {
            setStories(prev => [...prev, ...mockStories]);
          }
          
          setHasMoreStories(
            currentScenario === TestScenario.LARGE_DATASET || 
            mockStories.length === storiesPerPage
          );
          setCurrentPage(page);
          break;
      }
    } catch (err: any) {
      console.error('Error in test scenario:', err);
      setError(err.message);
    } finally {
      if (currentScenario !== TestScenario.LOADING) {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    }
  };
  
  // Load initial stories when tab or scenario changes
  useEffect(() => {
    setCurrentPage(0);
    fetchStories(0, true);
  }, [activeTab, currentScenario]);
  
  // Refresh stories
  const refreshStories = async () => {
    await fetchStories(0, true);
  };
  
  // Load more stories
  const loadMoreStories = async () => {
    if (!isLoading && !isRefreshing && hasMoreStories) {
      await fetchStories(currentPage + 1);
    }
  };
  
  // Retry mechanism
  const retryFetch = async () => {
    if (error && retryCount < 3) {
      setRetryCount(prev => prev + 1);
      await fetchStories(currentPage, true);
    }
  };
  
  // Set the test scenario
  const setTestScenario = (scenario: TestScenario) => {
    setCurrentScenario(scenario);
    setRetryCount(0);
  };
  
  return {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
    setTestScenario,
    currentScenario,
  };
}
