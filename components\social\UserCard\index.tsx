import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { User as UserType } from '@/types/user';
import { BookOpen, Crown, UserPlus, Check } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface UserCardProps {
  user: UserType;
  onPress?: (userId: string) => void;
  onFollow?: (userId: string) => void;
  isFollowing?: boolean;
  isFollowingInProgress?: boolean;
}

export default function UserCard({
  user,
  onPress,
  onFollow,
  isFollowing = false,
  isFollowingInProgress = false,
}: UserCardProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress?.(user.id)}
      disabled={!onPress}
    >
      <View style={styles.topRow}>
        <Image source={{ uri: user.avatar }} style={styles.avatar} />

        {user.isPremium && (
          <View style={styles.premiumBadge}>
            <Crown size={10} color="#000" />
          </View>
        )}

        <TouchableOpacity
          style={[styles.followButton, isFollowing && styles.followingButton]}
          onPress={() => onFollow?.(user.id)}
          disabled={!onFollow || isFollowingInProgress}
        >
          {isFollowingInProgress ? (
            <ActivityIndicator
              size="small"
              color={theme.dark ? theme.colors.background : '#FFFFFF'}
            />
          ) : isFollowing ? (
            <Check size={16} color={theme.colors.primary} />
          ) : (
            <UserPlus
              size={16}
              color={theme.dark ? theme.colors.background : '#FFFFFF'}
            />
          )}
        </TouchableOpacity>
      </View>

      <Text style={styles.displayName} numberOfLines={1}>
        {user.displayName}
      </Text>

      <Text style={styles.username} numberOfLines={1}>
        @{user.username}
      </Text>

      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <BookOpen size={12} color={theme.colors.secondaryText} />
          <Text style={styles.statText}>{user.storiesCount}</Text>
        </View>

        <View style={styles.statDivider} />

        <Text style={styles.statText}>
          {user.followers} {t('followersSuffix', '粉丝')}
        </Text>
      </View>
    </TouchableOpacity>
  );
}
