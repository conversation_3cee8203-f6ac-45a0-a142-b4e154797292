-- Add missing columns to story_segments table
ALTER TABLE IF EXISTS public.story_segments
ADD COLUMN IF NOT EXISTS likes_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS dislikes_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS bookmarks_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS comment_count integer DEFAULT 0;

-- Create segment_likes table
CREATE TABLE IF NOT EXISTS public.segment_likes (
  segment_id uuid REFERENCES story_segments(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (segment_id, user_id)
);

-- Create segment_dislikes table
CREATE TABLE IF NOT EXISTS public.segment_dislikes (
  segment_id uuid REFERENCES story_segments(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (segment_id, user_id)
);

-- Create segment_bookmarks table
CREATE TABLE IF NOT EXISTS public.segment_bookmarks (
  segment_id uuid REFERENCES story_segments(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (segment_id, user_id)
);

-- Create segment_comments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.segment_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  segment_id uuid REFERENCES story_segments(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  content text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE IF EXISTS public.segment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.segment_dislikes ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.segment_bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.segment_comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for segment_likes
CREATE POLICY "任何人都可以查看segment_likes"
  ON public.segment_likes FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "认证用户可以创建segment_likes"
  ON public.segment_likes FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "认证用户可以删除自己的segment_likes"
  ON public.segment_likes FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create RLS policies for segment_dislikes
CREATE POLICY "任何人都可以查看segment_dislikes"
  ON public.segment_dislikes FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "认证用户可以创建segment_dislikes"
  ON public.segment_dislikes FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "认证用户可以删除自己的segment_dislikes"
  ON public.segment_dislikes FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create RLS policies for segment_bookmarks
CREATE POLICY "任何人都可以查看segment_bookmarks"
  ON public.segment_bookmarks FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "认证用户可以创建segment_bookmarks"
  ON public.segment_bookmarks FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "认证用户可以删除自己的segment_bookmarks"
  ON public.segment_bookmarks FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create RLS policies for segment_comments
CREATE POLICY "任何人都可以查看segment_comments"
  ON public.segment_comments FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "认证用户可以创建segment_comments"
  ON public.segment_comments FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "认证用户可以更新自己的segment_comments"
  ON public.segment_comments FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "认证用户可以删除自己的segment_comments"
  ON public.segment_comments FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create functions to update counts
CREATE OR REPLACE FUNCTION update_segment_likes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE story_segments
    SET likes_count = likes_count + 1
    WHERE id = NEW.segment_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE story_segments
    SET likes_count = likes_count - 1
    WHERE id = OLD.segment_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_segment_dislikes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE story_segments
    SET dislikes_count = dislikes_count + 1
    WHERE id = NEW.segment_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE story_segments
    SET dislikes_count = dislikes_count - 1
    WHERE id = OLD.segment_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_segment_bookmarks_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE story_segments
    SET bookmarks_count = bookmarks_count + 1
    WHERE id = NEW.segment_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE story_segments
    SET bookmarks_count = bookmarks_count - 1
    WHERE id = OLD.segment_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_segment_comments_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE story_segments
    SET comment_count = comment_count + 1
    WHERE id = NEW.segment_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE story_segments
    SET comment_count = comment_count - 1
    WHERE id = OLD.segment_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER segment_likes_count_trigger
AFTER INSERT OR DELETE ON segment_likes
FOR EACH ROW
EXECUTE FUNCTION update_segment_likes_count();

CREATE TRIGGER segment_dislikes_count_trigger
AFTER INSERT OR DELETE ON segment_dislikes
FOR EACH ROW
EXECUTE FUNCTION update_segment_dislikes_count();

CREATE TRIGGER segment_bookmarks_count_trigger
AFTER INSERT OR DELETE ON segment_bookmarks
FOR EACH ROW
EXECUTE FUNCTION update_segment_bookmarks_count();

CREATE TRIGGER segment_comments_count_trigger
AFTER INSERT OR DELETE ON segment_comments
FOR EACH ROW
EXECUTE FUNCTION update_segment_comments_count();
