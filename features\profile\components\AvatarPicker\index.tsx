import React, { useState } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Camera, ImageOff, Upload } from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';

interface AvatarPickerProps {
  avatarUrl?: string | null;
  size?: number;
  onImageSelected: (uri: string) => void;
  loading?: boolean;
}

/**
 * AvatarPicker component
 * 
 * A component that displays the current avatar and allows the user to select a new one
 * from the device's media library.
 */
export default function AvatarPicker({
  avatarUrl,
  size = 120,
  onImageSelected,
  loading = false,
}: AvatarPickerProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const [requestingPermission, setRequestingPermission] = useState(false);

  // Request permission and open image picker
  const pickImage = async () => {
    try {
      setRequestingPermission(true);
      
      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          t('permissions.mediaLibraryTitle', 'Permission Required'),
          t(
            'permissions.mediaLibraryMessage',
            'We need access to your media library to select an avatar image.'
          )
        );
        setRequestingPermission(false);
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      setRequestingPermission(false);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      setRequestingPermission(false);
      console.error('Error picking image:', error);
      Alert.alert(
        t('error', 'Error'),
        t(
          'profile.avatarPickerError',
          'There was an error selecting the image. Please try again.'
        )
      );
    }
  };

  // Determine the avatar source
  const avatarSource = avatarUrl
    ? { uri: avatarUrl }
    : require('@/assets/images/default-avatar.png');

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.avatarContainer,
          { width: size, height: size, borderRadius: size / 2 },
        ]}
      >
        <Image
          source={avatarSource}
          style={[
            styles.avatar,
            { width: size, height: size, borderRadius: size / 2 },
          ]}
        />
        
        {loading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator color={theme.colors.onPrimary} size="large" />
          </View>
        )}
      </View>

      <TouchableOpacity
        style={styles.editButton}
        onPress={pickImage}
        disabled={loading || requestingPermission}
      >
        {requestingPermission ? (
          <ActivityIndicator size="small" color={theme.colors.onPrimary} />
        ) : (
          <Camera size={18} color={theme.colors.onPrimary} />
        )}
      </TouchableOpacity>
    </View>
  );
}
