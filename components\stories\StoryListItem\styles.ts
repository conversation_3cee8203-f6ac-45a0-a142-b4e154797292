import { StyleSheet } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

export const createStyles = (theme: ReturnType<typeof useAppTheme>) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      borderRadius: theme.radius.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      overflow: 'hidden',
      alignItems: 'center',
    },
    coverImage: {
      width: 80,
      height: 80,
    },
    content: {
      flex: 1,
      padding: theme.spacing.sm,
      justifyContent: 'space-between',
      minHeight: 80,
    },
    title: {
      fontFamily: theme.fonts.bold,
      fontSize: theme.fontSizes.md,
      marginBottom: theme.spacing.xs,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    statsGroup: {
      flexDirection: 'row',
      gap: theme.spacing.sm,
    },
    stat: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
    },
    themesRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.xs,
      alignItems: 'center',
    },
    themeTag: {
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.radius.sm,
    },
    themeText: {
      fontFamily: theme.fonts.regular,
      fontSize: 10,
    },
    statusTag: {
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.radius.sm,
    },
    statusText: {
      fontFamily: theme.fonts.medium,
      fontSize: 10,
    },
    optionsButton: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.lg,
      justifyContent: 'center',
      alignSelf: 'stretch',
    },
  });
