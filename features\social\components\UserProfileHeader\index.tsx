import React from 'react';
import { View, Text, Image } from 'react-native';
import { User } from '@/types/user';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface UserProfileHeaderProps {
  user: User;
}

export default function UserProfileHeader({ user }: UserProfileHeaderProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.header}>
      <Image source={{ uri: user.avatar }} style={styles.avatar} />

      <View style={styles.nameContainer}>
        <Text style={styles.displayName}>{user.displayName}</Text>
        <Text style={styles.username}>@{user.username}</Text>
      </View>

      {user.isPremium && (
        <View style={styles.premiumBadge}>
          <Text style={styles.premiumText}>
            {t('social.userProfile.premium', 'Premium')}
          </Text>
        </View>
      )}
    </View>
  );
}
