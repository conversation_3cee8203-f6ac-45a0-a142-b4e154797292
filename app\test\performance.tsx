import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/StoryTabs';
import { StoryList } from '@/features/stories/components/StoryList';
import { generateMockStories } from '@/utils/mockData';
import { Story } from '@/api/stories/types';
import { StoryTabKey } from '@/features/stories/components/StoryTabs';

/**
 * Performance test screen
 * This screen tests the performance of the app with different data set sizes
 */
export default function PerformanceTest() {
  const theme = useAppTheme();
  const { t } = useTranslation();
  const router = useRouter();
  
  // State
  const [activeTab, setActiveTab] = useState<StoryTabKey>('drafts');
  const [stories, setStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dataSetSize, setDataSetSize] = useState(10);
  const [renderTime, setRenderTime] = useState(0);
  const [memoryUsage, setMemoryUsage] = useState('N/A');
  
  // Data set size options
  const dataSizes = [10, 50, 100, 200, 500, 1000];
  
  // Load stories with the selected data set size
  const loadStories = useCallback(async () => {
    setIsLoading(true);
    
    // Record start time
    const startTime = performance.now();
    
    // Generate mock stories
    const mockStories = generateMockStories(dataSetSize);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Set stories
    setStories(mockStories);
    
    // Record end time
    const endTime = performance.now();
    setRenderTime(endTime - startTime);
    
    // Try to get memory usage (only works in development)
    try {
      if (global.performance && global.performance.memory) {
        // @ts-ignore - This is a non-standard API
        const memory = global.performance.memory;
        setMemoryUsage(`${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`);
      } else {
        setMemoryUsage('Not available');
      }
    } catch (error) {
      setMemoryUsage('Not available');
    }
    
    setIsLoading(false);
  }, [dataSetSize]);
  
  // Load stories when data set size changes
  useEffect(() => {
    loadStories();
  }, [dataSetSize, loadStories]);
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadStories();
    setIsRefreshing(false);
  };
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    metricsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.md,
    },
    metricCard: {
      flex: 1,
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 8,
      padding: theme.spacing.md,
      marginRight: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    metricTitle: {
      fontSize: 12,
      color: theme.colors.secondaryText,
      marginBottom: theme.spacing.xs,
    },
    metricValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    dataSizeContainer: {
      marginBottom: theme.spacing.md,
    },
    dataSizeTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    dataSizeButtonsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.sm,
    },
    dataSizeButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 8,
      borderWidth: 1,
    },
    dataSizeButtonText: {
      fontSize: 14,
    },
    backButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
    contentContainer: {
      flex: 1,
    },
  });
  
  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Performance Test</Text>
        
        <View style={styles.metricsContainer}>
          <View style={styles.metricCard}>
            <Text style={styles.metricTitle}>Data Set Size</Text>
            <Text style={styles.metricValue}>{dataSetSize} items</Text>
          </View>
          
          <View style={styles.metricCard}>
            <Text style={styles.metricTitle}>Render Time</Text>
            <Text style={styles.metricValue}>{renderTime.toFixed(2)} ms</Text>
          </View>
          
          <View style={styles.metricCard}>
            <Text style={styles.metricTitle}>Memory Usage</Text>
            <Text style={styles.metricValue}>{memoryUsage}</Text>
          </View>
        </View>
        
        <View style={styles.dataSizeContainer}>
          <Text style={styles.dataSizeTitle}>Data Set Size</Text>
          
          <View style={styles.dataSizeButtonsContainer}>
            {dataSizes.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.dataSizeButton,
                  {
                    borderColor: dataSetSize === size ? theme.colors.primary : theme.colors.border,
                    backgroundColor: dataSetSize === size ? `${theme.colors.primary}20` : 'transparent',
                  },
                ]}
                onPress={() => setDataSetSize(size)}
              >
                <Text
                  style={[
                    styles.dataSizeButtonText,
                    {
                      color: dataSetSize === size ? theme.colors.primary : theme.colors.text,
                    },
                  ]}
                >
                  {size} items
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Back to Tests</Text>
        </TouchableOpacity>
      </View>
      
      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
      
      <View style={styles.contentContainer}>
        <StoryList
          stories={stories}
          onStoryPress={handleStoryPress}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          onRefresh={handleRefresh}
          hasMoreStories={false}
        />
      </View>
    </SafeAreaView>
  );
}
