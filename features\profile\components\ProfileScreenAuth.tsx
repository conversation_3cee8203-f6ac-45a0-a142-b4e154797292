import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from '../screens/ProfileScreen.styles';

interface ProfileScreenAuthProps {
  onLogin: () => void;
  onRegister: () => void;
}

export function ProfileScreenAuth({ onLogin, onRegister }: ProfileScreenAuthProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.container_centered}>
      <Text style={styles.authPromptText}>{t('profileAuthPrompt')}</Text>
      <TouchableOpacity style={styles.authButton} onPress={onLogin}>
        <Text style={styles.authButtonText}>{t('loginButton')}</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.authButton, styles.registerButton]}
        onPress={onRegister}
      >
        <Text style={styles.authButtonText}>{t('registerButton')}</Text>
      </TouchableOpacity>
    </View>
  );
}
