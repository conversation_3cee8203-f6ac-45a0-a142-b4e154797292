// AI assistance related types
export interface AiSuggestion {
  id: string;
  content: string;
  type: 'continuation' | 'branch' | 'edit' | 'title';
  confidence: number;
  createdAt: string;
}

export interface AiPrompt {
  storyContext: string;
  userPreferences: AiUserPreferences;
  continuationLength: 'short' | 'medium' | 'long';
  tone: string;
  theme: string[];
  keywords: string[];
}

export interface AiUserPreferences {
  writingStyle: string;
  favoriteGenres: string[];
  contentRating: 'general' | 'teen' | 'mature';
  creativeLevel: 'realistic' | 'creative' | 'experimental';
}

export interface AiBranchOption {
  id: string;
  title: string;
  preview: string;
  tone: string;
  probability: number;
}