import React from 'react';
import { View, Text, Image } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './ProfileHeader.styles';
import { Crown } from 'lucide-react-native'; // Import Crown icon
import { useTranslation } from 'react-i18next'; // Import useTranslation

interface ProfileHeaderProps {
  name: string;
  bio: string;
  avatarUrl?: string; // Optional avatar URL
  isPremium?: boolean; // Add isPremium prop
}

export function ProfileHeader({ name, bio, avatarUrl, isPremium }: ProfileHeaderProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation(); // Get translation function

  return (
    <View style={styles.header}>
      <View style={styles.avatarContainer}>
        <Image
          source={avatarUrl ? { uri: avatarUrl } : require('../../../assets/images/default-avatar.png')} // Use relative path for default avatar
          style={styles.avatar}
        />
        {/* Conditionally render the premium badge */}
        {isPremium && (
          <View style={styles.premiumBadge}>
            <Crown size={12} color="#000" />
            <Text style={styles.premiumText}>{t('premium')}</Text>
          </View>
        )}
      </View>
      <Text style={styles.name}>{name}</Text>
      <Text style={styles.bio}>{bio}</Text>
    </View>
  );
} 