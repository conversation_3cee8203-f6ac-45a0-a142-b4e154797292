import { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { optimizeStoryContent } from '@/api/ai/storyGeneration';

type OptimizationType = 'grammar' | 'style' | 'creativity' | 'conciseness' | 'all';

interface UseStoryOptimizationProps {
  onOptimizedContent: (optimizedContent: string) => void;
}

export function useStoryOptimization({
  onOptimizedContent,
}: UseStoryOptimizationProps) {
  const { t } = useTranslation();
  
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationType, setOptimizationType] = useState<OptimizationType>('all');

  const handleOptimizeContent = async (content: string) => {
    if (!content.trim()) {
      Alert.alert(
        t('storyOptimization.errors.title', '优化错误'),
        t('storyOptimization.errors.emptyContent', '无法优化空内容。')
      );
      return;
    }
    
    setIsOptimizing(true);
    try {
      const response = await optimizeStoryContent({
        content,
        optimizationType,
      });
      
      if (response.error) {
        Alert.alert(
          t('storyOptimization.errors.title', '优化错误'),
          response.error
        );
      } else if (response.optimizedContent) {
        onOptimizedContent(response.optimizedContent);
        // Optional success feedback
        Alert.alert(
          t('storyOptimization.success.title', '优化成功'),
          t('storyOptimization.success.message', '内容已成功优化！')
        );
      }
    } catch (error: any) {
      console.error('Error optimizing content:', error);
      Alert.alert(
        t('storyOptimization.errors.title', '优化错误'),
        error.message ||
          t('storyOptimization.errors.failed', '内容优化失败，请重试。')
      );
    } finally {
      setIsOptimizing(false);
    }
  };

  return {
    isOptimizing,
    optimizationType,
    setOptimizationType,
    handleOptimizeContent,
  };
}
