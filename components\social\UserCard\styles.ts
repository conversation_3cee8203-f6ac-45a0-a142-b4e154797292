import { StyleSheet, Dimensions } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

const { width } = Dimensions.get('window');

// Calculate width for 2 columns with standard medium spacing
// Note: Accessing theme here is tricky, pass spacing value or use approximate value
const approxSpacingMd = 16;
const CARD_WIDTH = (width - approxSpacingMd * 3) / 2;

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      width: CARD_WIDTH,
      padding: theme.spacing.md,
      borderRadius: theme.roundness,
      borderWidth: StyleSheet.hairlineWidth,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
      marginBottom: theme.spacing.md,
      // Add shadow from theme if needed
    },
    topRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
    },
    premiumBadge: {
      position: 'absolute',
      top: -theme.spacing.xs, // Adjust position
      left: 30, // Adjust position
      backgroundColor: '#FFD700',
      padding: 3,
      borderRadius: 8,
      zIndex: 1,
    },
    followButton: {
      marginLeft: 'auto', // Push to the right
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
    },
    followingButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    displayName: {
      fontFamily: theme.fonts.bold,
      fontSize: 14,
      color: theme.colors.text,
    },
    username: {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.secondaryText,
      marginBottom: theme.spacing.sm,
    },
    statsRow: {
      flexDirection: 'row',
      alignItems: 'center',
      borderTopWidth: StyleSheet.hairlineWidth,
      borderTopColor: theme.colors.border,
      paddingTop: theme.spacing.sm,
      marginTop: theme.spacing.xs,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontFamily: theme.fonts.regular,
      fontSize: 12,
      color: theme.colors.secondaryText,
    },
    statDivider: {
      width: StyleSheet.hairlineWidth,
      height: 12, // Adjust height
      backgroundColor: theme.colors.border,
      marginHorizontal: theme.spacing.xs,
    },
  });
