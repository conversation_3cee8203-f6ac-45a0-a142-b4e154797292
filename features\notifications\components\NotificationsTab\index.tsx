import React, { useState } from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Al<PERSON>,
  Sc<PERSON>View,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { useNotifications } from '../../hooks/useNotifications';
import NotificationItem from '../NotificationItem';
import EmptyNotificationsState from '../EmptyNotificationsState';
import { Notification, NotificationType } from '@/api/notifications/types';
import { Check } from 'lucide-react-native';

export default function NotificationsTab() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();
  const [selectedFilter, setSelectedFilter] = useState<NotificationType | null>(
    null
  );

  const {
    notifications,
    isLoading,
    hasM<PERSON>,
    mark<PERSON><PERSON>ead,
    markAllAsRead,
    removeNotification,
    refreshNotifications,
    loadMoreNotifications,
    filterNotifications,
  } = useNotifications({ autoRefresh: true });

  // 处理通知点击
  const handleNotificationPress = (notification: Notification) => {
    // 根据通知类型导航到不同页面
    if (
      notification.type === 'like' ||
      notification.type === 'comment' ||
      notification.type === 'new_segment'
    ) {
      if (notification.story_id) {
        router.push(`/stories/${notification.story_id}`);
      }
    } else if (notification.type === 'follow') {
      if (notification.actor_id) {
        router.push(`/users/${notification.actor_id}`);
      }
    } else if (notification.type === 'new_story') {
      if (notification.story_id) {
        router.push(`/stories/${notification.story_id}`);
      }
    }
  };

  // 处理标记所有为已读
  const handleMarkAllAsRead = () => {
    Alert.alert(
      t('notifications.markAllAsReadTitle', '标记所有为已读'),
      t('notifications.markAllAsReadMessage', '确定要将所有通知标记为已读吗？'),
      [
        {
          text: t('common.cancel', '取消'),
          style: 'cancel',
        },
        {
          text: t('common.confirm', '确定'),
          onPress: () => markAllAsRead(),
        },
      ]
    );
  };

  // 处理筛选
  const handleFilterChange = (type: NotificationType | null) => {
    setSelectedFilter(type);
    filterNotifications(type, null);
  };

  // 渲染筛选按钮
  const renderFilterButton = (type: NotificationType | null, label: string) => {
    const isSelected = selectedFilter === type;
    return (
      <TouchableOpacity
        style={[
          styles.filterButton,
          isSelected && { backgroundColor: theme.colors.primary },
        ]}
        onPress={() => handleFilterChange(type)}
      >
        <Text
          style={[
            styles.filterButtonText,
            isSelected && { color: theme.colors.background },
          ]}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  // 渲染列表头部
  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderFilterButton(null, t('notifications.filters.all', '全部'))}
          {renderFilterButton('like', t('notifications.types.like', '点赞'))}
          {renderFilterButton(
            'comment',
            t('notifications.types.comment', '评论')
          )}
          {renderFilterButton(
            'follow',
            t('notifications.types.follow', '关注')
          )}
          {renderFilterButton(
            'mention',
            t('notifications.types.mention', '提及')
          )}
          {renderFilterButton(
            'new_story',
            t('notifications.types.newStory', '新故事')
          )}
          {renderFilterButton(
            'new_segment',
            t('notifications.types.newSegment', '新段落')
          )}
          {renderFilterButton(
            'system',
            t('notifications.types.system', '系统')
          )}
        </ScrollView>
      </View>

      {notifications.length > 0 && (
        <TouchableOpacity
          style={styles.markAllButton}
          onPress={handleMarkAllAsRead}
        >
          <Check size={16} color={theme.colors.primary} />
          <Text style={styles.markAllButtonText}>
            {t('notifications.markAllAsRead', '全部已读')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  // 渲染列表底部
  const renderFooter = () => {
    if (!isLoading) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    if (isLoading) return null;
    return <EmptyNotificationsState filterType={selectedFilter} />;
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <NotificationItem
            notification={item}
            onPress={handleNotificationPress}
            onMarkAsRead={markAsRead}
            onDelete={removeNotification}
          />
        )}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        onRefresh={refreshNotifications}
        refreshing={isLoading && notifications.length === 0}
        onEndReached={loadMoreNotifications}
        onEndReachedThreshold={0.5}
        contentContainerStyle={
          notifications.length === 0 ? styles.emptyContentContainer : undefined
        }
      />
    </View>
  );
}
