import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    backButton: {
      padding: theme.spacing.xs,
    },
    headerTitle: {
      flex: 1,
      fontSize: 18,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      textAlign: 'center',
      marginHorizontal: theme.spacing.md,
    },
    headerRight: {
      width: 24, // Same width as back button for balance
    },
    commentsContainer: {
      flex: 1,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.xl,
      backgroundColor: theme.colors.background,
    },
    errorText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.error,
      textAlign: 'center',
    },
  });