import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => 
  StyleSheet.create({
    storiesContainer: {
      padding: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
    },
    emptyStoriesContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 32,
      borderWidth: 1,
      borderStyle: 'dashed',
      borderColor: theme.colors.border,
      borderRadius: 8,
    },
    emptyStoriesText: {
      marginTop: 8,
      fontSize: 16,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
  });
