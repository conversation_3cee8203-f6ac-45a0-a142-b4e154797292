import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      marginVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
    },
    currentUserContainer: {
      justifyContent: 'flex-end',
    },
    otherUserContainer: {
      justifyContent: 'flex-start',
    },
    avatarContainer: {
      marginHorizontal: theme.spacing.sm,
    },
    avatar: {
      width: 36,
      height: 36,
      borderRadius: 18,
    },
    placeholderAvatar: {
      backgroundColor: theme.colors.placeholder,
    },
    messageContainer: {
      maxWidth: '70%',
      borderRadius: theme.radius.md,
      padding: theme.spacing.md,
    },
    currentUserMessageContainer: {
      backgroundColor: theme.colors.primary,
      borderBottomRightRadius: 0,
    },
    otherUserMessageContainer: {
      backgroundColor: theme.colors.surface,
      borderBottomLeftRadius: 0,
    },
    messageText: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
    },
    timeText: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
      color: theme.colors.tertiaryText,
      marginTop: theme.spacing.xs,
      alignSelf: 'flex-end',
    },
  });