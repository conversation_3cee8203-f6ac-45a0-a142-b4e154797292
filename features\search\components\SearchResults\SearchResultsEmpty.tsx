import React from 'react';
import { View, Text } from 'react-native';
import { Search } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

interface SearchResultsEmptyProps {
  searchQuery: string;
}

export function SearchResultsEmpty({ searchQuery }: SearchResultsEmptyProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  if (!searchQuery.trim()) {
    return (
      <View style={styles.centerContainer}>
        <Search size={48} color={theme.colors.secondaryText} />
        <Text style={[styles.emptyText, { color: theme.colors.secondaryText }]}>
          {t('social.search.enterQuery', '请输入搜索内容')}
        </Text>
      </View>
    );
  }
  
  return (
    <View style={styles.centerContainer}>
      <Text style={[styles.emptyText, { color: theme.colors.secondaryText }]}>
        {t('social.search.noResults', '没有找到相关结果')}
      </Text>
    </View>
  );
}
