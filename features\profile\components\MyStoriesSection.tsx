import React from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './MyStoriesSection.styles';
import StoryPreviewCard from '@/components/stories/StoryPreviewCard';
import { Story } from '@/types/story';
import { useTranslation } from 'react-i18next';

interface MyStoriesSectionProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  loading?: boolean;
  error?: string | null;
}

export function MyStoriesSection({
  stories,
  onStoryPress,
  loading,
  error,
}: MyStoriesSectionProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  // 渲染单个故事预览项
  const renderStoryItem = ({ item }: { item: Story }) => (
    <StoryPreviewCard story={item} onPress={() => onStoryPress?.(item.id)} />
  );

  return (
    <View style={styles.section}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>
          {t('profile.myStories', 'My Stories')}
        </Text>
      </View>
      {loading && (
        <ActivityIndicator
          size="large"
          color={theme.colors.primary}
          style={styles.loader}
        />
      )}
      {!loading && error && <Text style={styles.placeholderText}>{error}</Text>}
      {!loading && !error && stories && stories.length > 0 ? (
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={stories}
          renderItem={renderStoryItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.scrollViewContent}
          style={styles.scrollView}
        />
      ) : (
        !loading &&
        !error && (
          <Text style={styles.placeholderText}>
            {t('profile.noStoriesYet', "You haven't created any stories yet.")}
          </Text>
        )
      )}
    </View>
  );
}
