import React, { useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Modal, TextInput } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStoryOptimizationBlockStyles } from './styles';
import { Wand2 } from 'lucide-react-native';

type OptimizationType = 'grammar' | 'style' | 'creativity' | 'conciseness' | 'all';

interface StoryOptimizationBlockProps {
  onOptimizeContent: (content: string, type: OptimizationType) => void;
  isOptimizing: boolean;
  disabled?: boolean;
}

export default function StoryOptimizationBlock({
  onOptimizeContent,
  isOptimizing,
  disabled = false,
}: StoryOptimizationBlockProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = useMemo(() => createStoryOptimizationBlockStyles(theme), [theme]);
  
  const [modalVisible, setModalVisible] = useState(false);
  const [contentToOptimize, setContentToOptimize] = useState('');
  const [selectedType, setSelectedType] = useState<OptimizationType>('all');

  const optimizationTypes: { value: OptimizationType; label: string }[] = [
    { value: 'grammar', label: t('storyOptimization.types.grammar', '语法') },
    { value: 'style', label: t('storyOptimization.types.style', '风格') },
    { value: 'creativity', label: t('storyOptimization.types.creativity', '创意') },
    { value: 'conciseness', label: t('storyOptimization.types.conciseness', '简洁') },
    { value: 'all', label: t('storyOptimization.types.all', '全面优化') },
  ];

  const handleOptimize = () => {
    if (contentToOptimize.trim()) {
      onOptimizeContent(contentToOptimize, selectedType);
      setModalVisible(false);
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.optimizeButton}
        onPress={() => setModalVisible(true)}
        disabled={isOptimizing || disabled}
      >
        {isOptimizing ? (
          <ActivityIndicator color={theme.colors.onPrimary} />
        ) : (
          <>
            <Wand2 size={20} color={theme.colors.onPrimary} style={styles.buttonIcon} />
            <Text style={styles.buttonText}>
              {t('storyOptimization.button', 'AI 优化内容')}
            </Text>
          </>
        )}
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {t('storyOptimization.modalTitle', 'AI 内容优化')}
            </Text>
            
            <Text style={styles.inputLabel}>
              {t('storyOptimization.contentLabel', '需要优化的内容:')}
            </Text>
            <TextInput
              style={styles.contentInput}
              multiline
              numberOfLines={6}
              value={contentToOptimize}
              onChangeText={setContentToOptimize}
              placeholder={t('storyOptimization.contentPlaceholder', '请输入需要优化的内容...')}
              placeholderTextColor={theme.colors.placeholder}
            />
            
            <Text style={styles.inputLabel}>
              {t('storyOptimization.typeLabel', '优化类型:')}
            </Text>
            <View style={styles.typeOptions}>
              {optimizationTypes.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.typeOption,
                    selectedType === type.value && styles.selectedTypeOption,
                  ]}
                  onPress={() => setSelectedType(type.value)}
                >
                  <Text
                    style={[
                      styles.typeOptionText,
                      selectedType === type.value && styles.selectedTypeOptionText,
                    ]}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>
                  {t('common.cancel', '取消')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.optimizeModalButton,
                  (!contentToOptimize.trim() || isOptimizing) && styles.disabledButton,
                ]}
                onPress={handleOptimize}
                disabled={!contentToOptimize.trim() || isOptimizing}
              >
                {isOptimizing ? (
                  <ActivityIndicator color={theme.colors.onPrimary} size="small" />
                ) : (
                  <Text style={styles.optimizeModalButtonText}>
                    {t('storyOptimization.optimize', '优化')}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
