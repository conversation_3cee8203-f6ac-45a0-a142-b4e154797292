import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden',
      marginVertical: theme.spacing.md,
    },
    header: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    title: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
    },
    commentsList: {
      padding: theme.spacing.md,
    },
    commentItem: {
      marginBottom: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    commentHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    commentAuthor: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginRight: theme.spacing.sm,
    },
    commentTime: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
      flex: 1,
    },
    deleteButton: {
      padding: theme.spacing.xs,
    },
    commentContent: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      lineHeight: theme.lineHeights.md,
    },
    loadingContainer: {
      padding: theme.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      marginTop: theme.spacing.sm,
    },
    emptyContainer: {
      padding: theme.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emptyText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      marginTop: theme.spacing.sm,
      textAlign: 'center',
    },
    inputContainer: {
      flexDirection: 'row',
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    input: {
      flex: 1,
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: theme.spacing.sm,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
      maxHeight: 100,
    },
    sendButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: theme.spacing.sm,
    },
    disabledButton: {
      opacity: 0.5,
    },
    loginPromptContainer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
      alignItems: 'center',
    },
    loginPromptText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
    },
  });
};
