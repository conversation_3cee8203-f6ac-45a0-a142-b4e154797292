import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, ScrollView, FlatList } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories';

// Hooks
import { useStoryDetails } from '../../hooks/useStoryDetails';
import { useStoryAISuggestions } from '../../hooks/useStoryAISuggestions';
import { useStorySegmentManagement } from '../../hooks/useStorySegmentManagement';
import { useStoryOptimization } from '../../hooks/useStoryOptimization';

// Components
import StoryDetailHeader from '../../components/StoryDetailHeader';
import StorySegmentItem from '../../components/StorySegmentItem';
import AddSegmentForm from '../../components/AddSegmentForm';
import AISuggestionBlock from '../../components/AISuggestionBlock';
import StoryOptimizationBlock from '../../components/StoryOptimizationBlock';
import { LoadingState } from './LoadingState';
import { ErrorState } from './ErrorState';
import { EmptyState } from './EmptyState';
import { createDynamicStyles } from '../StoryDetailScreen.styles';

interface StoryDetailScreenProps {
  storyId: string;
}

export default function StoryDetailScreen({ storyId }: StoryDetailScreenProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = useMemo(() => createDynamicStyles(theme), [theme]);

  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    isLiked,
    likeCount,
    isLiking,
    fetchStoryDetails,
    handleLikeToggle,
  } = useStoryDetails(storyId);

  const [segments, setSegments] = useState<StorySegment[]>([]);

  useEffect(() => {
    if (story?.story_segments) {
      setSegments(story.story_segments);
    }
  }, [story?.story_segments]);

  const handleSegmentAdded = useCallback((newSegment: StorySegment) => {
    setSegments((prevSegments) => [...prevSegments, newSegment]);
  }, []);

  const {
    newSegmentContent,
    setNewSegmentContent,
    isSubmittingSegment,
    handleAddSegment,
  } = useStorySegmentManagement({
    storyId,
    onSegmentAdded: handleSegmentAdded,
  });

  const {
    aiSegmentSuggestions,
    loadingAISegmentSuggestions,
    showAISegmentSuggestions,
    currentSegmentIsAI,
    setCurrentSegmentIsAI,
    handleFetchAISuggestionsForSegment,
    handleSelectAISuggestion,
  } = useStoryAISuggestions(story);

  const {
    isOptimizing,
    optimizationType,
    setOptimizationType,
    handleOptimizeContent,
  } = useStoryOptimization({
    onOptimizedContent: (optimizedContent) => {
      setNewSegmentContent(optimizedContent);
      setCurrentSegmentIsAI(true); // Mark as AI-assisted
    },
  });

  const onSelectAISuggestionForForm = (suggestion: string) => {
    handleSelectAISuggestion(suggestion, setNewSegmentContent);
  };

  const submitSegment = () => {
    handleAddSegment(currentSegmentIsAI);
    setCurrentSegmentIsAI(false); // Reset AI flag
  };

  const handleOptimize = (content: string, type: any) => {
    setOptimizationType(type);
    handleOptimizeContent(content);
  };

  if (isLoadingDetails) {
    return <LoadingState />;
  }

  if (storyError) {
    return <ErrorState errorMessage={storyError} onRetry={fetchStoryDetails} />;
  }

  if (!story) {
    return <EmptyState />;
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      keyboardShouldPersistTaps="handled"
    >
      <StoryDetailHeader
        story={story}
        isLiked={isLiked}
        likeCount={likeCount}
        onLikeToggle={handleLikeToggle}
        isLiking={isLiking}
      />

      <View style={styles.segmentsSection}>
        <Text style={styles.sectionTitle}>
          {t('storyDetail.storyContent', 'Story Content')}
        </Text>
        {segments && segments.length > 0 ? (
          <FlatList
            data={segments}
            renderItem={({ item, index }) => (
              <StorySegmentItem
                segment={item}
                isFirst={index === 0}
                isLast={index === segments.length - 1}
                showDivider={index > 0}
              />
            )}
            keyExtractor={(item) => item.id.toString()}
            scrollEnabled={false} // Important if inside a ScrollView
          />
        ) : (
          <Text style={styles.noSegmentsText}>
            {t(
              'storyDetail.noContent',
              'This story has no content yet. Be the first to add to it!'
            )}
          </Text>
        )}
      </View>

      <AddSegmentForm
        segmentContent={newSegmentContent}
        onContentChange={setNewSegmentContent}
        onSubmit={submitSegment}
        isSubmitting={isSubmittingSegment}
      />

      <AISuggestionBlock
        onFetchSuggestions={handleFetchAISuggestionsForSegment}
        loadingSuggestions={loadingAISegmentSuggestions}
        showSuggestions={showAISegmentSuggestions}
        suggestions={aiSegmentSuggestions}
        onSelectSuggestion={onSelectAISuggestionForForm}
        isProcessingSegment={isSubmittingSegment}
      />

      <StoryOptimizationBlock
        onOptimizeContent={handleOptimize}
        isOptimizing={isOptimizing}
        disabled={isSubmittingSegment}
      />
    </ScrollView>
  );
}
