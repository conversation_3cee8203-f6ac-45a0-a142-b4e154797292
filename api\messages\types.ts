import { Profile } from '../profiles';

export interface Conversation {
  id: string;
  created_at: string;
  updated_at: string;
  last_message_at: string;
  last_message: string | null;
  
  // 嵌入的关联数据
  participants?: ConversationParticipant[];
  other_participant?: Profile; // 在一对一对话中，另一个参与者
  unread_count?: number; // 未读消息数量
}

export interface ConversationParticipant {
  id: string;
  conversation_id: string;
  user_id: string;
  created_at: string;
  last_read_at: string;
  
  // 嵌入的关联数据
  user?: Profile;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  is_read: boolean;
  
  // 嵌入的关联数据
  sender?: Profile;
}

export interface GetConversationsOptions {
  limit?: number;
  offset?: number;
}

export interface GetMessagesOptions {
  limit?: number;
  offset?: number;
}

export interface CreateConversationOptions {
  participant_ids: string[]; // 参与者ID列表（不包括当前用户）
  initial_message?: string; // 初始消息
}

export interface SendMessageOptions {
  conversation_id: string;
  content: string;
}