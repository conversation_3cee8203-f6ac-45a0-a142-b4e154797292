import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { Send, Trash2, MessageCircle } from 'lucide-react-native';
import { BranchComment } from '@/api/stories';
import { useAuthStore } from '@/lib/store/authStore';
import { formatDistanceToNow } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { useLocale } from '@/hooks/useLocale';

interface BranchCommentsProps {
  comments: BranchComment[];
  isLoading: boolean;
  isAddingComment: boolean;
  onAddComment: (content: string) => Promise<BranchComment | null>;
  onDeleteComment: (commentId: string) => Promise<boolean>;
  onRefresh: () => void;
}

export default function BranchComments({
  comments,
  isLoading,
  isAddingComment,
  onAddComment,
  onDeleteComment,
  onRefresh,
}: BranchCommentsProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { user } = useAuthStore();
  const { locale } = useLocale();

  const [commentText, setCommentText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 处理添加评论
  const handleAddComment = async () => {
    if (!commentText.trim()) return;

    const result = await onAddComment(commentText);
    if (result) {
      setCommentText('');
    }
  };

  // 处理删除评论
  const handleDeleteComment = (commentId: string) => {
    Alert.alert(
      t('storyDetail.deleteCommentTitle', 'Delete Comment'),
      t(
        'storyDetail.deleteCommentConfirm',
        'Are you sure you want to delete this comment?'
      ),
      [
        {
          text: t('cancel', 'Cancel'),
          style: 'cancel',
        },
        {
          text: t('delete', 'Delete'),
          style: 'destructive',
          onPress: async () => {
            await onDeleteComment(commentId);
          },
        },
      ]
    );
  };

  // 处理刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: locale === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // 渲染评论项
  const renderCommentItem = ({ item }: { item: BranchComment }) => {
    const isAuthor = user?.id === item.user_id;

    return (
      <View style={styles.commentItem}>
        <View style={styles.commentHeader}>
          <Text style={styles.commentAuthor}>
            {item.profiles?.username ||
              t('storyDetail.unknownAuthor', 'Unknown')}
          </Text>
          <Text style={styles.commentTime}>{formatTime(item.created_at)}</Text>
          {isAuthor && (
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteComment(item.id)}
            >
              <Trash2 size={16} color={theme.colors.error} />
            </TouchableOpacity>
          )}
        </View>
        <Text style={styles.commentContent}>{item.content}</Text>
      </View>
    );
  };

  // 渲染评论列表
  const renderCommentList = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={styles.loadingText}>
            {t('storyDetail.loadingComments', 'Loading comments...')}
          </Text>
        </View>
      );
    }

    if (comments.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <MessageCircle size={24} color={theme.colors.secondaryText} />
          <Text style={styles.emptyText}>
            {t(
              'storyDetail.noComments',
              'No comments yet. Be the first to comment!'
            )}
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={comments}
        renderItem={renderCommentItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.commentsList}
        onRefresh={handleRefresh}
        refreshing={refreshing}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {t('storyDetail.comments', 'Comments')} ({comments.length})
        </Text>
      </View>

      {renderCommentList()}

      {user && (
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder={t(
              'storyDetail.addCommentPlaceholder',
              'Add a comment...'
            )}
            placeholderTextColor={theme.colors.placeholder}
            value={commentText}
            onChangeText={setCommentText}
            multiline
            maxLength={500}
            editable={!isAddingComment}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!commentText.trim() || isAddingComment) && styles.disabledButton,
            ]}
            onPress={handleAddComment}
            disabled={!commentText.trim() || isAddingComment}
          >
            {isAddingComment ? (
              <ActivityIndicator size="small" color={theme.colors.onPrimary} />
            ) : (
              <Send size={20} color={theme.colors.onPrimary} />
            )}
          </TouchableOpacity>
        </View>
      )}

      {!user && (
        <View style={styles.loginPromptContainer}>
          <Text style={styles.loginPromptText}>
            {t('storyDetail.loginToComment', 'Please log in to add a comment')}
          </Text>
        </View>
      )}
    </View>
  );
}
