import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles for the shared OptionsGroup component
export const createStyles = (theme: AppTheme) => StyleSheet.create({
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.roundness * 2,
    padding: theme.spacing.xs,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.border,
    alignSelf: 'stretch',
  },
  optionButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.roundness,
    alignItems: 'center',
  },
  optionButtonSelected: {
    backgroundColor: theme.colors.primary,
  },
  optionText: {
    color: theme.colors.text,
    fontFamily: theme.fonts.medium,
  },
  optionTextSelected: {
    color: theme.dark ? '#000' : '#FFF',
  },
});
