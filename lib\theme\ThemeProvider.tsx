import React, { createContext, useMemo } from 'react';
import { useColorScheme } from 'react-native';
import { PaperProvider, adaptNavigationTheme } from 'react-native-paper';
import {
  DarkTheme as NavigationDarkTheme,
  DefaultTheme as NavigationDefaultTheme,
  ThemeProvider as NavigationThemeProvider,
} from '@react-navigation/native';
import merge from 'deepmerge';
// Update store and theme import paths
import { useSettingsStore } from '@/lib/store/settingsStore';
import { lightTheme, darkTheme, AppTheme } from './themes';
import { paperLightTheme, paperDarkTheme } from './paperThemes';

interface ThemeContextProps {
  theme: AppTheme;
}

export const ThemeContext = createContext<ThemeContextProps>({
  theme: lightTheme, // Default theme
});

// 适配导航主题
const { LightTheme, DarkTheme } = adaptNavigationTheme({
  reactNavigationLight: NavigationDefaultTheme,
  reactNavigationDark: NavigationDarkTheme,
});

// 合并 Paper 主题和导航主题
const CombinedLightTheme = merge(LightTheme, paperLightTheme);
const CombinedDarkTheme = merge(DarkTheme, paperDarkTheme);

export const ThemeProvider: React.FC<React.PropsWithChildren<{}>> = ({
  children,
}) => {
  const themeMode = useSettingsStore((state) => state.themeMode);
  const systemColorScheme = useSettingsStore(
    (state) => state.systemColorScheme
  );
  const hasHydrated = useSettingsStore((state) => state._hasHydrated);
  const deviceColorScheme = useColorScheme();

  // 计算当前主题模式
  const currentMode = useMemo(() => {
    if (!hasHydrated) {
      return deviceColorScheme || 'light'; // 使用设备主题或默认亮色模式
    }
    return themeMode === 'system'
      ? systemColorScheme || deviceColorScheme
      : themeMode;
  }, [themeMode, systemColorScheme, hasHydrated, deviceColorScheme]);

  // 自定义主题（用于现有组件）
  const activeTheme = useMemo(() => {
    return currentMode === 'dark' ? darkTheme : lightTheme;
  }, [currentMode]);

  // 合并后的主题（Paper + Navigation）
  const combinedTheme = useMemo(() => {
    return currentMode === 'dark' ? CombinedDarkTheme : CombinedLightTheme;
  }, [currentMode]);

  return (
    <ThemeContext.Provider value={{ theme: activeTheme }}>
      <PaperProvider theme={combinedTheme}>
        <NavigationThemeProvider value={combinedTheme}>
          {children}
        </NavigationThemeProvider>
      </PaperProvider>
    </ThemeContext.Provider>
  );
};
