import React from 'react';
import { View } from 'react-native';
import { Card, Button } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Story as ApiStory } from '@/api/stories';
import { FeaturedStoryCardHeader } from './FeaturedStoryCardHeader';
import { FeaturedStoryCardStats } from './FeaturedStoryCardStats';
import { FeaturedStoryCardContent } from './FeaturedStoryCardContent';

interface FeaturedStoryCardProps {
  story: ApiStory;
  onPress?: () => void;
}

export default function FeaturedStoryCard({
  story,
  onPress,
}: FeaturedStoryCardProps) {
  const theme = usePaperTheme();

  const coverImageUrl = story.cover_image_url;
  const isPremium = false;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const summary = story.first_segment_content || '';
  const themeTags = story.tags || [];

  return (
    <Card
      mode="elevated"
      onPress={onPress}
      style={{
        marginBottom: theme.spacing?.lg || 16,
        overflow: 'hidden',
      }}
    >
      <Card.Cover
        source={
          coverImageUrl
            ? { uri: coverImageUrl }
            : require('../../../assets/images/default-story-placeholder.png')
        }
        style={{ height: 200 }}
      />

      {/* Overlay content */}
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          padding: theme.spacing?.md || 16,
          justifyContent: 'space-between',
        }}
      >
        <FeaturedStoryCardHeader title={story.title} isPremium={isPremium} />

        <View>
          <FeaturedStoryCardStats
            authorName={authorName}
            views={views}
            likes={likes}
          />

          <FeaturedStoryCardContent summary={summary} themeTags={themeTags} />

          <Button
            mode="contained-tonal"
            onPress={onPress}
            style={{ marginTop: theme.spacing?.sm || 8 }}
          >
            开始阅读
          </Button>
        </View>
      </View>
    </Card>
  );
}
