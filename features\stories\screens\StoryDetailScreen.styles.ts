import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes'; // 确保 AppTheme 类型和路径正确

// Styles for the main StoryDetailScreen container and overall layout
export const createDynamicStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    contentContainer: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl, // Extra padding at bottom for scroll
    },
    centered: {
      // For loading/error states
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.background,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: theme.fontSizes.md,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    // Button style for 'Try Again'
    button: {
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.radius.md,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 44,
    },
    buttonText: {
      color: theme.colors.onPrimary, // 确保 onPrimary 在 AppTheme 中定义
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
    },
    // Styles related to the list of segments itself
    branchManagerContainer: {
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderRadius: theme.radius.md,
      overflow: 'hidden',
    },
    segmentsSection: {
      marginTop: theme.spacing.lg, // Space after header
      marginBottom: theme.spacing.lg, // Space before add segment form
    },
    sectionTitle: {
      // Generic section title (e.g., "Story Content")
      fontSize: theme.fontSizes.lg, // 确保 lg 在 AppTheme 中定义
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    noSegmentsText: {
      // Text shown if there are no segments
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText, // 确保 secondaryText 在 AppTheme 中定义
      textAlign: 'center',
      paddingVertical: theme.spacing.lg,
      fontStyle: 'italic',
    },
  });
};
