import React from 'react';
import { View, Text, TouchableOpacity, Image, Pressable } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  MessageCircle,
  Heart,
  Bookmark,
  MoreHorizontal,
  GitBranch,
  ThumbsUp,
  ThumbsDown,
} from 'lucide-react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { StorySegment } from '@/api/stories/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useI18n } from '@/hooks/useI18n';
import { getChildrenCount } from '@/utils/storyHelpers';

interface StoryNodeCardProps {
  segment: StorySegment;
  isOriginStory?: boolean;
  onBranchPress: () => void;
  hasBranches?: boolean;
}

export default function StoryNodeCard({
  segment,
  isOriginStory = false,
  onBranchPress,
  hasBranches: propHasBranches,
}: StoryNodeCardProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { currentLanguage } = useI18n();

  // Format the relative time (e.g., "2h", "1d")
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const locale = currentLanguage === 'zh' ? zhCN : enUS;
      return formatDistanceToNow(date, { locale, addSuffix: false });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // 使用工具函数获取子分支数量
  const childrenCount = getChildrenCount(segment.children_count);

  // 如果传入了 hasBranches 属性，则使用该属性，否则根据子分支数量决定
  const hasBranches =
    propHasBranches !== undefined ? propHasBranches : childrenCount > 0;

  // 用于显示的子分支数量，使用实际的子分支数量
  const displayChildrenCount = childrenCount;

  console.log(
    'StoryNodeCard - segment:',
    segment.id,
    'parent_segment_id:',
    segment.parent_segment_id,
    'children_count:',
    displayChildrenCount,
    'hasBranches:',
    hasBranches,
    'content:',
    segment.content.substring(0, 30)
  );

  return (
    <View
      style={[styles.container, isOriginStory && styles.originStoryContainer]}
    >
      {/* Creator Info Row */}
      <View style={styles.creatorInfoRow}>
        <View style={styles.creatorInfo}>
          <Image
            source={
              segment.profiles?.avatar_url
                ? { uri: segment.profiles.avatar_url }
                : require('@/assets/images/default-avatar.png')
            }
            style={styles.avatar}
          />
          <Text style={styles.username} numberOfLines={1}>
            {segment.profiles?.username || t('common.unknownUser', '未知用户')}
          </Text>
          <Text style={styles.handle} numberOfLines={1}>
            @{segment.profiles?.username || 'username'}
          </Text>
          <Text style={styles.timeAgo}>
            {formatRelativeTime(segment.created_at)}
          </Text>
        </View>

        <TouchableOpacity style={styles.moreButton}>
          <MoreHorizontal size={20} color={theme.colors.secondaryText} />
        </TouchableOpacity>
      </View>

      {/* Story Content */}
      <Text style={styles.content}>{segment.content}</Text>

      {/* Interaction Row */}
      <View style={styles.interactionRow}>
        <View style={styles.interactionIcons}>
          <TouchableOpacity style={styles.iconButton}>
            <MessageCircle size={16} color={theme.colors.secondaryText} />
            <Text style={styles.iconText}>0</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.iconButton}>
            <ThumbsUp size={16} color={theme.colors.secondaryText} />
            <Text style={styles.iconText}>0</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.iconButton}>
            <ThumbsDown size={16} color={theme.colors.secondaryText} />
            <Text style={styles.iconText}>0</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.iconButton}>
            <Bookmark size={16} color={theme.colors.secondaryText} />
            <Text style={styles.iconText}>0</Text>
          </TouchableOpacity>
        </View>

        {hasBranches ? (
          <TouchableOpacity
            style={[
              styles.branchIndicator,
              isOriginStory && styles.originBranchIndicator,
            ]}
            onPress={onBranchPress}
            accessibilityLabel={t('storyDetail.viewBranches', 'View branches')}
          >
            <GitBranch size={14} color={theme.colors.background} />
            <Text
              style={[
                styles.branchCount,
                isOriginStory && styles.originBranchCount,
              ]}
            >
              {displayChildrenCount}
            </Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.branchPlaceholder} />
        )}
      </View>
    </View>
  );
}
