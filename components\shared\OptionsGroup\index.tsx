import React from 'react';
import { View, Text, TouchableOpacity, ViewStyle, StyleProp } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';

// Define the structure for each option
export interface Option<T> {
  value: T;
  label: string;
}

interface OptionsGroupProps<T> {
  options: Option<T>[];
  selectedValue: T;
  onSelect: (value: T) => void;
  style?: StyleProp<ViewStyle>; // Optional style prop for the container
}

export function OptionsGroup<T>({ options, selectedValue, onSelect, style }: OptionsGroupProps<T>) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={[styles.optionsContainer, style]}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.label} // Use label as key assuming labels are unique for a group
          style={[
            styles.optionButton,
            selectedValue === option.value && styles.optionButtonSelected,
          ]}
          onPress={() => onSelect(option.value)}
        >
          <Text
            style={[
              styles.optionText,
              selectedValue === option.value && styles.optionTextSelected,
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}
