import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

/**
 * Creates styles for the AvatarPicker component based on the current theme
 * 
 * @param theme The current app theme
 * @returns StyleSheet object with themed styles
 */
export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: theme.spacing.md,
      position: 'relative',
    },
    avatarContainer: {
      position: 'relative',
      overflow: 'hidden',
      borderWidth: 3,
      borderColor: theme.colors.primary,
    },
    avatar: {
      width: '100%',
      height: '100%',
    },
    editButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      backgroundColor: theme.colors.primary,
      width: 36,
      height: 36,
      borderRadius: 18,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: theme.colors.background,
    },
    loadingOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      alignItems: 'center',
      justifyContent: 'center',
    },
  });
};
