import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden',
      marginVertical: theme.spacing.md,
    },
    navigatorSection: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    sectionTitle: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    breadcrumbContainer: {
      flexDirection: 'row',
    },
    breadcrumbContent: {
      alignItems: 'center',
      paddingVertical: theme.spacing.xs,
    },
    breadcrumbItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.radius.sm,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginRight: theme.spacing.xs,
    },
    breadcrumbItemActive: {
      backgroundColor: theme.colors.primaryLight,
      borderColor: theme.colors.primary,
    },
    breadcrumbText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginLeft: 4,
      maxWidth: 120,
    },
    breadcrumbTextActive: {
      color: theme.colors.primary,
      fontFamily: theme.fonts.bold,
    },
    breadcrumbSeparator: {
      marginHorizontal: 4,
    },
    childrenListContent: {
      paddingVertical: theme.spacing.sm,
    },
    childBranchItem: {
      width: 200,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: theme.spacing.sm,
      marginRight: theme.spacing.md,
    },
    childBranchHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.xs,
    },
    childBranchTitle: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginLeft: 4,
    },
    childBranchPreview: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    childBranchFooter: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: theme.spacing.xs,
    },
    childBranchAuthor: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
    },
    emptyChildrenContainer: {
      padding: theme.spacing.md,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    emptyChildrenText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
    loadingMoreContainer: {
      padding: theme.spacing.md,
      justifyContent: 'center',
      alignItems: 'center',
      width: 150,
    },
    loadingMoreText: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.medium,
      color: theme.colors.secondaryText,
      marginTop: theme.spacing.xs,
      textAlign: 'center',
    },
    loadMoreButton: {
      padding: theme.spacing.md,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.radius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      width: 120,
      marginHorizontal: theme.spacing.sm,
    },
    loadMoreButtonText: {
      fontSize: theme.fontSizes.sm,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
  });
};
