import { Notification } from '@/api/notifications/types';
import { mockUsers } from '../users';
import { mockStories } from '../stories';

// 生成随机日期（过去7天内）
const getRandomDate = () => {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 7); // 0-7天前
  const hoursAgo = Math.floor(Math.random() * 24); // 0-24小时前
  const minutesAgo = Math.floor(Math.random() * 60); // 0-60分钟前

  now.setDate(now.getDate() - daysAgo);
  now.setHours(now.getHours() - hoursAgo);
  now.setMinutes(now.getMinutes() - minutesAgo);

  return now.toISOString();
};

// 创建一个简单的通知数组，避免依赖可能不存在的属性
export const mockNotifications: Notification[] = [
  // 点赞通知
  {
    id: '1',
    user_id: 'user1',
    type: 'like',
    title: '收到新的点赞',
    body: '有人点赞了你的故事',
    is_read: false,
    created_at: getRandomDate(),
    actor_id: 'user2',
    story_id: '1',
    actor: {
      id: 'user2',
      username: 'user2',
      avatar_url: null,
    },
    data: {
      story_id: '1',
    },
  },
  // 评论通知
  {
    id: '2',
    user_id: 'user1',
    type: 'comment',
    title: '收到新的评论',
    body: '有人评论了你的故事: "这个故事真的很棒！"',
    is_read: true,
    created_at: getRandomDate(),
    actor_id: 'user3',
    story_id: '2',
    actor: {
      id: 'user3',
      username: 'user3',
      avatar_url: null,
    },
    data: {
      story_id: '2',
      comment_id: 'comment-1',
    },
  },
  // 关注通知
  {
    id: '3',
    user_id: 'user1',
    type: 'follow',
    title: '新的关注者',
    body: '有人关注了你',
    is_read: false,
    created_at: getRandomDate(),
    actor_id: 'user4',
    actor: {
      id: 'user4',
      username: 'user4',
      avatar_url: null,
    },
  },
  // 系统通知
  {
    id: '7',
    user_id: 'user1',
    type: 'system',
    title: '系统通知',
    body: '欢迎使用 SupaPose！探索更多精彩故事，开始你的创作之旅。',
    is_read: true,
    created_at: getRandomDate(),
  },
];
