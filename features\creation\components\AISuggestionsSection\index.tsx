import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import AiSuggestionCard from '@/components/creation/AiSuggestionCard';

interface AISuggestionsSectionProps {
  onFetchSuggestions: () => void;
  loadingSuggestions: boolean;
  showSuggestions: boolean;
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  disabled?: boolean;
}

export default function AISuggestionsSection({
  onFetchSuggestions,
  loadingSuggestions,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  disabled = false,
}: AISuggestionsSectionProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <View style={{ marginBottom: theme.spacing?.lg || 24 }}>
      <Button
        mode="contained"
        onPress={onFetchSuggestions}
        disabled={loadingSuggestions || disabled}
        loading={loadingSuggestions}
        buttonColor={(theme.colors as any).aiAction}
        textColor={(theme.colors as any).onAiAction}
        style={{
          paddingVertical: theme.spacing?.sm || 8,
          marginBottom: theme.spacing?.md || 16,
        }}
        labelStyle={{ fontSize: 16 }}
      >
        {t('storyForm.getAISuggestions', '获取 AI 建议')}
      </Button>

      {showSuggestions && loadingSuggestions && (
        <ActivityIndicator
          size="large"
          color={theme.colors.primary}
          style={{ marginTop: theme.spacing?.md || 16 }}
        />
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length > 0 && (
        <View style={{ marginTop: theme.spacing?.md || 16 }}>
          <Text
            variant="titleMedium"
            style={{ marginBottom: theme.spacing?.sm || 8 }}
          >
            {t('aiSuggestions.title', 'AI 建议:')}
          </Text>
          {suggestions.map((suggestion, index) => (
            <AiSuggestionCard
              key={index}
              suggestion={suggestion}
              onSelect={onSelectSuggestion}
            />
          ))}
        </View>
      )}
      {showSuggestions && !loadingSuggestions && suggestions.length === 0 && (
        <Text
          variant="bodyMedium"
          style={{
            marginTop: theme.spacing?.md || 16,
            textAlign: 'center',
            color: theme.colors.onSurfaceVariant,
          }}
        >
          {t(
            'aiSuggestions.noSuggestions',
            '暂时没有合适的建议，尝试修改你的输入或稍后再试。'
          )}
        </Text>
      )}
    </View>
  );
}
