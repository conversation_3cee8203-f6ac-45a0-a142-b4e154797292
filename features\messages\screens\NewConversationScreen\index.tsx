import React, { useState, useEffect } from 'react';
import { View, FlatList, TextInput, TouchableOpacity, Text, Image, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { Stack, useRouter } from 'expo-router';
import { Search, X, Check } from 'lucide-react-native';
import { mockUsers } from '@/utils/mockData/users';
import { useConversations } from '../../hooks/useConversations';

export default function NewConversationScreen() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState(mockUsers);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const { startConversation } = useConversations();

  // 过滤用户
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredUsers(mockUsers);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = mockUsers.filter(
        (user) =>
          user.displayName.toLowerCase().includes(query) ||
          user.username.toLowerCase().includes(query)
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery]);

  // 处理用户选择
  const handleUserSelect = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  // 处理创建对话
  const handleCreateConversation = async () => {
    if (selectedUsers.length === 0 || isCreating) {
      return;
    }

    setIsCreating(true);
    try {
      const conversation = await startConversation({
        participant_ids: selectedUsers,
      });

      if (conversation) {
        router.replace(`/messages/${conversation.id}`);
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // 渲染用户项
  const renderUserItem = ({ item }: { item: typeof mockUsers[0] }) => {
    const isSelected = selectedUsers.includes(item.id);
    return (
      <TouchableOpacity
        style={[styles.userItem, isSelected && styles.selectedUserItem]}
        onPress={() => handleUserSelect(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.userInfo}>
          {item.avatar ? (
            <Image source={{ uri: item.avatar }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatar, styles.placeholderAvatar]} />
          )}
          <View style={styles.userTextContainer}>
            <Text style={styles.displayName}>{item.displayName}</Text>
            <Text style={styles.username}>@{item.username}</Text>
          </View>
        </View>

        <View
          style={[
            styles.checkCircle,
            isSelected && styles.selectedCheckCircle,
          ]}
        >
          {isSelected && <Check size={16} color={theme.colors.background} />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: t('messages.newConversation', '新建对话'),
          headerShown: true,
          headerRight: () => (
            <TouchableOpacity
              style={[
                styles.createButton,
                (selectedUsers.length === 0 || isCreating) && styles.disabledCreateButton,
              ]}
              onPress={handleCreateConversation}
              disabled={selectedUsers.length === 0 || isCreating}
            >
              {isCreating ? (
                <ActivityIndicator size="small" color={theme.colors.background} />
              ) : (
                <Text style={styles.createButtonText}>
                  {t('messages.create', '创建')}
                </Text>
              )}
            </TouchableOpacity>
          ),
        }}
      />

      <View style={styles.searchContainer}>
        <Search size={18} color={theme.colors.secondaryText} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={t('messages.searchUsers', '搜索用户...')}
          placeholderTextColor={theme.colors.placeholder}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoFocus
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => setSearchQuery('')}
          >
            <X size={16} color={theme.colors.secondaryText} />
          </TouchableOpacity>
        )}
      </View>

      {selectedUsers.length > 0 && (
        <View style={styles.selectedUsersContainer}>
          <Text style={styles.selectedUsersTitle}>
            {t('messages.selectedUsers', '已选择的用户')}
            {' ('}
            {selectedUsers.length}
            {')'}
          </Text>
          <FlatList
            data={mockUsers.filter((user) => selectedUsers.includes(user.id))}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.selectedUserChip}>
                <Text style={styles.selectedUserName}>{item.displayName}</Text>
                <TouchableOpacity
                  style={styles.removeUserButton}
                  onPress={() => handleUserSelect(item.id)}
                >
                  <X size={12} color={theme.colors.background} />
                </TouchableOpacity>
              </View>
            )}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.selectedUsersList}
          />
        </View>
      )}

      <FlatList
        data={filteredUsers}
        keyExtractor={(item) => item.id}
        renderItem={renderUserItem}
        contentContainerStyle={styles.usersList}
      />
    </View>
  );
}