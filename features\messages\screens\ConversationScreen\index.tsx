import React, { useEffect, useState } from 'react';
import { View, FlatList, ActivityIndicator, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import { useLocalSearchParams, Stack } from 'expo-router';
import { useMessages } from '../../hooks/useMessages';
import { getConversationById, markConversationAsRead } from '@/api/messages';
import MessageItem from '../../components/MessageItem';
import MessageInput from '../../components/MessageInput';
import { Message } from '@/api/messages/types';

export default function ConversationScreen() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [title, setTitle] = useState('');
  const [isLoadingConversation, setIsLoadingConversation] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const {
    messages,
    isLoading: isLoadingMessages,
    sendMessage,
    refreshMessages,
    loadMoreMessages,
  } = useMessages({
    conversationId: id || '',
    autoRefresh: true,
    refreshInterval: 10000, // 10秒
  });

  // 获取对话详情
  useEffect(() => {
    const fetchConversation = async () => {
      if (!id) return;

      setIsLoadingConversation(true);
      setError(null);

      try {
        const { data, error } = await getConversationById(id);
        if (error) {
          throw new Error(error.message);
        }
        if (data) {
          setTitle(
            data.other_participant?.display_name ||
              data.other_participant?.username ||
              t('messages.unknownUser', '未知用户')
          );

          // 标记对话为已读
          if (data.unread_count && data.unread_count > 0) {
            await markConversationAsRead(id);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch conversation'));
      } finally {
        setIsLoadingConversation(false);
      }
    };

    fetchConversation();
  }, [id, t]);

  // 处理发送消息
  const handleSendMessage = async (content: string) => {
    if (!id) return;
    await sendMessage(content);
  };

  // 渲染列表底部
  const renderFooter = () => {
    if (!isLoadingMessages) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    if (isLoadingMessages) return null;
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          {t('messages.noMessagesYet', '暂无消息，开始对话吧')}
        </Text>
      </View>
    );
  };

  // 渲染消息项
  const renderMessageItem = ({ item }: { item: Message }) => {
    const isCurrentUser = item.sender_id === 'current-user-id'; // 假设当前用户ID
    return <MessageItem message={item} isCurrentUser={isCurrentUser} />;
  };

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: isLoadingConversation ? t('messages.loading', '加载中...') : title,
          headerShown: true,
        }}
      />

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {error.message || t('messages.errorLoading', '加载失败')}
          </Text>
        </View>
      ) : (
        <>
          <FlatList
            data={messages}
            keyExtractor={(item) => item.id}
            renderItem={renderMessageItem}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmpty}
            onRefresh={refreshMessages}
            refreshing={isLoadingMessages && messages.length === 0}
            onEndReached={loadMoreMessages}
            onEndReachedThreshold={0.5}
            inverted // 倒序显示，最新的消息在底部
            contentContainerStyle={styles.listContent}
          />

          <MessageInput onSend={handleSendMessage} />
        </>
      )}
    </View>
  );
}