import React from 'react';
import { View } from 'react-native';
import { TextInput, Button, Text, HelperText } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface CreateStoryFormProps {
  title: string;
  onTitleChange: (text: string) => void;
  initialContent: string;
  onContentChange: (text: string) => void;
  contentFocused: boolean;
  onContentFocus: () => void;
  onContentBlur: () => void;
  isFormValid: boolean;
  submitting: boolean;
  onSubmit: () => void;
}

export default function CreateStoryForm({
  title,
  onTitleChange,
  initialContent,
  onContentChange,
  contentFocused,
  onContentFocus,
  onContentBlur,
  isFormValid,
  submitting,
  onSubmit,
}: CreateStoryFormProps) {
  const { t } = useTranslation();
  const theme = usePaperTheme();

  return (
    <>
      <View style={{ marginBottom: theme.spacing?.lg || 24 }}>
        <TextInput
          mode="outlined"
          label={t('storyForm.titleLabel', '标题')}
          value={title}
          onChangeText={onTitleChange}
          placeholder={t('storyForm.titlePlaceholder', '输入故事标题')}
          left={<TextInput.Icon icon="bookmark-outline" />}
          maxLength={100}
          style={{ marginBottom: theme.spacing?.sm || 8 }}
        />
        {title.length > 0 && (
          <HelperText type="info" visible={true}>
            {title.length}/100
          </HelperText>
        )}
      </View>

      <View style={{ marginBottom: theme.spacing?.lg || 24 }}>
        <TextInput
          mode="outlined"
          label={t('storyForm.initialContentLabel', '开始你的故事')}
          value={initialContent}
          onChangeText={onContentChange}
          placeholder={t('storyForm.initialContentPlaceholder', '从前...')}
          left={<TextInput.Icon icon="book-open-page-variant-outline" />}
          multiline
          numberOfLines={6}
          onFocus={onContentFocus}
          onBlur={onContentBlur}
          style={{ marginBottom: theme.spacing?.sm || 8 }}
        />
        {initialContent.length > 0 && (
          <HelperText
            type={initialContent.length < 50 ? 'error' : 'info'}
            visible={true}
          >
            {initialContent.length < 50
              ? `${t('storyForm.contentMinLength', '至少需要50个字符')} (${
                  initialContent.length
                }/50)`
              : `${initialContent.length} ${t(
                  'storyForm.characters',
                  '个字符'
                )}`}
          </HelperText>
        )}
      </View>

      <HelperText
        type="info"
        visible={true}
        style={{ marginBottom: theme.spacing?.lg || 24 }}
      >
        {t('storyForm.tip', '提示：一个好的开头能够吸引读者继续阅读你的故事。')}
      </HelperText>

      <Button
        mode="contained"
        onPress={onSubmit}
        disabled={submitting || !isFormValid}
        loading={submitting}
        style={{ paddingVertical: theme.spacing?.sm || 8 }}
        labelStyle={{ fontSize: 16 }}
      >
        {t('storyForm.submitButton', '创建故事')}
      </Button>
    </>
  );
}
