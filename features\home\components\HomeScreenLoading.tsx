import React from 'react';
import { View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export function HomeScreenLoading() {
  const theme = usePaperTheme();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: theme.spacing?.lg || 20,
      }}
    >
      <ActivityIndicator size="large" animating={true} />
    </View>
  );
}
