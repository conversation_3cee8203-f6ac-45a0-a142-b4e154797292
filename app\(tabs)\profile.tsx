import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileScreenContent from '@/features/profile/screens/ProfileScreen';
import { useAppTheme } from '@/hooks/useAppTheme';

// This route file now simply imports and renders the feature screen
// It also handles the top-level SafeAreaView for this route segment
export default function ProfileRoute() {
  const theme = useAppTheme();
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ProfileScreenContent />
    </SafeAreaView>
  );
}

/*
// Original placeholder content (commented out)
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';

export default function ProfileScreen_Placeholder() {
  const { t } = useTranslation();
  const theme = useAppTheme();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ color: theme.colors.text, fontSize: 20 }}>
        {t('profile')} {t('testPlaceholder')} 
      </Text>
    </SafeAreaView>
  );
}
*/