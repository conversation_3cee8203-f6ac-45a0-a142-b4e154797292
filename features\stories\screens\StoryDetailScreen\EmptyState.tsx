import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { createDynamicStyles } from '../StoryDetailScreen.styles';

export function EmptyState() {
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.centered}>
      <Text style={styles.errorText}>
        {t('storyDetail.errors.notFound', 'Story not found.')}
      </Text>
    </View>
  );
}
