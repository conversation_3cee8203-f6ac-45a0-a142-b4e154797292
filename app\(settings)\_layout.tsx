import { Stack } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';

export default function SettingsLayout() {
  const theme = useAppTheme();
  const { t } = useTranslation();

  return (
    <Stack
      screenOptions={{
        headerStyle: { backgroundColor: theme.colors.surface }, // Use surface color for header bg
        headerTintColor: theme.colors.text, // Color for back button and title
        headerTitleStyle: {
          fontFamily: theme.fonts.bold,
        },
        headerShown: true, // Ensure header is shown
      }}
    >
      {/* Define screens within the settings stack */}
      {/* The actual screen content comes from app/(settings)/index.tsx */}
      <Stack.Screen
        name="index"
        options={{
          title: t('settings'), // Get title from translation
        }}
      />
    </Stack>
  );
}
