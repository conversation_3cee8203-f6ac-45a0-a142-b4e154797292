import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { createAddSegmentFormStyles } from './AddSegmentForm.styles';
import { GitBranch } from 'lucide-react-native';

interface AddSegmentFormProps {
  segmentContent: string;
  onContentChange: (text: string) => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  onCreateBranchClick?: () => void;
}

export default function AddSegmentForm({
  segmentContent,
  onContentChange,
  onSubmit,
  isSubmitting,
  onCreateBranchClick,
}: AddSegmentFormProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = useMemo(() => createAddSegmentFormStyles(theme), [theme]);

  const isDisabled = isSubmitting || !segmentContent.trim();

  // 处理创建分支按钮点击
  const handleCreateBranchClick = () => {
    if (!segmentContent.trim()) {
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        t('storyDetail.errors.emptyBranch', 'Cannot create an empty branch.')
      );
      return;
    }

    if (onCreateBranchClick) {
      onCreateBranchClick();
    }
  };

  return (
    <View style={styles.addSegmentSection}>
      <Text style={styles.sectionTitle}>
        {t('storyDetail.continueStory', 'Continue the Story')}
      </Text>
      <TextInput
        style={styles.textInput}
        placeholder={t('storyDetail.addYourPart', 'Add your part...')}
        placeholderTextColor={theme.colors.placeholder}
        textAlignVertical="top"
        multiline
        value={segmentContent}
        onChangeText={onContentChange}
        editable={!isSubmitting}
      />
      <View style={styles.buttonContainer}>
        {onCreateBranchClick && (
          <TouchableOpacity
            style={[styles.branchButton, isDisabled && styles.buttonDisabled]}
            onPress={handleCreateBranchClick}
            disabled={isDisabled}
          >
            <GitBranch size={16} color={theme.colors.onPrimary} />
            <Text style={styles.branchButtonText}>
              {t('storyDetail.createBranch', 'Create Branch')}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.submitButton,
            isDisabled && styles.buttonDisabled,
            onCreateBranchClick ? styles.submitButtonWithBranch : null,
          ]}
          onPress={onSubmit}
          disabled={isDisabled}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color={theme.colors.onPrimary} />
          ) : (
            <Text style={styles.submitButtonText}>
              {t('storyDetail.addSegment', 'Add Your Part')}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* AI Suggestions Section has been moved to AISuggestionBlock component */}
    </View>
  );
}
