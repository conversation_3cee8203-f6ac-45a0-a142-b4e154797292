import React from 'react';
import { View, Text } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './ProfileStats.styles';
import { useTranslation } from 'react-i18next';

interface ProfileStatsProps {
  posts: number;
  followers: number;
  following: number;
}

export function ProfileStats({
  posts,
  followers,
  following,
}: ProfileStatsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  const StatItem = ({ value, label }: { value: number; label: string }) => (
    <View style={styles.statItem}>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );

  return (
    <View style={styles.statsContainer}>
      <StatItem value={posts} label={t('profile.stories', '故事')} />
      <StatItem value={followers} label={t('profile.followers', '粉丝')} />
      <StatItem value={following} label={t('profile.following', '关注')} />
    </View>
  );
}
