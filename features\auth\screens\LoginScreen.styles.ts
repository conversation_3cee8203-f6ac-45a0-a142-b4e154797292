import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.background,
    },
    title: {
      fontSize: 24,
      fontFamily: theme.fonts.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
    },
    input: {
      width: '100%',
      padding: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.roundness,
      marginBottom: theme.spacing.md,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
    },
    forgotPasswordContainer: {
      alignSelf: 'flex-end',
      marginBottom: theme.spacing.md,
    },
    forgotPasswordText: {
      color: theme.colors.primary,
      fontFamily: theme.fonts.medium,
      fontSize: theme.fontSizes.sm,
    },
    button: {
      width: '100%',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: theme.roundness,
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    buttonDisabled: {
      backgroundColor: theme.colors.disabled,
    },
    buttonText: {
      color: theme.colors.onPrimary, // Assuming theme has onPrimary color
      fontFamily: theme.fonts.bold,
      fontSize: 16,
    },
    signupContainer: {
      flexDirection: 'row',
      marginTop: theme.spacing.md,
    },
    signupText: {
      color: theme.colors.text,
      fontSize: 14,
      fontFamily: theme.fonts.regular,
    },
    signupLink: {
      color: theme.colors.primary,
      fontSize: 14,
      fontFamily: theme.fonts.bold,
      marginLeft: theme.spacing.xs,
    },
  });
