import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { supabase } from '@/utils/supabase';
import { useAuthStore } from '@/lib/store/authStore';
import { ChevronLeft, Send, Wand2 } from 'lucide-react-native';
import { StyleSheet } from 'react-native';
import { useStoryOptimization } from '@/features/stories/hooks/useStoryOptimization';
import StoryOptimizationBlock from '@/features/stories/components/StoryOptimizationBlock';

export default function CreateSegmentScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const router = useRouter();
  const { storyId, parentSegmentId } = useLocalSearchParams<{
    storyId?: string;
    parentSegmentId?: string;
  }>();
  const { user } = useAuthStore();

  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAIGenerated, setIsAIGenerated] = useState(false);

  const handleSubmit = useCallback(async () => {
    if (!storyId || !parentSegmentId || !user?.id) {
      setError(t('createSegment.missingParams', 'Missing required parameters'));
      return;
    }

    if (!content.trim()) {
      setError(t('createSegment.contentRequired', 'Content is required'));
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Format content with title if provided
      const formattedContent = title.trim()
        ? `[${title.trim()}] ${content.trim()}`
        : content.trim();

      // Insert new segment
      const { data, error: insertError } = await supabase
        .from('story_segments')
        .insert({
          story_id: storyId,
          author_id: user.id,
          content: formattedContent,
          parent_segment_id: parentSegmentId,
          is_ai_generated: isAIGenerated,
        })
        .select('id')
        .single();

      if (insertError) {
        throw new Error(insertError.message);
      }

      // Navigate back to story detail
      router.replace(`/stories/${storyId}`);
    } catch (err) {
      console.error('Error creating segment:', err);
      setError(
        err instanceof Error
          ? err.message
          : t('createSegment.unknownError', 'An unknown error occurred')
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [
    storyId,
    parentSegmentId,
    user?.id,
    content,
    title,
    router,
    t,
    isAIGenerated,
  ]);

  const handleCancel = useCallback(() => {
    router.back();
  }, [router]);

  // 添加 AI 优化功能
  const { isOptimizing, handleOptimizeContent } = useStoryOptimization({
    onOptimizedContent: (optimizedContent) => {
      setContent(optimizedContent);
      setIsAIGenerated(true);
    },
  });

  // 处理 AI 优化
  const handleOptimize = (contentToOptimize: string, type: any) => {
    handleOptimizeContent(contentToOptimize);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.surface,
    },
    headerTitle: {
      fontSize: 18,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    backButton: {
      padding: theme.spacing.xs,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    inputContainer: {
      marginBottom: theme.spacing.md,
    },
    label: {
      fontSize: 16,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    titleInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: theme.spacing.md,
      fontSize: 16,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
    },
    contentInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: theme.spacing.md,
      fontSize: 16,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      minHeight: 200,
      textAlignVertical: 'top',
    },
    errorText: {
      color: theme.colors.error,
      fontSize: 14,
      fontFamily: theme.fonts.regular,
      marginTop: theme.spacing.sm,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: theme.spacing.md,
    },
    cancelButton: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.roundness,
      borderWidth: 1,
      borderColor: theme.colors.border,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginRight: theme.spacing.sm,
    },
    cancelButtonText: {
      fontSize: 16,
      fontFamily: theme.fonts.medium,
      color: theme.colors.text,
    },
    submitButton: {
      flex: 1,
      backgroundColor: theme.colors.primary,
      borderRadius: theme.roundness,
      padding: theme.spacing.md,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
    },
    submitButtonText: {
      fontSize: 16,
      fontFamily: theme.fonts.medium,
      color: theme.colors.background,
      marginLeft: theme.spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleCancel}>
          <ChevronLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {t('createSegment.title', 'Create New Segment')}
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView style={styles.content}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              {t('createSegment.titleLabel', 'Title (Optional)')}
            </Text>
            <TextInput
              style={styles.titleInput}
              value={title}
              onChangeText={setTitle}
              placeholder={t(
                'createSegment.titlePlaceholder',
                'Enter a title for your segment'
              )}
              placeholderTextColor={theme.colors.secondaryText}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>
              {t('createSegment.contentLabel', 'Content')}
            </Text>
            <TextInput
              style={styles.contentInput}
              value={content}
              onChangeText={setContent}
              placeholder={t(
                'createSegment.contentPlaceholder',
                'Write your story segment here...'
              )}
              placeholderTextColor={theme.colors.secondaryText}
              multiline
              textAlignVertical="top"
            />
          </View>

          {error && <Text style={styles.errorText}>{error}</Text>}

          {/* AI 优化按钮 */}
          <StoryOptimizationBlock
            onOptimizeContent={handleOptimize}
            isOptimizing={isOptimizing}
            disabled={isSubmitting}
          />

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
              disabled={isSubmitting}
            >
              <Text style={styles.cancelButtonText}>
                {t('common.cancel', '取消')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator
                  size="small"
                  color={theme.colors.background}
                />
              ) : (
                <>
                  <Send size={18} color={theme.colors.background} />
                  <Text style={styles.submitButtonText}>
                    {t('common.submit', '提交')}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
