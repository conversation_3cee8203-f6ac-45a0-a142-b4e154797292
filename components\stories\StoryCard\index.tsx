import React from 'react';
import { View } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Story as ApiStory } from '@/api/stories';
import { PremiumBadge } from './PremiumBadge';
import { StoryStats } from './StoryStats';

export type { ApiStory as Story };

interface StoryCardProps {
  story: ApiStory;
  onPress?: (storyId: string) => void;
  style?: any; // Allow passing additional styles
}

export default function StoryCard({ story, onPress, style }: StoryCardProps) {
  const theme = usePaperTheme();
  const router = useRouter();

  const coverImageUrl = story.cover_image_url;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const isPremium = false;

  const handlePress = () => {
    if (onPress) {
      onPress(story.id);
    } else {
      router.push(`/stories/${story.id}`);
    }
  };

  return (
    <Card
      mode="elevated"
      onPress={handlePress}
      style={[
        {
          margin: theme.spacing?.xs || 4,
          flex: 1,
        },
        style,
      ]}
    >
      <View style={{ position: 'relative' }}>
        <Card.Cover
          source={
            coverImageUrl
              ? { uri: coverImageUrl }
              : require('../../../assets/images/default-story-placeholder.png')
          }
        />

        {/* Premium badge overlay */}
        <PremiumBadge visible={isPremium} />
      </View>

      <Card.Content style={{ paddingTop: theme.spacing?.sm || 8 }}>
        <Text variant="titleSmall" numberOfLines={1}>
          {story.title}
        </Text>

        <Text
          variant="bodySmall"
          style={{ color: theme.colors.onSurfaceVariant }}
          numberOfLines={1}
        >
          @{authorName}
        </Text>

        <StoryStats views={views} likes={likes} />
      </Card.Content>
    </Card>
  );
}
