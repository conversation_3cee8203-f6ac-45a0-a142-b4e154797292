import { Platform } from 'react-native'; // Added Platform import for SHADOWS

// Extracted from constants/Theme.ts for reusability and clarity
const FONT_FAMILY_CONST = {
  regular: 'Inter-Regular',
  medium: 'Inter-Medium',
  bold: 'Inter-Bold',
  storyTitle: 'Merriweather-Bold',
  storyBody: 'Merriweather-Regular',
};

const FONT_SIZE_CONST = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 30,
};

const LINE_HEIGHT_CONST = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 28,
  xl: 30,
  xxl: 32,
  xxxl: 38,
};

const SPACING_CONST = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

const RADIUS_CONST = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  xxl: 32,
  round: 9999,
};

const SHADOWS_CONST = {
  sm: {
    boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.15)',
  },
  md: {
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.2)',
  },
  lg: {
    boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.25)',
  },
};

const ANIMATION_CONST = {
  fast: 200,
  normal: 300,
  slow: 500,
};

export const lightTheme = {
  dark: false,
  colors: {
    primary: '#6200EE',
    background: '#FFFFFF',
    surface: '#FFFFFF',
    card: '#FFFFFF',
    text: '#000000',
    onSurface: '#000000',
    secondaryText: 'rgba(0, 0, 0, 0.6)',
    disabled: 'rgba(0, 0, 0, 0.26)',
    placeholder: 'rgba(0, 0, 0, 0.54)',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#f50057',
    border: '#D1D1D6',
    accent: '#03DAC6',
    error: '#B00020',
    tint: '#6200EE',
    tabIconDefault: '#ccc',
    tabIconSelected: '#6A55FA',
    success: '#34C759',
    warning: '#FF9500',
    accent1: '#DA62FF',
    accent2: '#4A89F7',
    overlay: 'rgba(0, 0, 0, 0.1)',
    onPrimary: '#FFFFFF',
    gold: '#FFD700',
    silver: '#C0C0C0',
    bronze: '#CD7F32',
  },
  fonts: FONT_FAMILY_CONST,
  fontSizes: FONT_SIZE_CONST,
  lineHeights: LINE_HEIGHT_CONST,
  spacing: SPACING_CONST,
  radius: RADIUS_CONST, // Changed from roundness to radius object
  shadows: SHADOWS_CONST,
  animation: ANIMATION_CONST,
};

export const darkTheme = {
  dark: true,
  colors: {
    primary: '#BB86FC',
    background: '#121212',
    surface: '#1E1E1E',
    card: '#1E1E1E',
    text: '#FFFFFF',
    onSurface: '#FFFFFF',
    secondaryText: 'rgba(255, 255, 255, 0.7)',
    disabled: 'rgba(255, 255, 255, 0.38)',
    placeholder: 'rgba(255, 255, 255, 0.54)',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#ff80ab',
    border: '#272729',
    accent: '#03DAC6',
    error: '#CF6679',
    tint: '#BB86FC',
    tabIconDefault: '#888',
    tabIconSelected: '#8E7CFF',
    success: '#30D158',
    warning: '#FF9F0A',
    accent1: '#DA62FF',
    accent2: '#4A89F7',
    overlay: 'rgba(0, 0, 0, 0.6)',
    onPrimary: '#000000',
    gold: '#FFD700',
    silver: '#C0C0C0',
    bronze: '#CD7F32',
  },
  fonts: FONT_FAMILY_CONST,
  fontSizes: FONT_SIZE_CONST,
  lineHeights: LINE_HEIGHT_CONST,
  spacing: SPACING_CONST,
  radius: RADIUS_CONST, // Changed from roundness to radius object
  shadows: SHADOWS_CONST,
  animation: ANIMATION_CONST,
};

export type AppTheme = typeof lightTheme;
