import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import MessagesTabComponent from '@/features/messages/components/MessagesTab';

interface MessagesTabProps {
  // Props if needed in the future
}

export function MessagesTab({}: MessagesTabProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <MessagesTabComponent />
    </View>
  );
}

// 创建样式
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
  });
