import React, { useState } from 'react';
import { View, Text, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useAuthStore } from '@/lib/store/authStore';
import ActionButtons from './ActionButtons';
import ContinuationInput from './ContinuationInput';

// Types
export interface StoryPart {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  isAiGenerated?: boolean;
  createdAt: string;
  likes?: number;
  parentId?: string | null;
  children?: StoryPart[];
}

interface StoryPartNodeProps {
  part: StoryPart;
  level?: number;
  onAddContinuation?: (
    parentId: string,
    content: string,
    isAiGenerated?: boolean
  ) => Promise<void>;
}

export default function StoryPartNode({
  part,
  level = 0,
  onAddContinuation,
}: StoryPartNodeProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const user = useAuthStore((state) => state.user);

  const [showContinueInput, setShowContinueInput] = useState(false);
  const [continuationContent, setContinuationContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleToggleContinueInput = () => {
    setShowContinueInput(!showContinueInput);
    setContinuationContent('');
  };

  const handleSubmitContinuation = async () => {
    if (!continuationContent.trim()) {
      Alert.alert(
        t('error', 'Error'),
        t('story.emptyContinuation', 'Please write something to continue the story.')
      );
      return;
    }

    if (!user) {
      Alert.alert(
        t('error', 'Error'),
        t('auth.loginRequired', 'You need to be logged in to continue the story.')
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (onAddContinuation) {
        await onAddContinuation(part.id, continuationContent.trim());
      }
      setContinuationContent('');
      setShowContinueInput(false);
    } catch (error) {
      console.error('Failed to add continuation:', error);
      Alert.alert(
        t('error', 'Error'),
        t('story.failedToAddContinuation', 'Failed to add your continuation. Please try again.')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={level > 0 ? styles.nodeContainer : undefined}>
      <View style={styles.partContainer}>
        <Text style={styles.partContent}>{part.content}</Text>
        <Text style={styles.partAuthor}>
          {part.isAiGenerated
            ? t('story.aiGenerated', 'AI Generated')
            : t('story.writtenBy', 'Written by {{author}}', {
                author: part.authorName,
              })}
        </Text>

        <ActionButtons
          isLoggedIn={!!user}
          showContinueInput={showContinueInput}
          onToggleContinueInput={handleToggleContinueInput}
        />

        {showContinueInput && (
          <ContinuationInput
            content={continuationContent}
            onContentChange={setContinuationContent}
            onSubmit={handleSubmitContinuation}
            isSubmitting={isSubmitting}
          />
        )}
      </View>

      {part.children && part.children.length > 0 && (
        <View style={styles.childrenContainer}>
          {part.children.map((childPart) => (
            <StoryPartNode
              key={childPart.id}
              part={childPart}
              level={level + 1}
              onAddContinuation={onAddContinuation}
            />
          ))}
        </View>
      )}
    </View>
  );
}
