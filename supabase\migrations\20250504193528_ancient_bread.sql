/*
  # Initial Schema Setup

  1. New Tables
    - `users`
      - Core user information and stats
      - Includes premium membership tracking
    - `story_themes`
      - Predefined story themes/categories
    - `stories`
      - Main story information
      - Links to author and themes
    - `story_branches`
      - Story content and branch relationships
    - `story_comments`
      - User comments on stories and branches

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Secure access to premium content
*/

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT auth.uid(),
  username text UNIQUE NOT NULL,
  display_name text NOT NULL,
  email text UNIQUE NOT NULL,
  avatar_url text,
  bio text,
  member_since timestamptz DEFAULT now(),
  is_premium boolean DEFAULT false,
  premium_until timestamptz,
  followers_count int DEFAULT 0,
  following_count int DEFAULT 0,
  stories_count int DEFAULT 0,
  branches_count int DEFAULT 0,
  likes_received int DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create story_themes table
CREATE TABLE IF NOT EXISTS story_themes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  icon text NOT NULL,
  color text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create stories table
CREATE TABLE IF NOT EXISTS stories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  cover_image text NOT NULL,
  summary text,
  author_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  main_branch_id uuid,
  is_completed boolean DEFAULT false,
  is_premium boolean DEFAULT false,
  likes_count int DEFAULT 0,
  views_count int DEFAULT 0,
  is_ai_assisted boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create story_branches table
CREATE TABLE IF NOT EXISTS story_branches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id uuid REFERENCES stories(id) ON DELETE CASCADE NOT NULL,
  content text NOT NULL,
  author_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  is_ai_generated boolean DEFAULT false,
  likes_count int DEFAULT 0,
  parent_branch_id uuid REFERENCES story_branches(id),
  created_at timestamptz DEFAULT now()
);

-- Create story_comments table
CREATE TABLE IF NOT EXISTS story_comments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id uuid REFERENCES stories(id) ON DELETE CASCADE NOT NULL,
  branch_id uuid REFERENCES story_branches(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  content text NOT NULL,
  likes_count int DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Create stories_themes junction table
CREATE TABLE IF NOT EXISTS stories_themes (
  story_id uuid REFERENCES stories(id) ON DELETE CASCADE,
  theme_id uuid REFERENCES story_themes(id) ON DELETE CASCADE,
  PRIMARY KEY (story_id, theme_id)
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_themes ENABLE ROW LEVEL SECURITY;
ALTER TABLE stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE stories_themes ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- Users policies
CREATE POLICY "Users can read all users"
  ON users FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update own profile"
  ON users FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Story themes policies
CREATE POLICY "Anyone can read themes"
  ON story_themes FOR SELECT
  TO anon, authenticated
  USING (true);

-- Stories policies
CREATE POLICY "Anyone can read non-premium stories"
  ON stories FOR SELECT
  TO anon, authenticated
  USING (NOT is_premium OR auth.uid() IN (
    SELECT id FROM users WHERE is_premium = true AND premium_until > now()
  ));

CREATE POLICY "Authors can update own stories"
  ON stories FOR UPDATE
  TO authenticated
  USING (auth.uid() = author_id)
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authenticated users can create stories"
  ON stories FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = author_id);

-- Story branches policies
CREATE POLICY "Anyone can read branches of accessible stories"
  ON story_branches FOR SELECT
  TO anon, authenticated
  USING (
    story_id IN (
      SELECT id FROM stories 
      WHERE NOT is_premium 
      OR auth.uid() IN (SELECT id FROM users WHERE is_premium = true AND premium_until > now())
    )
  );

CREATE POLICY "Authenticated users can create branches"
  ON story_branches FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update own branches"
  ON story_branches FOR UPDATE
  TO authenticated
  USING (auth.uid() = author_id)
  WITH CHECK (auth.uid() = author_id);

-- Story comments policies
CREATE POLICY "Anyone can read comments"
  ON story_comments FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Authenticated users can create comments"
  ON story_comments FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own comments"
  ON story_comments FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Stories themes junction policies
CREATE POLICY "Anyone can read stories themes"
  ON stories_themes FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Authors can manage story themes"
  ON stories_themes FOR ALL
  TO authenticated
  USING (
    story_id IN (
      SELECT id FROM stories WHERE author_id = auth.uid()
    )
  );