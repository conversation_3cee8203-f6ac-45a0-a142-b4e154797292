import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      width: 150,
      height: 200,
      borderRadius: theme.radius.md,
      overflow: 'hidden',
      marginRight: theme.spacing.sm,
      marginBottom: theme.spacing.sm,
      backgroundColor: theme.colors.background,
    },
    coverImage: {
      width: '100%',
      height: '100%',
      position: 'absolute',
      borderRadius: theme.radius.md,
    },
    overlay: {
      flex: 1,
      justifyContent: 'flex-end',
    },
    contentContainer: {
      padding: theme.spacing.sm,
    },
    title: {
      fontFamily: theme.fonts.bold,
      fontSize: theme.fontSizes.md,
      color: '#FFFFFF',
      marginBottom: theme.spacing.xs,
    },
    bottomRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    themesContainer: {
      flexDirection: 'row',
      gap: theme.spacing.xs,
    },
    themeTag: {
      backgroundColor: 'rgba(255, 255, 255, 0.25)',
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.radius.sm,
    },
    themeText: {
      fontFamily: theme.fonts.regular,
      fontSize: 10,
      color: '#FFFFFF',
    },
    arrowContainer: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
    },
  });
