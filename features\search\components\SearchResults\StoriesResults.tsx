import React from 'react';
import { View, FlatList } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Story } from '@/types/story';
import StoryCard from '@/components/stories/StoryCard';
import { createStyles } from './styles';

interface StoriesResultsProps {
  stories: Story[];
  onStoryPress: (storyId: string) => void;
  limit?: number;
  scrollEnabled?: boolean;
}

export function StoriesResults({ 
  stories, 
  onStoryPress, 
  limit,
  scrollEnabled = true
}: StoriesResultsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  const displayStories = limit ? stories.slice(0, limit) : stories;
  
  return (
    <FlatList
      data={displayStories}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <View style={styles.storyCard}>
          <StoryCard story={item} onPress={() => onStoryPress(item.id)} />
        </View>
      )}
      numColumns={2}
      contentContainerStyle={styles.storiesGrid}
      scrollEnabled={scrollEnabled}
    />
  );
}
