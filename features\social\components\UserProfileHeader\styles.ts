import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => 
  StyleSheet.create({
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    avatar: {
      width: 80,
      height: 80,
      borderRadius: 40,
      marginRight: 16,
    },
    nameContainer: {
      flex: 1,
    },
    displayName: {
      fontSize: 22,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    username: {
      fontSize: 16,
      color: theme.colors.secondaryText,
      marginTop: 4,
    },
    premiumBadge: {
      backgroundColor: theme.colors.accent,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 8,
    },
    premiumText: {
      color: '#000',
      fontSize: 12,
      fontWeight: 'bold',
    },
  });
