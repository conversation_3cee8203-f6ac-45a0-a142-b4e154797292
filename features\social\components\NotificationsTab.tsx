import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import NotificationsTabComponent from '@/features/notifications/components/NotificationsTab';

interface NotificationsTabProps {
  // Props if needed in the future
}

export function NotificationsTab({}: NotificationsTabProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <NotificationsTabComponent />
    </View>
  );
}

// 创建样式
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
  });
