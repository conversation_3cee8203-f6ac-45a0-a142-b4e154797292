import React from 'react';
import { TextInput, IconButton } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { useTranslation } from 'react-i18next';
import { ViewStyle } from 'react-native';

interface SearchBarProps {
  value?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (text: string) => void;
  autoFocus?: boolean;
  placeholder?: string;
  style?: ViewStyle;
}

export default function SearchBar({
  value,
  onChangeText,
  onSearch,
  autoFocus = false,
  placeholder,
  style,
}: SearchBarProps) {
  const theme = usePaperTheme();
  const { t } = useTranslation();
  const [searchText, setSearchText] = React.useState(value || '');

  // 如果外部 value 变化，更新内部状态
  React.useEffect(() => {
    if (value !== undefined) {
      setSearchText(value);
    }
  }, [value]);

  const handleChangeText = (text: string) => {
    // 如果提供了外部 onChangeText，则调用它
    if (onChangeText) {
      onChangeText(text);
    } else {
      // 否则使用内部状态
      setSearchText(text);
    }
  };

  const handleSubmit = () => {
    const textToSearch = value !== undefined ? value : searchText;
    if (onSearch && textToSearch.trim()) {
      onSearch(textToSearch);
    }
  };

  const handleClear = () => {
    if (onChangeText) {
      onChangeText('');
    }
    setSearchText('');
  };

  const currentValue = value !== undefined ? value : searchText;
  const showClearButton = currentValue && currentValue.length > 0;

  return (
    <TextInput
      mode="outlined"
      placeholder={
        placeholder || t('searchPlaceholder', '搜索故事、作者、标签...')
      }
      value={currentValue}
      onChangeText={handleChangeText}
      onSubmitEditing={handleSubmit}
      returnKeyType="search"
      autoFocus={autoFocus}
      left={<TextInput.Icon icon="magnify" />}
      right={
        showClearButton ? (
          <TextInput.Icon icon="close" onPress={handleClear} />
        ) : undefined
      }
      style={style}
      // 使用主题的圆角和颜色
      theme={{
        roundness: theme.roundness,
      }}
    />
  );
}
