import { create } from 'zustand';
// Update i18n import path
import i18n from '@/lib/i18n/config';
import { Appearance } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createJSONStorage, persist } from 'zustand/middleware';

export type ThemeMode = 'light' | 'dark' | 'system';

interface SettingsState {
  themeMode: ThemeMode;
  systemColorScheme: 'light' | 'dark';
  language: string;
  setThemeMode: (mode: ThemeMode) => void;
  setSystemColorScheme: (scheme: 'light' | 'dark') => void;
  setLanguage: (lang: string) => void;
  _hasHydrated: boolean; // 用于确保在客户端初始化完成后再使用系统颜色
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      themeMode: 'system', // 默认跟随系统
      systemColorScheme: Appearance.getColorScheme() ?? 'light', // 获取初始系统颜色
      language: i18n.language || 'en', // 从 i18n 获取初始语言
      _hasHydrated: false, // Initialized to false, will be set to true on rehydration
      setThemeMode: (mode) => set({ themeMode: mode }),
      setSystemColorScheme: (scheme) => set({ systemColorScheme: scheme }),
      setLanguage: (lang) => {
        set({ language: lang });
        // 同步更新 i18next 实例
        i18n.changeLanguage(lang);
      },
    }),
    {
      name: 'settings-storage', // Unique name for AsyncStorage key
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        themeMode: state.themeMode,
        language: state.language,
        // Do not persist systemColorScheme or _hasHydrated
      }),
      onRehydrateStorage: () => {
        // This is called when hydration is done
        return (state, error) => {
          if (error) {
            console.error(
              'An error occurred during settings store rehydration:',
              error
            );
          } else {
            // After rehydration, set the i18n language to the rehydrated language
            if (state?.language) {
              i18n.changeLanguage(state.language);
            }
            // Mark as hydrated
            useSettingsStore.setState({ _hasHydrated: true });
            // Initialize system color scheme listener *after* hydration
            initializeSystemColorSchemeListeners();
          }
        };
      },
    }
  )
);

// This function will now only set up listeners and initial system scheme if not already set by hydration.
// It's called after hydration to ensure listeners are active.
const initializeSystemColorSchemeListeners = () => {
  // Set initial system color scheme if not already set during rehydration or by default.
  const currentSystemScheme = Appearance.getColorScheme() ?? 'light';
  if (useSettingsStore.getState().systemColorScheme !== currentSystemScheme) {
    useSettingsStore.setState({ systemColorScheme: currentSystemScheme });
  }

  // Remove previous listener finding logic - Appearance.listeners() is undefined.
  // Since this function is called only once after hydration, we assume adding is sufficient.
  // const listener = Appearance.listeners('change').find(l => (l as any)._settingsListenerMarker === true);
  // if (listener) {
  //   Appearance.removeChangeListener(listener as any);
  // }

  // Check if listener already exists (less reliable without direct access, but prevents duplicates if re-run)
  // A simple approach is to add only once, relying on the call context.
  // If a more robust deduplication is needed, a flag in the store could be used.

  // Add the listener for future changes
  const newListener = ({ colorScheme }: Appearance.AppearancePreferences) => {
    useSettingsStore.getState().setSystemColorScheme(colorScheme ?? 'light');
  };
  // We might still want to prevent adding multiple listeners if the app somehow causes this code to run again.
  // A simple flag could work.
  if (!(global as any).__appearanceListenerAttached) {
    Appearance.addChangeListener(newListener);
    (global as any).__appearanceListenerAttached = true;
  }
};

// The function to be called from _layout.tsx (or similar entry point) once.
// It ensures the store attempts to rehydrate.
// The actual initialization of listeners happens in onRehydrateStorage callback.
export const ensureSettingsStoreHydrated = () => {
  // Zustand's persist middleware handles rehydration automatically.
  // We just need to ensure the store is initialized in the app.
  // Calling a selector (even just getState) is enough to initialize it.
  useSettingsStore.getState();
};

// The `initializeSystemColorScheme` from _layout.tsx should be replaced by `ensureSettingsStoreHydrated`
// and the core logic is now within `onRehydrateStorage` and `initializeSystemColorSchemeListeners`.

// Clean up listener on app unmount if possible (though usually not critical for Expo apps)
// This part is more complex and might require a context or a top-level component effect cleanup.
