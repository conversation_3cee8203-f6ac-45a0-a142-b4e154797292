import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles for SettingsScreenContent, potentially reusable parts will be moved later
export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      // Background color applied by the parent layout using SafeAreaView
    },
    header: {
      paddingHorizontal: theme.spacing.md,
      paddingTop: theme.spacing.md,
      paddingBottom: theme.spacing.sm,
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: theme.colors.border,
      alignItems: 'center',
    },
    title: {
      fontSize: 20, // Adjusted size
      fontFamily: theme.fonts.bold,
      color: theme.colors.text, // Apply color directly
    },
    section: {
      marginTop: theme.spacing.lg,
      paddingHorizontal: theme.spacing.md,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: theme.fonts.medium,
      marginBottom: theme.spacing.md,
      color: theme.colors.text, // Apply color directly
    },
    // Styles below will likely move to child component styles
    optionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.roundness * 2,
      padding: theme.spacing.xs,
      borderWidth: StyleSheet.hairlineWidth,
      borderColor: theme.colors.border,
    },
    optionButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.roundness,
      alignItems: 'center',
    },
    optionButtonSelected: {
      backgroundColor: theme.colors.primary,
    },
    optionText: {
      color: theme.colors.text,
      fontFamily: theme.fonts.medium,
    },
    optionTextSelected: {
      color: theme.dark ? '#000' : '#FFF',
    },
    logoutButton: {
      backgroundColor: theme.colors.error, // Or another appropriate color like a subtle gray or theme.colors.surface
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.roundness,
      alignItems: 'center',
      marginTop: theme.spacing.sm,
    },
    logoutButtonText: {
      color: '#FFFFFF', // Assuming a dark button background
      fontFamily: theme.fonts.medium,
      fontSize: 16,
    },
  });
