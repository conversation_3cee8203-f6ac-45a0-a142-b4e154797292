import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { Profile } from '../profiles';

export interface FollowResult {
  follower_id: string;
  following_id: string;
  created_at: string;
}

export interface GetFollowsOptions {
  limit?: number;
  offset?: number;
}

/**
 * Follow a user
 * @param userId The ID of the user to follow
 * @returns The follow result or error
 */
export async function followUser(
  userId: string
): Promise<{ data: FollowResult | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  // Check if already following
  const { data: existingFollow, error: checkError } = await supabase
    .from('user_follows')
    .select('*')
    .eq('follower_id', user.id)
    .eq('following_id', userId)
    .single();

  if (checkError && checkError.code !== 'PGRST116') {
    // PGRST116 is the error code for "no rows returned"
    return { data: null, error: checkError };
  }

  if (existingFollow) {
    return { data: existingFollow as FollowResult, error: null };
  }

  // Create follow relationship
  const { data, error } = await supabase
    .from('user_follows')
    .insert({
      follower_id: user.id,
      following_id: userId,
    })
    .select()
    .single();

  if (error) {
    console.error('Error following user:', error);
  }

  return { data: data as FollowResult, error };
}

/**
 * Unfollow a user
 * @param userId The ID of the user to unfollow
 * @returns Success status or error
 */
export async function unfollowUser(
  userId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  const { error } = await supabase
    .from('user_follows')
    .delete()
    .eq('follower_id', user.id)
    .eq('following_id', userId);

  if (error) {
    console.error('Error unfollowing user:', error);
    return { success: false, error };
  }

  return { success: true, error: null };
}

/**
 * Get a user's followers
 * @param userId The ID of the user to get followers for
 * @param options Pagination options
 * @returns List of followers or error
 */
export async function getFollowers(
  userId: string,
  options: GetFollowsOptions = {}
): Promise<{ data: Profile[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0 } = options;

  const { data, error } = await supabase
    .from('user_follows')
    .select(`
      follower_id,
      users!user_follows_follower_id_fkey (
        id, username, display_name, avatar_url, bio, is_premium, followers_count, following_count
      )
    `)
    .eq('following_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error getting followers:', error);
    return { data: null, error };
  }

  // Transform the data to match the Profile interface
  const followers = data.map((item: any) => ({
    id: item.users.id,
    username: item.users.username,
    full_name: item.users.display_name,
    avatar_url: item.users.avatar_url,
    bio: item.users.bio,
    is_premium: item.users.is_premium,
    followers_count: item.users.followers_count,
    following_count: item.users.following_count,
  }));

  return { data: followers, error: null };
}

/**
 * Get users followed by a user
 * @param userId The ID of the user to get following for
 * @param options Pagination options
 * @returns List of followed users or error
 */
export async function getFollowing(
  userId: string,
  options: GetFollowsOptions = {}
): Promise<{ data: Profile[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0 } = options;

  const { data, error } = await supabase
    .from('user_follows')
    .select(`
      following_id,
      users!user_follows_following_id_fkey (
        id, username, display_name, avatar_url, bio, is_premium, followers_count, following_count
      )
    `)
    .eq('follower_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error getting following:', error);
    return { data: null, error };
  }

  // Transform the data to match the Profile interface
  const following = data.map((item: any) => ({
    id: item.users.id,
    username: item.users.username,
    full_name: item.users.display_name,
    avatar_url: item.users.avatar_url,
    bio: item.users.bio,
    is_premium: item.users.is_premium,
    followers_count: item.users.followers_count,
    following_count: item.users.following_count,
  }));

  return { data: following, error: null };
}

/**
 * Check if the current user follows a specific user
 * @param userId The ID of the user to check
 * @returns Boolean indicating if the user is followed
 */
export async function checkIfFollowing(
  userId: string
): Promise<{ data: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  const { data, error } = await supabase
    .from('user_follows')
    .select('*')
    .eq('follower_id', user.id)
    .eq('following_id', userId)
    .single();

  if (error && error.code !== 'PGRST116') {
    // PGRST116 is the error code for "no rows returned"
    console.error('Error checking if following:', error);
    return { data: false, error };
  }

  return { data: !!data, error: null };
}

/**
 * Get the count of followers for a user
 * @param userId The ID of the user to get follower count for
 * @returns The follower count or error
 */
export async function getFollowerCount(
  userId: string
): Promise<{ count: number; error: PostgrestError | null }> {
  const { count, error } = await supabase
    .from('user_follows')
    .select('*', { count: 'exact', head: true })
    .eq('following_id', userId);

  if (error) {
    console.error('Error getting follower count:', error);
    return { count: 0, error };
  }

  return { count: count || 0, error: null };
}

/**
 * Get the count of users followed by a user
 * @param userId The ID of the user to get following count for
 * @returns The following count or error
 */
export async function getFollowingCount(
  userId: string
): Promise<{ count: number; error: PostgrestError | null }> {
  const { count, error } = await supabase
    .from('user_follows')
    .select('*', { count: 'exact', head: true })
    .eq('follower_id', userId);

  if (error) {
    console.error('Error getting following count:', error);
    return { count: 0, error };
  }

  return { count: count || 0, error: null };
}