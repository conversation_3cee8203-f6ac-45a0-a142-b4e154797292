import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { Notification } from '@/api/notifications/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import { Heart, MessageCircle, UserPlus, AtSign, BookOpen, Sparkle, Bell } from 'lucide-react-native';

interface NotificationItemProps {
  notification: Notification;
  onPress: (notification: Notification) => void;
  onMarkAsRead: (notificationId: string) => void;
  onDelete: (notificationId: string) => void;
}

export default function NotificationItem({
  notification,
  onPress,
  onMarkAsRead,
  onDelete,
}: NotificationItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t, i18n } = useTranslation();

  // 根据通知类型获取图标
  const getNotificationIcon = () => {
    const iconSize = 20;
    const iconColor = theme.colors.primary;

    switch (notification.type) {
      case 'like':
        return <Heart size={iconSize} color={iconColor} />;
      case 'comment':
        return <MessageCircle size={iconSize} color={iconColor} />;
      case 'follow':
        return <UserPlus size={iconSize} color={iconColor} />;
      case 'mention':
        return <AtSign size={iconSize} color={iconColor} />;
      case 'new_story':
        return <BookOpen size={iconSize} color={iconColor} />;
      case 'new_segment':
        return <Sparkle size={iconSize} color={iconColor} />;
      case 'system':
        return <Bell size={iconSize} color={iconColor} />;
      default:
        return <Bell size={iconSize} color={iconColor} />;
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 处理通知点击
  const handlePress = () => {
    if (!notification.is_read) {
      onMarkAsRead(notification.id);
    }
    onPress(notification);
  };

  // 处理删除按钮点击
  const handleDelete = () => {
    onDelete(notification.id);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        !notification.is_read && styles.unreadContainer,
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>{getNotificationIcon()}</View>

      <View style={styles.contentContainer}>
        {notification.actor && (
          <View style={styles.actorContainer}>
            {notification.actor.avatar_url ? (
              <Image
                source={{ uri: notification.actor.avatar_url }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatar, styles.placeholderAvatar]} />
            )}
            <Text style={styles.actorName}>{notification.actor.username}</Text>
          </View>
        )}

        <Text style={styles.title}>{notification.title}</Text>
        <Text style={styles.body} numberOfLines={2}>
          {notification.body}
        </Text>

        <Text style={styles.time}>{formatTime(notification.created_at)}</Text>
      </View>

      <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
        <Text style={styles.deleteText}>
          {t('notifications.delete', '删除')}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
}