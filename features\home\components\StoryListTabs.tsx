import React from 'react';
import { SegmentedButtons } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface StoryListTabsProps {
  tabs: string[];
  activeTab: string;
  onTabPress: (tab: string) => void;
}

export function StoryListTabs({
  tabs,
  activeTab,
  onTabPress,
}: StoryListTabsProps) {
  const theme = usePaperTheme();

  // Convert tabs array to SegmentedButtons format
  const buttons = tabs.map((tab) => ({
    value: tab,
    label: tab,
  }));

  return (
    <SegmentedButtons
      value={activeTab}
      onValueChange={onTabPress}
      buttons={buttons}
      style={{
        marginTop: theme.spacing?.lg || 16,
        marginBottom: theme.spacing?.md || 12,
      }}
    />
  );
}
