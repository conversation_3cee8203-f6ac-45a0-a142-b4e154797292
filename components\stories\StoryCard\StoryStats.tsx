import React from 'react';
import { View } from 'react-native';
import { Text, Icon } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface StoryStatsProps {
  views: number;
  likes: number;
}

export function StoryStats({ views, likes }: StoryStatsProps) {
  const theme = usePaperTheme();

  return (
    <View
      style={{
        flexDirection: 'row',
        marginTop: theme.spacing?.xs || 4,
        gap: theme.spacing?.md || 12,
      }}
    >
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: theme.spacing?.xs || 4,
        }}
      >
        <Icon source="eye" size={12} />
        <Text
          variant="bodySmall"
          style={{ color: theme.colors.onSurfaceVariant }}
        >
          {views}
        </Text>
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: theme.spacing?.xs || 4,
        }}
      >
        <Icon source="heart" size={12} />
        <Text
          variant="bodySmall"
          style={{ color: theme.colors.onSurfaceVariant }}
        >
          {likes}
        </Text>
      </View>
    </View>
  );
}
