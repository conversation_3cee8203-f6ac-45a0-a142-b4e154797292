import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './styles';
import { useTranslation } from 'react-i18next';
import {
  GitBranch,
  GitMerge,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';
import { useStoryBranches } from '../../hooks/useStoryBranches';
import { StorySegment } from '@/api/stories';

// 组件
import BranchVisualizer from '../BranchVisualizer';
import BranchNavigator from '../BranchNavigator';
import CreateBranchForm from '../CreateBranchForm';
import BranchInteractions from '../BranchInteractions';

interface BranchManagerProps {
  storyId: string;
  initialSegmentId?: string;
  onBranchChange?: (segmentId: string) => void;
  onRequestAiSuggestion?: () => Promise<string | null>;
}

export default function BranchManager({
  storyId,
  initialSegmentId,
  onBranchChange,
  onRequestAiSuggestion,
}: BranchManagerProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  // 状态
  const [isExpanded, setIsExpanded] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // 状态
  const [visualizationStyle, setVisualizationStyle] = useState<
    'tree' | 'flow' | 'network' | 'timeline'
  >('tree');

  // 使用分支Hook
  const {
    branchTree,
    currentSegmentId,
    currentPath,
    currentChildren,
    isLoading,
    error,
    isCreatingBranch,
    isLoadingMore,
    hasMoreChildren,
    navigateToBranch,
    createBranch,
    renameBranch,
    deleteBranch,
    refreshBranches,
    loadMoreChildren,
  } = useStoryBranches({
    storyId,
    initialSegmentId,
  });

  // 处理分支选择
  const handleBranchSelect = useCallback(
    (segmentId: string) => {
      console.log('BranchManager - handleBranchSelect:', segmentId);

      // 确保分支ID有效
      if (!segmentId) {
        console.error('Invalid segment ID for branch selection');
        return;
      }

      // 导航到选中的分支
      navigateToBranch(segmentId);

      // 通知父组件分支变化
      if (onBranchChange) {
        console.log('Notifying parent component of branch change:', segmentId);
        onBranchChange(segmentId);
      }
    },
    [navigateToBranch, onBranchChange]
  );

  // 处理根分支选择
  const handleRootSelect = useCallback(() => {
    if (branchTree) {
      navigateToBranch(branchTree.id);
      if (onBranchChange) {
        onBranchChange(branchTree.id);
      }
    }
  }, [branchTree, navigateToBranch, onBranchChange]);

  // 处理创建分支
  const handleCreateBranch = useCallback(
    async (
      content: string,
      branchTitle?: string,
      isAiGenerated: boolean = false
    ) => {
      if (!currentSegmentId) return;

      const newBranch = await createBranch(
        currentSegmentId,
        content,
        branchTitle,
        isAiGenerated
      );

      if (newBranch) {
        setShowCreateForm(false);
        // 可选：导航到新创建的分支
        navigateToBranch(newBranch.id);
        if (onBranchChange) {
          onBranchChange(newBranch.id);
        }
      }
    },
    [currentSegmentId, createBranch, navigateToBranch, onBranchChange]
  );

  // 切换展开/折叠
  const toggleExpanded = () => {
    setIsExpanded((prev) => !prev);
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerTitleContainer}>
            <GitBranch size={20} color={theme.colors.text} />
            <Text style={styles.headerTitle}>
              {t('storyDetail.branches', 'Story Branches')}
            </Text>
          </View>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>
            {t('storyDetail.loadingBranches', 'Loading branches...')}
          </Text>
        </View>
      </View>
    );
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerTitleContainer}>
            <GitBranch size={20} color={theme.colors.text} />
            <Text style={styles.headerTitle}>
              {t('storyDetail.branches', 'Story Branches')}
            </Text>
          </View>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {t('storyDetail.branchesError', 'Failed to load branches')}
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={refreshBranches}
          >
            <Text style={styles.retryButtonText}>{t('retry', 'Retry')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={toggleExpanded}
        activeOpacity={0.7}
      >
        <View style={styles.headerTitleContainer}>
          <GitBranch size={20} color={theme.colors.text} />
          <Text style={styles.headerTitle}>
            {t('storyDetail.branches', 'Story Branches')}
          </Text>
        </View>

        <View style={styles.headerControls}>
          <TouchableOpacity
            style={styles.createBranchButton}
            onPress={() => setShowCreateForm(true)}
          >
            <GitMerge size={16} color={theme.colors.onPrimary} />
            <Text style={styles.createBranchButtonText}>
              {t('storyDetail.createBranch', 'Create Branch')}
            </Text>
          </TouchableOpacity>

          {isExpanded ? (
            <ChevronUp size={24} color={theme.colors.text} />
          ) : (
            <ChevronDown size={24} color={theme.colors.text} />
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          <BranchNavigator
            currentPath={currentPath}
            currentChildren={currentChildren}
            onBranchSelect={handleBranchSelect}
            onRootSelect={handleRootSelect}
            isLoadingMore={isLoadingMore || false}
            hasMoreChildren={hasMoreChildren || false}
            onLoadMoreChildren={loadMoreChildren}
          />

          {/* 可视化样式选择器 */}
          <View style={styles.visualizationStyleSelector}>
            <Text style={styles.visualizationStyleLabel}>
              {t('storyDetail.visualizationStyle', 'Visualization Style')}:
            </Text>
            <View style={styles.visualizationStyleButtons}>
              <TouchableOpacity
                style={[
                  styles.visualizationStyleButton,
                  visualizationStyle === 'tree' &&
                    styles.visualizationStyleButtonActive,
                ]}
                onPress={() => setVisualizationStyle('tree')}
              >
                <Text
                  style={[
                    styles.visualizationStyleButtonText,
                    visualizationStyle === 'tree' &&
                      styles.visualizationStyleButtonTextActive,
                  ]}
                >
                  {t('storyDetail.treeView', 'Tree')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.visualizationStyleButton,
                  visualizationStyle === 'flow' &&
                    styles.visualizationStyleButtonActive,
                ]}
                onPress={() => setVisualizationStyle('flow')}
              >
                <Text
                  style={[
                    styles.visualizationStyleButtonText,
                    visualizationStyle === 'flow' &&
                      styles.visualizationStyleButtonTextActive,
                  ]}
                >
                  {t('storyDetail.flowView', 'Flow')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.visualizationStyleButton,
                  visualizationStyle === 'network' &&
                    styles.visualizationStyleButtonActive,
                ]}
                onPress={() => setVisualizationStyle('network')}
              >
                <Text
                  style={[
                    styles.visualizationStyleButtonText,
                    visualizationStyle === 'network' &&
                      styles.visualizationStyleButtonTextActive,
                  ]}
                >
                  {t('storyDetail.networkView', 'Network')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.visualizationStyleButton,
                  visualizationStyle === 'timeline' &&
                    styles.visualizationStyleButtonActive,
                ]}
                onPress={() => setVisualizationStyle('timeline')}
              >
                <Text
                  style={[
                    styles.visualizationStyleButtonText,
                    visualizationStyle === 'timeline' &&
                      styles.visualizationStyleButtonTextActive,
                  ]}
                >
                  {t('storyDetail.timelineView', 'Timeline')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <BranchVisualizer
            branchTree={branchTree}
            currentSegmentId={currentSegmentId}
            onBranchSelect={handleBranchSelect}
            onRenameBranch={renameBranch}
            onDeleteBranch={deleteBranch}
            collapsible={true}
            initialScale={0.9}
            visualizationStyle={visualizationStyle}
          />

          {/* 分支交互组件 */}
          {currentSegmentId && (
            <BranchInteractions segmentId={currentSegmentId} />
          )}
        </View>
      )}

      <CreateBranchForm
        visible={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSubmit={handleCreateBranch}
        parentSegmentId={currentSegmentId || ''}
        isSubmitting={isCreatingBranch}
        onRequestAiSuggestion={onRequestAiSuggestion}
      />
    </View>
  );
}
