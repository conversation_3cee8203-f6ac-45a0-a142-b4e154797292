import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

// Styles specific to the Discover Tab content
export const createStyles = (theme: AppTheme) =>
  StyleSheet.create({
    scrollView: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    contentContainer: {
      paddingVertical: theme.spacing.lg,
      paddingHorizontal: theme.spacing.md, // Horizontal padding for content
      paddingBottom: theme.spacing.xl * 2, // Extra padding at bottom for better scroll experience
    },
    searchBarContainer: {
      marginBottom: theme.spacing.lg,
      // shadowColor: theme.dark ? 'transparent' : '#000',
      // shadowOffset: { width: 0, height: 1 },
      // shadowOpacity: theme.dark ? 0 : 0.1,
      // shadowRadius: 3,
      // elevation: theme.dark ? 0 : 2,
      boxShadow: theme.dark ? 'none' : '0px 1px 3px rgba(0,0,0,0.1)',
      borderRadius: theme.radius.sm, // Assuming theme.roundness was similar to a small radius
    },
    sectionTitle: {
      fontFamily: theme.fonts.bold,
      fontSize: 20,
      color: theme.colors.text,
      marginTop: theme.spacing.lg, // Add top margin for sections after first
      marginBottom: theme.spacing.md,
    },
    usersGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.xl,
      // Use gap on UserCard if needed
    },
    topicsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.xl * 1.5, // More padding at the end
    },
    emptyStateContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.xl,
    },
    emptyStateText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.secondaryText,
      textAlign: 'center',
    },
    loadingContainer: {
      padding: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 200,
    },
    errorContainer: {
      padding: theme.spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 200,
    },
    errorText: {
      fontFamily: theme.fonts.medium,
      fontSize: 16,
      color: theme.colors.error,
      textAlign: 'center',
    },
  });
