import React from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { ChevronLeft } from 'lucide-react-native';
import { CommentList } from '@/features/comments/components';
import { createStyles } from './styles';

export default function StoryCommentsScreen() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();

  if (!id) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          {t('comments.errors.storyNotFound', '故事ID不存在')}
        </Text>
      </View>
    );
  }

  const handleBackPress = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <ChevronLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {t('comments.screenTitle', '评论')}
        </Text>
        <View style={styles.headerRight} />
      </View>
      
      {/* Comments List */}
      <View style={styles.commentsContainer}>
        <CommentList storyId={id} />
      </View>
    </SafeAreaView>
  );
}