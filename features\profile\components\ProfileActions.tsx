import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './ProfileActions.styles';
import { useTranslation } from 'react-i18next';

interface ProfileActionsProps {
  onEditProfile?: () => void;
  onShareProfile?: () => void;
}

export function ProfileActions({
  onEditProfile,
  onShareProfile,
}: ProfileActionsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  return (
    <View style={styles.actionsContainer}>
      {onEditProfile && (
        <TouchableOpacity style={styles.actionButton} onPress={onEditProfile}>
          <Text style={styles.actionButtonText}>
            {t('profile.editProfile', '编辑资料')}
          </Text>
        </TouchableOpacity>
      )}
      {onShareProfile && (
        <TouchableOpacity style={styles.actionButton} onPress={onShareProfile}>
          <Text style={styles.actionButtonText}>
            {t('profile.shareProfile', '分享')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
