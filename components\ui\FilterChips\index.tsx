import React from 'react';
import { ScrollView } from 'react-native';
import { Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

export interface FilterOption {
  id: string;
  label: string;
}

interface FilterChipsProps {
  options: FilterOption[];
  selectedIds: string[];
  onSelect: (id: string) => void;
  multiSelect?: boolean;
}

export default function FilterChips({
  options,
  selectedIds,
  onSelect,
  multiSelect = false,
}: FilterChipsProps) {
  const theme = usePaperTheme();

  const handleSelect = (id: string) => {
    onSelect(id);
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 8,
        gap: 8,
      }}
    >
      {options.map((option) => {
        const isSelected = selectedIds.includes(option.id);
        return (
          <Chip
            key={option.id}
            mode="outlined"
            selected={isSelected}
            onPress={() => handleSelect(option.id)}
            compact
          >
            {option.label}
          </Chip>
        );
      })}
    </ScrollView>
  );
}
