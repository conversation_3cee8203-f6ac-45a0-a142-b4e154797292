-- 修复缺少的字段：添加status、visibility字段到stories表
-- 创建story_segments表和story_likes表

-- 添加缺少的字段到stories表
ALTER TABLE IF EXISTS public.stories 
ADD COLUMN IF NOT EXISTS status text DEFAULT 'published' NOT NULL,
ADD COLUMN IF NOT EXISTS visibility text DEFAULT 'public' NOT NULL,
ADD COLUMN IF NOT EXISTS tags text[] DEFAULT '{}'::text[],
ADD COLUMN IF NOT EXISTS cover_image_url text;

-- 重命名cover_image列为旧列，并将其数据迁移到新的cover_image_url列
UPDATE public.stories
SET cover_image_url = cover_image
WHERE cover_image_url IS NULL AND cover_image IS NOT NULL;

-- 创建story_segments表(如果不存在)
CREATE TABLE IF NOT EXISTS public.story_segments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  story_id uuid REFERENCES stories(id) ON DELETE CASCADE NOT NULL,
  author_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  content_type text DEFAULT 'text' NOT NULL,
  content text NOT NULL,
  parent_segment_id uuid REFERENCES story_segments(id),
  order_in_branch integer DEFAULT 0,
  is_ai_generated boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 创建story_likes表(如果不存在)
CREATE TABLE IF NOT EXISTS public.story_likes (
  story_id uuid REFERENCES stories(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (story_id, user_id)
);

-- 为story_segments表启用RLS
ALTER TABLE IF EXISTS public.story_segments ENABLE ROW LEVEL SECURITY;
-- 为story_likes表启用RLS
ALTER TABLE IF EXISTS public.story_likes ENABLE ROW LEVEL SECURITY;

-- 创建story_segments的RLS策略
CREATE POLICY "任何人都可以查看story_segments"
  ON public.story_segments FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "认证用户可以创建story_segments"
  ON public.story_segments FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = author_id);

CREATE POLICY "作者可以更新自己的story_segments"
  ON public.story_segments FOR UPDATE
  TO authenticated
  USING (auth.uid() = author_id)
  WITH CHECK (auth.uid() = author_id);

-- 创建story_likes的RLS策略
CREATE POLICY "任何人都可以查看story_likes"
  ON public.story_likes FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "认证用户可以创建story_likes"
  ON public.story_likes FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "认证用户可以删除自己的story_likes"
  ON public.story_likes FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id); 