import { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/lib/store/authStore';
import { createStoryWithInitialSegment } from '@/api/stories';

interface UseCreateStoryProps {
  onSuccess?: () => void;
}

export function useCreateStory({ onSuccess }: UseCreateStoryProps = {}) {
  const { t } = useTranslation();
  const router = useRouter();
  const user = useAuthStore((state) => state.user);

  const [title, setTitle] = useState('');
  const [initialContent, setInitialContent] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [contentFocused, setContentFocused] = useState(false);
  const [initialContentIsAI, setInitialContentIsAI] = useState(false);

  const isFormValid =
    title.trim().length > 0 && initialContent.trim().length > 50;

  const handleSubmit = async () => {
    if (!user) {
      Alert.alert(
        t('common.error', '错误'),
        t('auth.signInRequired', '您需要登录才能创建故事。')
      );
      return;
    }
    if (!title.trim()) {
      Alert.alert(
        t('validationError'),
        t('creationErrors.titleRequired', '故事标题是必填的。')
      );
      return;
    }
    if (!initialContent.trim()) {
      Alert.alert(
        t('validationError'),
        t('creationErrors.contentRequired', '初始故事内容是必填的。')
      );
      return;
    }

    setSubmitting(true);

    try {
      const { data: newStoryData, error } = await createStoryWithInitialSegment(
        title.trim(),
        initialContent.trim(),
        'published',
        'public',
        undefined,
        undefined,
        initialContentIsAI
      );

      setSubmitting(false);

      if (error) {
        console.error('Error creating story with initial segment:', error);
        Alert.alert(
          t('common.error', '错误'),
          error?.message ||
            t('storyForm.errors.submitFailed', '创建故事失败，请稍后再试。')
        );
      } else if (newStoryData) {
        console.log('Story created successfully with ID:', newStoryData.id);

        if (newStoryData.id) {
          router.push(`/stories/${newStoryData.id}`);
        } else {
          router.push('/(tabs)/home');
        }

        setTimeout(() => {
          Alert.alert(
            t('common.success', '成功'),
            t('storyForm.success.storyCreated', '故事已成功创建！')
          );
        }, 500);

        setTitle('');
        setInitialContent('');
        setInitialContentIsAI(false);
        
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      console.error('Unexpected error during story submission:', err);
      setSubmitting(false);
      Alert.alert(
        t('common.error', '错误'),
        err.message ||
          t('storyForm.errors.submitFailed', '创建故事失败，请稍后再试。')
      );
    }
  };

  return {
    title,
    setTitle,
    initialContent,
    setInitialContent,
    submitting,
    contentFocused,
    setContentFocused,
    initialContentIsAI,
    setInitialContentIsAI,
    isFormValid,
    handleSubmit,
  };
}
