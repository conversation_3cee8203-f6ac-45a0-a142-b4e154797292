import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './MembershipCard.styles';
import { Crown } from 'lucide-react-native'; // Import Crown icon
import { useTranslation } from 'react-i18next';

interface MembershipCardProps {
  isPremium: boolean;
  onManagePress?: () => void;
}

export function MembershipCard({ isPremium, onManagePress }: MembershipCardProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();

  // Only render the card if the user is premium, or adjust logic as needed
  if (!isPremium) {
    return null; // Or render a different card prompting upgrade
  }

  return (
    <TouchableOpacity style={styles.card} onPress={onManagePress} disabled={!onManagePress}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Crown size={28} color={theme.dark ? '#000' : '#FFF'} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{t('premiumMember')}</Text>
          <Text style={styles.description}>{t('premiumBenefits')}</Text>
        </View>
      </View>
      {onManagePress && (
        <Text style={styles.cta}>{t('manageMembership')}</Text>
      )}
    </TouchableOpacity>
  );
} 