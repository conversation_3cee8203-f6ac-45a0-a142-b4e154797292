import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createDynamicStyles } from '../screens/StoryDetailScreen.styles';

interface StoryDetailErrorProps {
  error: string;
  onRetry: () => void;
}

export function StoryDetailError({ error, onRetry }: StoryDetailErrorProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createDynamicStyles(theme);
  
  return (
    <View style={styles.centered}>
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity onPress={onRetry} style={styles.button}>
        <Text style={styles.buttonText}>{t('tryAgain', 'Try Again')}</Text>
      </TouchableOpacity>
    </View>
  );
}
