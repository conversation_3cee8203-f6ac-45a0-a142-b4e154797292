import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStyles = (theme: AppTheme) => 
  StyleSheet.create({
    actionButtonsContainer: {
      flexDirection: 'row',
      padding: 16,
      justifyContent: 'center',
      gap: 12,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 20,
      flex: 1,
    },
    followButton: {
      backgroundColor: theme.colors.primary,
    },
    followingButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    messageButton: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    actionButtonText: {
      marginLeft: 8,
      fontWeight: '500',
      fontSize: 14,
      color: theme.colors.background,
    },
    followingButtonText: {
      color: theme.colors.primary,
    },
    messageButtonText: {
      color: theme.colors.primary,
    },
  });
