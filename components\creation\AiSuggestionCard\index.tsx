import React from 'react';
import { Card, Text } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';
import { Sparkles } from 'lucide-react-native';

interface AiSuggestionCardProps {
  suggestion: string;
  onSelect: (text: string) => void;
}

export default function AiSuggestionCard({
  suggestion,
  onSelect,
}: AiSuggestionCardProps) {
  const theme = usePaperTheme();

  return (
    <Card
      mode="outlined"
      onPress={() => onSelect(suggestion)}
      style={{
        marginBottom: theme.spacing?.sm || 8,
        backgroundColor: theme.colors.surface,
      }}
    >
      <Card.Content
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: theme.spacing?.sm || 8,
        }}
      >
        <Sparkles
          size={16}
          color={theme.colors.primary}
          style={{ marginRight: theme.spacing?.sm || 8 }}
        />
        <Text variant="bodyMedium" numberOfLines={1} style={{ flex: 1 }}>
          {suggestion}
        </Text>
      </Card.Content>
    </Card>
  );
}
