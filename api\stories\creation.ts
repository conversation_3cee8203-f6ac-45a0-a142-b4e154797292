import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { Story, StorySegment } from './types';

/**
 * Creates a new story and its initial segment.
 * Uses a Supabase Edge Function ('create_story_with_initial_segment') for atomicity.
 */
export async function createStoryWithInitialSegment(
  title: string,
  initialContent: string,
  // userId is implicitly taken from auth.uid() in the Edge Function
  status: Story['status'] = 'published',
  visibility: Story['visibility'] = 'public',
  tags?: string[] | null,
  coverImageUrl?: string | null,
  isInitialContentAIMediated?: boolean // New parameter
): Promise<{ data: Story | null; error: PostgrestError | Error | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return { data: null, error: new Error('User not authenticated') };

  // For client-side only version (if not using Edge Function):
  // This is kept for reference or if Edge Function approach is reverted.
  // In a production app, an Edge Function is strongly recommended for atomicity.

  // 1. Create the story
  const { data: storyData, error: storyError } = await supabase
    .from('stories')
    .insert({
      author_id: user.id,
      title,
      status,
      visibility,
      tags,
      cover_image_url: coverImageUrl,
    })
    .select(
      `
        id, author_id, title, status, visibility, tags, cover_image_url, created_at, updated_at,
        profiles!stories_author_id_fkey ( id, username, avatar_url )
    `
    )
    .single();

  if (storyError || !storyData) {
    console.error('Error creating story:', storyError);
    return {
      data: null,
      error: storyError || new Error('Failed to create story metadata.'),
    };
  }

  // 2. Create the initial segment
  const { data: segmentData, error: segmentError } = await supabase
    .from('story_segments')
    .insert({
      story_id: storyData.id,
      author_id: user.id,
      content: initialContent,
      content_type: 'text', // Default to text for MVP
      order_in_branch: 0,
      is_ai_generated: isInitialContentAIMediated ?? false, // Set is_ai_generated
    })
    .select() // Select the created segment
    .single();

  if (segmentError) {
    console.error('Error creating initial segment:', segmentError);
    // Attempt to delete the story if segment creation fails (manual rollback)
    await supabase.from('stories').delete().eq('id', storyData.id);
    return { data: null, error: segmentError };
  }

  // @ts-ignore
  return { data: storyData, error: null };
}

/**
 * Adds a new segment to an existing story (接龙).
 */
export async function addStorySegment(
  storyId: string,
  content: string,
  // userId is implicitly taken from auth.uid()
  contentType: StorySegment['content_type'] = 'text',
  parentSegmentId?: string, // For branching
  isAiGenerated?: boolean // New parameter
): Promise<{ data: StorySegment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };

  const { data, error } = await supabase
    .from('story_segments')
    .insert({
      story_id: storyId,
      author_id: user.id,
      content,
      content_type: contentType,
      parent_segment_id: parentSegmentId,
      is_ai_generated: isAiGenerated ?? false, // Set is_ai_generated
      // order_in_branch might need more logic if we want to re-order siblings,
      // for now, relying on created_at for chronological order under a parent.
    })
    .select(
      `
        id, story_id, author_id, content_type, content, parent_segment_id, order_in_branch, is_ai_generated, created_at, updated_at,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
    `
    )
    .single();

  if (error) {
    console.error('Error adding story segment:', error);
  }
  // @ts-ignore
  return { data, error };
}
