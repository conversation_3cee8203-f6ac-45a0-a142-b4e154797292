import React from 'react';
import { View } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { User } from '@/types/user';
import UserCard from '@/components/social/UserCard';
import { createStyles } from './styles';

interface UsersResultsProps {
  users: User[];
  onUserPress: (userId: string) => void;
  limit?: number;
}

export function UsersResults({ 
  users, 
  onUserPress,
  limit
}: UsersResultsProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  const displayUsers = limit ? users.slice(0, limit) : users;
  
  return (
    <View style={styles.usersGrid}>
      {displayUsers.map((user) => (
        <UserCard
          key={user.id}
          user={user}
          onPress={onUserPress}
          onFollow={() => {}} // 暂不实现关注功能
          isFollowing={false}
        />
      ))}
    </View>
  );
}
