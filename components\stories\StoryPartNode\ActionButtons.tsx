import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { createStyles } from './styles';
import { useAppTheme } from '@/hooks/useAppTheme';

interface ActionButtonsProps {
  isLoggedIn: boolean;
  showContinueInput: boolean;
  onToggleContinueInput: () => void;
}

export default function ActionButtons({
  isLoggedIn,
  showContinueInput,
  onToggleContinueInput,
}: ActionButtonsProps) {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.actionsContainer}>
      {/* Placeholder for Like Button */}
      <TouchableOpacity style={styles.actionButton}>
        <Text style={styles.actionText}>Like (TODO)</Text>
      </TouchableOpacity>

      {/* Placeholder for Comment Button */}
      <TouchableOpacity style={styles.actionButton}>
        <Text style={styles.actionText}>Comment (TODO)</Text>
      </TouchableOpacity>

      {/* Continue Button */}
      {isLoggedIn && (
        <TouchableOpacity
          style={styles.actionButton}
          onPress={onToggleContinueInput}
        >
          <Text style={styles.actionText}>
            {showContinueInput
              ? t('story.cancelContinue', 'Cancel')
              : t('story.continue', 'Continue Story...')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
