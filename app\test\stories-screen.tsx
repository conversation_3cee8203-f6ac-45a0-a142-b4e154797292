import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { StoryTabs } from '@/features/stories/components/StoryTabs';
import { StoryList } from '@/features/stories/components/StoryList';
import { useStoriesScreenTest, TestScenario } from '@/features/stories/hooks/useStoriesScreenTest';
import { useRouter } from 'expo-router';

/**
 * Test screen for the stories screen functionality
 * This screen allows testing different scenarios for the stories screen
 */
export default function StoriesScreenTest() {
  const theme = useAppTheme();
  const { t } = useTranslation();
  const router = useRouter();
  
  // Use the test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
    setTestScenario,
    currentScenario,
  } = useStoriesScreenTest();
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  // Create styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.cardBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    scenariosContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.md,
    },
    scenarioButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 8,
      borderWidth: 1,
    },
    activeScenarioButton: {
      backgroundColor: theme.colors.primary,
    },
    scenarioButtonText: {
      fontSize: 12,
    },
    activeScenarioButtonText: {
      color: theme.colors.onPrimary,
    },
    contentContainer: {
      flex: 1,
    },
    backButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      alignItems: 'center',
    },
    backButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
  });
  
  // Render a scenario button
  const renderScenarioButton = (scenario: TestScenario, label: string) => {
    const isActive = currentScenario === scenario;
    
    return (
      <TouchableOpacity
        style={[
          styles.scenarioButton,
          {
            borderColor: isActive ? theme.colors.primary : theme.colors.border,
          },
          isActive && styles.activeScenarioButton,
        ]}
        onPress={() => setTestScenario(scenario)}
      >
        <Text
          style={[
            styles.scenarioButtonText,
            {
              color: isActive ? theme.colors.onPrimary : theme.colors.text,
            },
            isActive && styles.activeScenarioButtonText,
          ]}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Stories Screen Test</Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.scenariosContainer}>
            {renderScenarioButton(TestScenario.SUCCESS, 'Success')}
            {renderScenarioButton(TestScenario.EMPTY, 'Empty')}
            {renderScenarioButton(TestScenario.LOADING, 'Loading')}
            {renderScenarioButton(TestScenario.ERROR_NETWORK, 'Network Error')}
            {renderScenarioButton(TestScenario.ERROR_SERVER, 'Server Error')}
            {renderScenarioButton(TestScenario.ERROR_AUTH, 'Auth Error')}
            {renderScenarioButton(TestScenario.LARGE_DATASET, 'Large Dataset')}
          </View>
        </ScrollView>
        
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Back to App</Text>
        </TouchableOpacity>
      </View>
      
      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
      
      <View style={styles.contentContainer}>
        <StoryList
          stories={stories}
          onStoryPress={handleStoryPress}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          onRefresh={refreshStories}
          onLoadMore={loadMoreStories}
          hasMoreStories={hasMoreStories}
          error={error}
          onRetry={retryFetch}
          retryCount={retryCount}
        />
      </View>
    </SafeAreaView>
  );
}
