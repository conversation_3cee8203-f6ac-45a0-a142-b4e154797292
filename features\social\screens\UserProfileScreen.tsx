import React from 'react';
import { ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './UserProfileScreen.styles';

// Custom hooks
import { useUserProfile } from '../hooks/useUserProfile';
import { useUserStories } from '../hooks/useUserStories';

// Components
import UserProfileHeader from '../components/UserProfileHeader';
import UserBio from '../components/UserBio';
import UserStats from '../components/UserStats';
import UserActionButtons from '../components/UserActionButtons';
import UserStories from '../components/UserStories';
import { LoadingState, ErrorState } from '../components/LoadingAndErrorStates';

interface UserProfileScreenProps {
  userId: string;
}

export default function UserProfileScreen({ userId }: UserProfileScreenProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  // 使用自定义 hook 获取用户资料
  const {
    user,
    isLoading,
    error,
    isFollowing,
    isFollowingInProgress,
    handleFollowToggle,
    handleSendMessage,
  } = useUserProfile({ userId });

  // 使用自定义 hook 获取用户故事
  const { userStories } = useUserStories({
    userId,
    user,
  });

  // 处理故事点击
  const handleStoryPress = (storyId: string) => {
    router.push(`/stories/${storyId}`);
  };

  // 显示加载状态
  if (isLoading) {
    return <LoadingState isLoading={isLoading} />;
  }

  // 显示错误状态
  if (error || !user) {
    return <ErrorState error={error} />;
  }

  return (
    <ScrollView style={styles.container}>
      {/* 用户头像和名称 */}
      <UserProfileHeader user={user} />

      {/* 用户简介 */}
      <UserBio bio={user.bio} />

      {/* 用户统计信息 */}
      <UserStats
        followers={user.followers}
        following={user.following}
        storiesCount={user.storiesCount}
      />

      {/* 操作按钮 */}
      <UserActionButtons
        isFollowing={isFollowing}
        isFollowingInProgress={isFollowingInProgress}
        onFollowToggle={handleFollowToggle}
        onSendMessage={handleSendMessage}
      />

      {/* 用户故事列表 */}
      <UserStories
        stories={userStories}
        userName={user.displayName}
        onStoryPress={handleStoryPress}
      />
    </ScrollView>
  );
}
