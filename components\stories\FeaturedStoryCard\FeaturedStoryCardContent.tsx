import React from 'react';
import { View } from 'react-native';
import { Text, Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface FeaturedStoryCardContentProps {
  summary: string;
  themeTags: string[];
}

export function FeaturedStoryCardContent({
  summary,
  themeTags,
}: FeaturedStoryCardContentProps) {
  const theme = usePaperTheme();

  return (
    <View>
      <Text
        variant="bodyMedium"
        style={{
          color: '#FFFFFF',
          marginBottom: theme.spacing?.sm || 8,
        }}
        numberOfLines={2}
      >
        {summary}
      </Text>

      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          gap: theme.spacing?.xs || 4,
        }}
      >
        {themeTags.map((tag) => (
          <Chip
            key={tag}
            mode="flat"
            compact
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
            }}
            textStyle={{
              color: '#FFFFFF',
              fontSize: 10,
            }}
          >
            {tag}
          </Chip>
        ))}
      </View>
    </View>
  );
}
