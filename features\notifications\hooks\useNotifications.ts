import { useState, useEffect, useCallback } from 'react';
import { 
  getNotifications, 
  markNotificationAsRead, 
  markAllNotificationsAsRead, 
  deleteNotification,
  getUnreadNotificationCount
} from '@/api/notifications';
import { Notification, NotificationType } from '@/api/notifications/types';

interface UseNotificationsProps {
  initialLimit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useNotifications({
  initialLimit = 20,
  autoRefresh = false,
  refreshInterval = 60000, // 1分钟
}: UseNotificationsProps = {}) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [limit] = useState(initialLimit);
  const [filterType, setFilterType] = useState<NotificationType | null>(null);
  const [filterRead, setFilterRead] = useState<boolean | null>(null);

  // 获取通知列表
  const fetchNotifications = useCallback(async (reset = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const newOffset = reset ? 0 : offset;
      const options = {
        limit,
        offset: newOffset,
        ...(filterType ? { types: [filterType] } : {}),
        ...(filterRead !== null ? { is_read: filterRead } : {}),
      };

      const { data, error } = await getNotifications(options);

      if (error) {
        throw new Error(error.message);
      }

      if (data) {
        if (reset) {
          setNotifications(data);
        } else {
          setNotifications((prev) => [...prev, ...data]);
        }
        setHasMore(data.length === limit);
        setOffset(reset ? limit : newOffset + limit);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch notifications'));
    } finally {
      setIsLoading(false);
    }
  }, [offset, limit, filterType, filterRead]);

  // 获取未读通知数量
  const fetchUnreadCount = useCallback(async () => {
    try {
      const { count, error } = await getUnreadNotificationCount();
      if (error) {
        throw new Error(error.message);
      }
      setUnreadCount(count);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  }, []);

  // 标记通知为已读
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const { success, error } = await markNotificationAsRead(notificationId);
      if (error) {
        throw new Error(error.message);
      }
      if (success) {
        setNotifications((prev) =>
          prev.map((notification) =>
            notification.id === notificationId
              ? { ...notification, is_read: true }
              : notification
          )
        );
        fetchUnreadCount();
      }
      return success;
    } catch (err) {
      console.error('Error marking notification as read:', err);
      return false;
    }
  }, [fetchUnreadCount]);

  // 标记所有通知为已读
  const markAllAsRead = useCallback(async () => {
    try {
      const { success, error } = await markAllNotificationsAsRead();
      if (error) {
        throw new Error(error.message);
      }
      if (success) {
        setNotifications((prev) =>
          prev.map((notification) => ({ ...notification, is_read: true }))
        );
        setUnreadCount(0);
      }
      return success;
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      return false;
    }
  }, []);

  // 删除通知
  const removeNotification = useCallback(async (notificationId: string) => {
    try {
      const { success, error } = await deleteNotification(notificationId);
      if (error) {
        throw new Error(error.message);
      }
      if (success) {
        setNotifications((prev) =>
          prev.filter((notification) => notification.id !== notificationId)
        );
        // 如果删除的是未读通知，更新未读数量
        const deletedNotification = notifications.find(
          (notification) => notification.id === notificationId
        );
        if (deletedNotification && !deletedNotification.is_read) {
          setUnreadCount((prev) => Math.max(0, prev - 1));
        }
      }
      return success;
    } catch (err) {
      console.error('Error deleting notification:', err);
      return false;
    }
  }, [notifications]);

  // 刷新通知列表
  const refreshNotifications = useCallback(() => {
    fetchNotifications(true);
    fetchUnreadCount();
  }, [fetchNotifications, fetchUnreadCount]);

  // 加载更多通知
  const loadMoreNotifications = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchNotifications();
    }
  }, [isLoading, hasMore, fetchNotifications]);

  // 筛选通知
  const filterNotifications = useCallback((type: NotificationType | null, read: boolean | null) => {
    setFilterType(type);
    setFilterRead(read);
    setOffset(0);
    setHasMore(true);
  }, []);

  // 初始加载
  useEffect(() => {
    fetchNotifications(true);
    fetchUnreadCount();
  }, [fetchNotifications, fetchUnreadCount]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const intervalId = setInterval(() => {
        fetchUnreadCount();
      }, refreshInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoRefresh, refreshInterval, fetchUnreadCount]);

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    hasMore,
    markAsRead,
    markAllAsRead,
    removeNotification,
    refreshNotifications,
    loadMoreNotifications,
    filterNotifications,
    filterType,
    filterRead,
  };
}