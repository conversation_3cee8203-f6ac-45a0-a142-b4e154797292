import React from 'react';
import { View } from 'react-native';
import { Text, Chip } from 'react-native-paper';
import { usePaperTheme } from '@/hooks/usePaperTheme';

interface FeaturedStoryCardHeaderProps {
  title: string;
  isPremium: boolean;
}

export function FeaturedStoryCardHeader({
  title,
  isPremium,
}: FeaturedStoryCardHeaderProps) {
  const theme = usePaperTheme();

  return (
    <View>
      {isPremium && (
        <Chip
          mode="flat"
          icon="crown"
          compact
          style={{
            backgroundColor: theme.colors.tertiary,
            alignSelf: 'flex-start',
            marginBottom: theme.spacing?.xs || 4,
          }}
          textStyle={{
            color: theme.colors.onTertiary,
            fontSize: 10,
          }}
        >
          会员
        </Chip>
      )}
      <Text
        variant="headlineSmall"
        style={{
          color: '#FFFFFF',
          fontWeight: 'bold',
        }}
      >
        {title}
      </Text>
    </View>
  );
}
