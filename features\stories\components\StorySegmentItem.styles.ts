import { StyleSheet } from 'react-native';
import { AppTheme } from '@/lib/theme/themes';

export const createStorySegmentItemStyles = (theme: AppTheme) => {
  return StyleSheet.create({
    segmentCard: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.md,
      borderRadius: theme.radius.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden',
    },
    firstSegmentCard: {
      // Style for the very first segment in a list
      marginTop: theme.spacing.sm, // Add some top margin if it's the first
      borderLeftWidth: 3,
      borderLeftColor: theme.colors.primary,
    },
    lastSegmentCard: {
      // Style for the very last segment in a list
      marginBottom: 0, // No bottom margin if it's the last
      // borderLeftWidth: 3, // Example: different styling for last item if needed
      // borderLeftColor: theme.colors.secondaryText, // Example
    },
    segmentDivider: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginVertical: theme.spacing.sm,
      width: '90%', // Slightly less than full width for a refined look
      alignSelf: 'center',
    },
    segmentContent: {
      fontSize: theme.fontSizes.md,
      fontFamily: theme.fonts.regular,
      color: theme.colors.text,
      lineHeight: theme.lineHeights.md, // Assuming lineHeight is part of theme
      marginBottom: theme.spacing.sm,
    },
    segmentFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: theme.spacing.sm,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: theme.spacing.sm,
    },
    segmentAuthor: {
      fontSize: theme.fontSizes.sm, // Slightly larger for author
      fontFamily: theme.fonts.medium,
      color: theme.colors.primary,
    },
    segmentTimestamp: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.regular,
      color: theme.colors.secondaryText,
    },
    aiIndicator: {
      fontSize: theme.fontSizes.xs,
      fontFamily: theme.fonts.italic,
      color: theme.colors.accent2, // Assuming accent2 for AI indication
      marginTop: theme.spacing.xs,
    },
  });
};
