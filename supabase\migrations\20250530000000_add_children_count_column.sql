-- Add children_count column to story_segments table
ALTER TABLE IF EXISTS public.story_segments
ADD COLUMN IF NOT EXISTS children_count integer DEFAULT 0;

-- <PERSON>reate function to update children_count
CREATE OR REPLACE FUNCTION update_segment_children_count()
RETURNS TRIGGER AS $$
BEGIN
  -- When a new segment is inserted, increment the parent's children_count
  IF TG_OP = 'INSERT' AND NEW.parent_segment_id IS NOT NULL THEN
    UPDATE story_segments
    SET children_count = children_count + 1
    WHERE id = NEW.parent_segment_id;
  -- When a segment is deleted, decrement the parent's children_count
  ELSIF TG_OP = 'DELETE' AND OLD.parent_segment_id IS NOT NULL THEN
    UPDATE story_segments
    SET children_count = children_count - 1
    WHERE id = OLD.parent_segment_id;
  -- When a segment's parent_segment_id is updated
  ELSIF TG_OP = 'UPDATE' AND (
    (OLD.parent_segment_id IS NULL AND NEW.parent_segment_id IS NOT NULL) OR
    (OLD.parent_segment_id IS NOT NULL AND NEW.parent_segment_id IS NULL) OR
    (OLD.parent_segment_id IS NOT NULL AND NEW.parent_segment_id IS NOT NULL AND OLD.parent_segment_id != NEW.parent_segment_id)
  ) THEN
    -- Decrement old parent's count if it exists
    IF OLD.parent_segment_id IS NOT NULL THEN
      UPDATE story_segments
      SET children_count = children_count - 1
      WHERE id = OLD.parent_segment_id;
    END IF;
    -- Increment new parent's count if it exists
    IF NEW.parent_segment_id IS NOT NULL THEN
      UPDATE story_segments
      SET children_count = children_count + 1
      WHERE id = NEW.parent_segment_id;
    END IF;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for story_segments
DROP TRIGGER IF EXISTS trigger_update_segment_children_count ON story_segments;
CREATE TRIGGER trigger_update_segment_children_count
AFTER INSERT OR UPDATE OR DELETE ON story_segments
FOR EACH ROW
EXECUTE FUNCTION update_segment_children_count();

-- Update existing children_count values
-- This will set the correct children_count for all existing segments
UPDATE story_segments s
SET children_count = (
  SELECT COUNT(*)
  FROM story_segments
  WHERE parent_segment_id = s.id
);
