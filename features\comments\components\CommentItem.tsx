import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from './CommentItem.styles';
import { Comment, deleteStoryComment, updateStoryComment } from '@/api/comments';
import { useTranslation } from 'react-i18next';
import { Avatar } from '@/components/ui/Avatar';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/lib/store/authStore';
import { Ionicons } from '@expo/vector-icons';
import { TextInput } from 'react-native-gesture-handler';

interface CommentItemProps {
  comment: Comment;
  onReply: (commentId: string) => void;
  onViewReplies: (commentId: string) => void;
  onCommentDeleted: (commentId: string) => void;
  onCommentUpdated: (comment: Comment) => void;
}

export function CommentItem({
  comment,
  onReply,
  onViewReplies,
  onCommentDeleted,
  onCommentUpdated,
}: CommentItemProps) {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const router = useRouter();
  const currentUser = useAuthStore((state) => state.user);
  
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(comment.content);
  const [isUpdating, setIsUpdating] = useState(false);
  
  const isOwnComment = currentUser?.id === comment.user_id;
  const hasReplies = (comment.reply_count || 0) > 0;
  
  const formattedDate = comment.created_at
    ? formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })
    : '';
  
  const handleUserPress = () => {
    if (comment.profiles?.id) {
      router.push(`/profile/${comment.profiles.id}`);
    }
  };
  
  const handleReplyPress = () => {
    onReply(comment.id);
  };
  
  const handleViewRepliesPress = () => {
    onViewReplies(comment.id);
  };
  
  const handleEditPress = () => {
    setIsEditing(true);
  };
  
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedContent(comment.content);
  };
  
  const handleSaveEdit = async () => {
    if (editedContent.trim() === '') {
      Alert.alert(
        t('error', '错误'),
        t('comments.emptyCommentError', '评论内容不能为空')
      );
      return;
    }
    
    setIsUpdating(true);
    
    try {
      const { data, error } = await updateStoryComment(comment.id, editedContent);
      
      if (error) throw error;
      
      if (data) {
        onCommentUpdated(data);
        setIsEditing(false);
      }
    } catch (err) {
      console.error('Failed to update comment:', err);
      Alert.alert(
        t('error', '错误'),
        t('comments.updateError', '更新评论失败，请重试')
      );
    } finally {
      setIsUpdating(false);
    }
  };
  
  const handleDeletePress = () => {
    Alert.alert(
      t('comments.deleteConfirmTitle', '删除评论'),
      t('comments.deleteConfirmMessage', '确定要删除这条评论吗？此操作无法撤销。'),
      [
        {
          text: t('cancel', '取消'),
          style: 'cancel',
        },
        {
          text: t('delete', '删除'),
          style: 'destructive',
          onPress: handleConfirmDelete,
        },
      ]
    );
  };
  
  const handleConfirmDelete = async () => {
    setIsDeleting(true);
    
    try {
      const { success, error } = await deleteStoryComment(comment.id);
      
      if (error) throw error;
      
      if (success) {
        onCommentDeleted(comment.id);
      }
    } catch (err) {
      console.error('Failed to delete comment:', err);
      Alert.alert(
        t('error', '错误'),
        t('comments.deleteError', '删除评论失败，请重试')
      );
    } finally {
      setIsDeleting(false);
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleUserPress}>
          <Avatar
            size={40}
            uri={comment.profiles?.avatar_url || ''}
            username={comment.profiles?.username || ''}
          />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <TouchableOpacity onPress={handleUserPress}>
            <Text style={styles.username}>{comment.profiles?.username || t('comments.unknownUser', '未知用户')}</Text>
          </TouchableOpacity>
          <Text style={styles.date}>{formattedDate}</Text>
        </View>
        
        {isOwnComment && !isEditing && (
          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleEditPress}
              disabled={isDeleting}
            >
              <Ionicons name="pencil-outline" size={18} color={theme.colors.secondaryText} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleDeletePress}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <ActivityIndicator size="small" color={theme.colors.error} />
              ) : (
                <Ionicons name="trash-outline" size={18} color={theme.colors.error} />
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
      
      {isEditing ? (
        <View style={styles.editContainer}>
          <TextInput
            style={styles.editInput}
            value={editedContent}
            onChangeText={setEditedContent}
            multiline
            autoFocus
            maxLength={1000}
          />
          
          <View style={styles.editActions}>
            <TouchableOpacity
              style={styles.editButton}
              onPress={handleCancelEdit}
              disabled={isUpdating}
            >
              <Text style={styles.cancelText}>{t('cancel', '取消')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.editButton, styles.saveButton]}
              onPress={handleSaveEdit}
              disabled={isUpdating}
            >
              {isUpdating ? (
                <ActivityIndicator size="small" color={theme.colors.white} />
              ) : (
                <Text style={styles.saveText}>{t('save', '保存')}</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <Text style={styles.content}>{comment.content}</Text>
      )}
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.footerButton}
          onPress={handleReplyPress}
        >
          <Text style={styles.footerButtonText}>{t('comments.reply', '回复')}</Text>
        </TouchableOpacity>
        
        {hasReplies && (
          <TouchableOpacity
            style={styles.footerButton}
            onPress={handleViewRepliesPress}
          >
            <Text style={styles.footerButtonText}>
              {t('comments.viewReplies', '查看回复 ({{count}})', { count: comment.reply_count })}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}