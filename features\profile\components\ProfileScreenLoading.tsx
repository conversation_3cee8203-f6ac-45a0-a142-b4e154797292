import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { createStyles } from '../screens/ProfileScreen.styles';

export function ProfileScreenLoading() {
  const theme = useAppTheme();
  const styles = createStyles(theme);
  
  return (
    <View style={styles.container_centered}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
  );
}
